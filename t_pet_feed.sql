CREATE TABLE `ustar`.`t_pet_feed` (
  `id` int(11) NOT NULL AUTO_INCREMENT COMMENT 'id',
  `pet_name` varchar(100) NOT NULL DEFAULT '' COMMENT '宠物名字,为空表示没有取名字，取了名字不能再更改',
  `pet_type` int(11) NOT NULL DEFAULT 0 COMMENT '宠物类型 1 猫咪 2 猎豹 3 狮子',
  `pet_status` int(11) NOT NULL DEFAULT 0 COMMENT '1 正常领养 2 放养',
  `eat_total_food` int(11) NOT NULL DEFAULT 0 COMMENT '已喂养的食物总数量',
  `uid` varchar(50) NOT NULL COMMENT '宠物领养用户，uid为唯一键',
  `food` int(11) NOT NULL DEFAULT 0 COMMENT '领养用户仓库食物总数量',
  `toys` int(11) NOT NULL DEFAULT 0 COMMENT '领养用户仓库玩具总数量',
  `ctime` int(11) NOT NULL DEFAULT 0 COMMENT '创建时间',
  `mtime` int(11) NOT NULL DEFAULT 0 COMMENT '修改时间',
  PRIMARY KEY (`id`),
  UNIQUE KEY `uk_uid` (`uid`) COMMENT 'uid唯一键索引'
) ENGINE = InnoDB AUTO_INCREMENT = 1 CHARACTER SET = utf8mb4 COLLATE = utf8mb4_0900_ai_ci COMMENT='宠物活动喂养表';
