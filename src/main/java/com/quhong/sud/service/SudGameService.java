package com.quhong.sud.service;

import com.alibaba.fastjson.JSONObject;
import com.quhong.analysis.EventReport;
import com.quhong.analysis.GameRecordLogEvent;
import com.quhong.config.SudGameConfig;
import com.quhong.constant.AchieveBadgeConstant;
import com.quhong.constant.MoneyTypeConstant;
import com.quhong.core.concurrency.BaseTaskFactory;
import com.quhong.core.concurrency.tasks.Task;
import com.quhong.core.utils.DateHelper;
import com.quhong.dailyTask.CommonTaskService;
import com.quhong.data.ActorData;
import com.quhong.data.BalootGameExtras;
import com.quhong.data.CommonMqTopicData;
import com.quhong.data.dto.MoneyDetailReq;
import com.quhong.data.dto.MoneyTypeDTO;
import com.quhong.data.dto.MuteMicDTO;
import com.quhong.enums.ApiResult;
import com.quhong.enums.CommonMqTaskConstant;
import com.quhong.enums.RetCodeEnum;
import com.quhong.enums.SudGameConstant;
import com.quhong.feign.DataCenterService;
import com.quhong.feign.IRoomService;
import com.quhong.image.ImageUrlGenerator;
import com.quhong.mongo.dao.ActorConfigDao;
import com.quhong.mongo.dao.ActorDao;
import com.quhong.mongo.dao.SudGameDao;
import com.quhong.mongo.data.SudGameData;
import com.quhong.mongo.data.SudGamePlayerData;
import com.quhong.mq.MqSenderService;
import com.quhong.msg.obj.RoomMicInfoObject;
import com.quhong.msg.obj.SudGameResultInfoObject;
import com.quhong.msg.room.SudGameResultMsg;
import com.quhong.mysql.dao.GameRoomUserCareerDao;
import com.quhong.mysql.dao.HeartRecordDao;
import com.quhong.mysql.dao.VipInfoDao;
import com.quhong.mysql.data.GameRoomUserCareerData;
import com.quhong.redis.OperationConfigRedis;
import com.quhong.redis.SudGameRedis;
import com.quhong.service.BadgeService;
import com.quhong.sud.data.SudGameConfigInfo;
import com.quhong.sud.dto.ReportGameInfoDTO;
import com.quhong.sud.vo.*;
import com.quhong.utils.RoomUtils;
import com.quhong.vo.RoomMicListVo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.ObjectUtils;
import tech.sud.mgp.auth.api.SudCode;
import tech.sud.mgp.auth.api.SudMGPAuth;
import tech.sud.mgp.auth.api.SudSSToken;
import tech.sud.mgp.auth.api.SudUid;

import javax.annotation.Resource;
import java.util.*;
import java.util.stream.Collectors;

/**
 * 即构小游戏
 *
 * <AUTHOR>
 * @date 2022/6/29
 */
@Service
public class SudGameService {

    private static final Logger logger = LoggerFactory.getLogger(SudGameService.class);
    Comparator<SudGameData.GameResult> awardValueDesc = Comparator.comparing(SudGameData.GameResult::getAwardValue).reversed();
    Comparator<SudGameData.GameResult> isEscapedAsc = Comparator.comparing(SudGameData.GameResult::getIsEscaped);
    Comparator<SudGameData.GameResult> scoreDesc = Comparator.comparing(SudGameData.GameResult::getScore).reversed();
    private static final List<Integer> MORE_MVP_GAME_TYPE_LIST = Arrays.asList(SudGameConstant.JACKAROO_GAME, SudGameConstant.BALOOT_GAME);
    private static final int COIN = 1;
    private static final int DIAMOND = 2;
    BaseVO<?> baseResp = new BaseVO<>();

    @Resource
    private SudMGPAuth sudMGPAuth;
    @Resource
    private ActorDao actorDao;
    @Resource
    private SudService sudService;
    @Resource
    private SudGameDao sudGameDao;
    @Resource
    private HeartRecordDao heartRecordDao;
    @Resource
    private EventReport eventReport;
    @Resource
    private DataCenterService dataCenterService;
    @Resource
    private VipInfoDao vipInfoDao;
    @Resource
    private ActorConfigDao actorConfigDao;
    @Resource
    private BadgeService badgeService;
    @Resource
    protected MqSenderService mqService;
    @Resource
    private SudGameConfig sudGameConfig;
    @Resource
    private CommonTaskService commonTaskService;
    @Resource
    private SudGameRedis sudGameRedis;
    @Resource
    private OperationConfigRedis operationConfigRedis;
    @Resource
    private SudGameRoomService sudGameRoomService;
    @Resource
    private GameRoomUserCareerDao gameRoomUserCareerDao;
    @Resource
    private IRoomService iRoomService;

    /**
     * 登录（GetCode）
     *
     * @param userId 用户ID
     * @return 登录响应
     */
    public BaseVO<LoginVO> login(String userId) {
        BaseVO<LoginVO> baseResp = new BaseVO<>();

        // 生成短期令牌Code, 不传expireDuration参数则使用SDK中默认两小时过期
        SudCode sudCode = sudMGPAuth.getCode(userId);

        // 返回响应结果
        baseResp.setData(LoginVO.builder()
                .code(sudCode.getCode())
                .expireDate(sudCode.getExpireDate())
                .build());
        return baseResp;
    }

    /**
     * 获取长期令牌SSToken(根据短期令牌Code更换长期令牌SSToken)
     *
     * @param code 短期令牌
     * @return 长期令牌SSToken响应
     */
    public BaseVO<GetSSTokenVO> getSSToken(String code) {
        BaseVO<GetSSTokenVO> baseResp = new BaseVO<>();
        SudUid sudUid = sudMGPAuth.getUidByCode(code);
        if (!sudUid.isSuccess()) {
            baseResp.setRetCodeByEnum(RetCodeEnum.REQUEST_FAILED);
            baseResp.setSdkErrorCode(sudUid.getErrorCode());
            return baseResp;
        }
        SudSSToken sudSSToken = sudMGPAuth.getSSToken(sudUid.getUid());
        ActorData actorData = actorDao.getActorDataFromCache(sudUid.getUid());
        baseResp.setData(GetSSTokenVO.builder()
                .ssToken(sudSSToken.getToken())
                .expireDate(sudSSToken.getExpireDate())
                .userInfo(actorData == null ? null : GetUserInfoVO.builder()
                        .uid(sudUid.getUid())
                        .isAi(actorData.getRobot() == 1 ? 1 : 0)
                        .aiLevel(3)
                        .nickName(actorData.getName())
                        .gender(actorData.getFb_gender() == 1 ? "male" : "female,")
                        .avatarUrl(ImageUrlGenerator.generateSudGameUserUrl(actorData.getHead(), vipInfoDao.getIntVipLevelFromCache(actorData.getUid())))
                        .build())
                .build());
        return baseResp;
    }

    /**
     * 更新长期令牌SSToken(根据当前的长期令牌SSToken更换新的SSToken)
     *
     * @param ssToken 长期令牌
     * @return 长期令牌SSToken响应
     */
    public BaseVO<UpdateSSTokenVO> updateSSToken(String ssToken) {
        BaseVO<UpdateSSTokenVO> baseResp = new BaseVO<>();
        SudUid sudUid = sudMGPAuth.getUidBySSToken(ssToken);
        if (!sudUid.isSuccess()) {
            baseResp.setRetCodeByEnum(RetCodeEnum.REQUEST_FAILED);
            baseResp.setSdkErrorCode(sudUid.getErrorCode());
            return baseResp;
        }

        SudSSToken sudSSToken = sudMGPAuth.getSSToken(sudUid.getUid());
        baseResp.setData(UpdateSSTokenVO.builder()
                .ssToken(sudSSToken.getToken())
                .expireDate(sudSSToken.getExpireDate())
                .build());
        return baseResp;
    }

    /**
     * 获取用户信息
     *
     * @param ssToken 长期令牌SSToken
     * @return 用户信息响应
     */
    public BaseVO<GetUserInfoVO> getUserInfo(String ssToken) {
        BaseVO<GetUserInfoVO> baseResp = new BaseVO<>();
        SudUid sudUid = sudMGPAuth.getUidBySSToken(ssToken);
        logger.info("get user info. uid={}", sudUid);
        if (!sudUid.isSuccess()) {
            baseResp.setRetCodeByEnum(RetCodeEnum.REQUEST_FAILED);
            baseResp.setSdkErrorCode(sudUid.getErrorCode());
            return baseResp;
        }
        ActorData actorData = actorDao.getActorDataFromCache(sudUid.getUid());
        if (actorData == null) {
            baseResp.setRetCodeByEnum(RetCodeEnum.NOT_FIND_USER_INFO);
            return baseResp;
        }
        baseResp.setData(GetUserInfoVO.builder()
                .uid(sudUid.getUid())
                .nickName(actorData.getName())
                .gender(actorData.getFb_gender() == 1 ? "male" : "female,")
                .avatarUrl(ImageUrlGenerator.generateSudGameUserUrl(actorData.getHead(), vipInfoDao.getIntVipLevelFromCache(actorData.getUid())))
                .build());
        return baseResp;
    }

    public BaseVO<?> reportGameInfo(ReportGameInfoDTO reqParam) {
        ReportGameInfoDTO.ReportMsg reportMsg = reqParam.getReportMsg();
        String roomId = reportMsg.getRoomId();
        String gameId = reportMsg.getReportGameInfoKey();
        SudGameData gameData = sudGameDao.findData(gameId);
        if (null == gameData) {
            logger.error("sud report game info, cannot find game data. roomId={} gameId={} gameRoundId={}", roomId, gameId, reportMsg.getGameRoundId());
            return baseResp;
        }
        if (Objects.equals(reqParam.getReportType(), SudGameConstant.GAME_START_REPORT_TYPE)) {
            // 游戏开始
            logger.info("start sud game. roomId={} reqParam={}", roomId, JSONObject.toJSONString(reqParam));
        } else {
            if (SudGameConstant.GAME_PROCESSING != gameData.getStatus()) {
                logger.error("game report info error. gameId={} gameStatus={}", gameId, gameData.getStatus());
                return baseResp;
            }
            // 游戏结束
            logger.info("end sud game. roomId={} reqParam={}", roomId, JSONObject.toJSONString(reqParam));
            if (reportMsg.getGameRoundId().equals(gameData.getGameRoundId())) {
                logger.error("repeat report game game settle, roomId={} gameRoundId={}", roomId, reportMsg.getGameRoundId());
                return baseResp;
            }
            List<SudGameData.GameResult> gameResultList = new ArrayList<>();
            List<String> uidList = Collections.emptyList();
            if (!CollectionUtils.isEmpty(gameData.getPlayerList())) {
                uidList = gameData.getPlayerList().stream().map(SudGamePlayerData::getUid).collect(Collectors.toList());
            }
            for (ReportGameInfoDTO.GameResult result : reportMsg.getResults()) {
                SudGameData.GameResult gameResult = new SudGameData.GameResult();
                BeanUtils.copyProperties(result, gameResult);
                if (gameResult.getIsWin() == 2 || gameResult.getRank() == 1) {
                    gameResult.setJoinRank(1000 + (100 - uidList.indexOf(gameResult.getUid())));
                } else {
                    gameResult.setJoinRank((100 - uidList.indexOf(gameResult.getUid())));
                }

                // 设置队伍
                if (gameData.getGameType() == SudGameConstant.BALOOT_GAME) {
                    BalootGameExtras balootGameExtras = JSONObject.parseObject(result.getExtras(), BalootGameExtras.class);
                    if (!ObjectUtils.isEmpty(balootGameExtras)) {
                        gameResult.setTeam(balootGameExtras.getTeamIndex());
                    }
                }
                gameResultList.add(gameResult);
            }
            gameData.setGameRoundId(reportMsg.getGameRoundId());
            // 游戏费结算
            if (gameData.getGameType() == SudGameConstant.WOISSPY_GAME) {
                sendWhoIsSpyWinnerReward(gameResultList, gameData);
            }else {
                sendWinnerReward(gameResultList, gameData);
            }
            gameData.setGameResultList(gameResultList);
            sudService.finishGame(gameData);
            if (RoomUtils.isGameRoom(gameData.getRoomId())) {
                // 自动组队下一局游戏
                sudGameRoomService.autoCreateOrJoinNext(gameData);
                BaseTaskFactory.getFactory().addSlow(new Task() {
                    @Override
                    protected void execute() {
                        //  统计游戏房游戏结果
                        int now = DateHelper.getNowSeconds();
                        for (SudGameData.GameResult gameResult : gameData.getGameResultList()) {
                            if (gameResult.getIsWin() == 2 || gameResult.getRank() == 1) {
                                updateUserCareerData(new GameRoomUserCareerData(gameResult.getUid(), 0L, 1L, 1L, now, now));
                            } else {
                                updateUserCareerData(new GameRoomUserCareerData(gameResult.getUid(), 0L, 1L, 0L, now, now));
                            }
                        }
                    }
                });
            }
            // 发送游戏结算消息
            sendGameResultMsg(gameData);
            // 用户任务
            sendCommonTaskMq(gameData);
        }
        return baseResp;
    }

    private void sendCommonTaskMq(SudGameData gameData) {
        if (CollectionUtils.isEmpty(gameData.getGameResultList())) {
            return;
        }
        for (SudGameData.GameResult playerData : gameData.getGameResultList()) {
            if (gameData.getGameType() == SudGameConstant.LUDO_GAME) {
                commonTaskService.sendCommonTaskMq(new CommonMqTopicData(playerData.getUid(), gameData.getRoomId(), "", gameData.getGameId().toString(), CommonMqTaskConstant.PLAY_LUDO, 1));
                if (playerData.getIsWin() == 2) {
                    CommonMqTopicData commonMqTopicData = new CommonMqTopicData(playerData.getUid(), gameData.getRoomId(), "", gameData.getGameId().toString(), CommonMqTaskConstant.WIN_LUDO, 1);
                    CommonMqTopicData.WinGameInfo winGameInfo = new CommonMqTopicData.WinGameInfo();
                    winGameInfo.setCurrencyType(gameData.getCurrencyType());
                    winGameInfo.setAwardValue(playerData.getAwardValue());
                    commonMqTopicData.setJsonData(JSONObject.toJSONString(winGameInfo));
                    commonTaskService.sendCommonTaskMq(commonMqTopicData);
                }
            } else if (gameData.getGameType() == SudGameConstant.UMO_GAME) {
                commonTaskService.sendCommonTaskMq(new CommonMqTopicData(playerData.getUid(), gameData.getRoomId(), "", gameData.getGameId().toString(), CommonMqTaskConstant.PLAY_UMO, 1));
                if (playerData.getIsWin() == 2) {
                    CommonMqTopicData commonMqTopicData = new CommonMqTopicData(playerData.getUid(), gameData.getRoomId(), "", gameData.getGameId().toString(), CommonMqTaskConstant.WIN_UMO, 1);
                    CommonMqTopicData.WinGameInfo winGameInfo = new CommonMqTopicData.WinGameInfo();
                    winGameInfo.setCurrencyType(gameData.getCurrencyType());
                    winGameInfo.setAwardValue(playerData.getAwardValue());
                    commonMqTopicData.setJsonData(JSONObject.toJSONString(winGameInfo));
                    commonTaskService.sendCommonTaskMq(commonMqTopicData);
                }
            } else if (gameData.getGameType() == SudGameConstant.MONSTER_CRUSH_GAME) {
                commonTaskService.sendCommonTaskMq(new CommonMqTopicData(playerData.getUid(), gameData.getRoomId(), "", gameData.getGameId().toString(), CommonMqTaskConstant.PLAY_MONSTER_CRUSH, 1));
                if (playerData.getIsWin() == 2) {
                    CommonMqTopicData commonMqTopicData = new CommonMqTopicData(playerData.getUid(), gameData.getRoomId(), "", gameData.getGameId().toString(), CommonMqTaskConstant.WIN_MONSTER_CRUSH, 1);
                    CommonMqTopicData.WinGameInfo winGameInfo = new CommonMqTopicData.WinGameInfo();
                    winGameInfo.setCurrencyType(gameData.getCurrencyType());
                    winGameInfo.setAwardValue(playerData.getAwardValue());
                    commonMqTopicData.setJsonData(JSONObject.toJSONString(winGameInfo));
                    commonTaskService.sendCommonTaskMq(commonMqTopicData);
                }
            } else if (gameData.getGameType() == SudGameConstant.DOMINO_GAME) {
                commonTaskService.sendCommonTaskMq(new CommonMqTopicData(playerData.getUid(), gameData.getRoomId(), "", gameData.getGameId().toString(), CommonMqTaskConstant.PLAY_DOMINO, 1));
                if (playerData.getIsWin() == 2) {
                    CommonMqTopicData commonMqTopicData = new CommonMqTopicData(playerData.getUid(), gameData.getRoomId(), "", gameData.getGameId().toString(), CommonMqTaskConstant.WIN_DOMINO, 1);
                    CommonMqTopicData.WinGameInfo winGameInfo = new CommonMqTopicData.WinGameInfo();
                    winGameInfo.setCurrencyType(gameData.getCurrencyType());
                    winGameInfo.setAwardValue(playerData.getAwardValue());
                    commonMqTopicData.setJsonData(JSONObject.toJSONString(winGameInfo));
                    commonTaskService.sendCommonTaskMq(commonMqTopicData);
                }
            } else if (gameData.getGameType() == SudGameConstant.CARROM_POOL_GAME) {
                commonTaskService.sendCommonTaskMq(new CommonMqTopicData(playerData.getUid(), gameData.getRoomId(), "", gameData.getGameId().toString(), CommonMqTaskConstant.PLAY_CARROM_POOL, 1));
                if (playerData.getIsWin() == 2) {
                    CommonMqTopicData commonMqTopicData = new CommonMqTopicData(playerData.getUid(), gameData.getRoomId(), "", gameData.getGameId().toString(), CommonMqTaskConstant.WIN_CARROM_POOL, playerData.getAwardValue());
                    CommonMqTopicData.WinGameInfo winGameInfo = new CommonMqTopicData.WinGameInfo();
                    winGameInfo.setCurrencyType(gameData.getCurrencyType());
                    winGameInfo.setAwardValue(playerData.getAwardValue());
                    commonMqTopicData.setJsonData(JSONObject.toJSONString(winGameInfo));
                    commonTaskService.sendCommonTaskMq(commonMqTopicData);
                }
            } else if (gameData.getGameType() == SudGameConstant.JACKAROO_GAME) {
                commonTaskService.sendCommonTaskMq(new CommonMqTopicData(playerData.getUid(), gameData.getRoomId(), "", gameData.getGameId().toString(), CommonMqTaskConstant.PLAY_JACKAROO, 1));
                if (playerData.getIsWin() == 2) {
                    CommonMqTopicData commonMqTopicData = new CommonMqTopicData(playerData.getUid(), gameData.getRoomId(), "", gameData.getGameId().toString(), CommonMqTaskConstant.WIN_JACKAROO, 1);
                    CommonMqTopicData.WinGameInfo winGameInfo = new CommonMqTopicData.WinGameInfo();
                    winGameInfo.setCurrencyType(gameData.getCurrencyType());
                    winGameInfo.setAwardValue(playerData.getAwardValue());
                    commonMqTopicData.setJsonData(JSONObject.toJSONString(winGameInfo));
                    commonTaskService.sendCommonTaskMq(commonMqTopicData);
                }
            } else if (gameData.getGameType() == SudGameConstant.BALOOT_GAME) {
                commonTaskService.sendCommonTaskMq(new CommonMqTopicData(playerData.getUid(), gameData.getRoomId(), "", gameData.getGameId().toString(), CommonMqTaskConstant.PLAY_BALOOT, 1));
                if (playerData.getIsWin() == 2) {
                    CommonMqTopicData commonMqTopicData = new CommonMqTopicData(playerData.getUid(), gameData.getRoomId(), "", gameData.getGameId().toString(), CommonMqTaskConstant.WIN_BALOOT, 1);
                    CommonMqTopicData.WinGameInfo winGameInfo = new CommonMqTopicData.WinGameInfo();
                    winGameInfo.setCurrencyType(gameData.getCurrencyType());
                    winGameInfo.setAwardValue(playerData.getAwardValue());
                    commonMqTopicData.setJsonData(JSONObject.toJSONString(winGameInfo));
                    commonTaskService.sendCommonTaskMq(commonMqTopicData);
                }
            } else if (gameData.getGameType() == SudGameConstant.WOISSPY_GAME) {
                commonTaskService.sendCommonTaskMq(new CommonMqTopicData(playerData.getUid(), gameData.getRoomId(), "", gameData.getGameId().toString(), CommonMqTaskConstant.PLAY_WOISSPY_GAME, 1));
                if (playerData.getIsWin() == 2) {
                    CommonMqTopicData commonMqTopicData = new CommonMqTopicData(playerData.getUid(), gameData.getRoomId(), "", gameData.getGameId().toString(), CommonMqTaskConstant.WIN_WOISSPY_GAME, 1);
                    CommonMqTopicData.WinGameInfo winGameInfo = new CommonMqTopicData.WinGameInfo();
                    winGameInfo.setCurrencyType(gameData.getCurrencyType());
                    winGameInfo.setAwardValue(playerData.getAwardValue());
                    commonMqTopicData.setJsonData(JSONObject.toJSONString(winGameInfo));
                    commonTaskService.sendCommonTaskMq(commonMqTopicData);
                }
            }
        }
    }

    private void sendGameResultMsg(SudGameData data) {
        List<SudGameResultInfoObject> gameResultList = new ArrayList<>();
        SudGameResultInfoObject mvpInfo = new SudGameResultInfoObject();
        List<SudGameResultInfoObject> mvpInfoList = new ArrayList<>();
        int index = 1;
        boolean isGameRoom = RoomUtils.isGameRoom(data.getRoomId());
        int gameType = data.getGameType();
        for (SudGameData.GameResult gameResult : data.getGameResultList()) {
            SudGameResultInfoObject infoObject = new SudGameResultInfoObject();
            BeanUtils.copyProperties(gameResult, infoObject);
            infoObject.setExtras(ObjectUtils.isEmpty(gameResult.getExtras()) ? "" : gameResult.getExtras());
            String awardValueStr = "";
            if (isGameRoom) {
                awardValueStr = gameResult.getAwardValue() == 0 ? "0"
                        : gameResult.getAwardValue() > 0 ? "+" + gameResult.getAwardValue()
                        : gameResult.getAwardValue() + "";
            } else {
                awardValueStr = gameResult.getAwardValue() <= 0 && data.getGameType() != SudGameConstant.WOISSPY_GAME ? "0" : gameResult.getAwardValue() + "";
            }
            infoObject.setAwardValue(awardValueStr);
            infoObject.setAwardType(data.getCurrencyType());
            gameResultList.add(infoObject);
            ActorData actorData = actorDao.getActorDataFromCache(gameResult.getUid());
            if (null == actorData) {
                continue;
            }
            infoObject.setGender(actorData.getFb_gender());
            infoObject.setName(actorData.getName());
            infoObject.setHead(ImageUrlGenerator.generateRoomUserUrl(actorData.getHead(), vipInfoDao.getIntVipLevelFromCache(actorData.getUid())));

            if (MORE_MVP_GAME_TYPE_LIST.contains(gameType)) {
                if (gameResult.getAwardValue() > 0 && mvpInfoList.size() < 2) {
                    SudGameResultInfoObject mvpInfoObject = new SudGameResultInfoObject();
                    BeanUtils.copyProperties(infoObject, mvpInfoObject);
                    mvpInfoList.add(mvpInfoObject);
                }
            } else {
                if (index == 1) {
                    BeanUtils.copyProperties(infoObject, mvpInfo);
                    mvpInfoList.add(mvpInfo);
                }
            }
            index++;
        }
        SudGameResultMsg resultMsg = new SudGameResultMsg();
        resultMsg.setGameResultList(gameResultList);
        resultMsg.setGameType(data.getGameType());
        resultMsg.setMvpInfo(mvpInfo);
        resultMsg.setMvpInfoList(mvpInfoList);
        resultMsg.setLeaderUid(data.getLeaderUid());
        sudService.sendGameResultMsg(data.getRoomId(), resultMsg, false, data.getGameType());
        whoIsSpyEndMicStatus(data);
    }

    private void whoIsSpyEndMicStatus(SudGameData gameInfo){
        try {
            String roomId = gameInfo.getRoomId();
            String micStatusInfo = sudGameRedis.getWhoIsSpyMicStatus(roomId);
            if (ObjectUtils.isEmpty(micStatusInfo)){
                return;
            }
            RoomMicListVo roomMicVO = JSONObject.parseObject(micStatusInfo, RoomMicListVo.class);
            for (RoomMicInfoObject roomMicInfoObject : roomMicVO.getList()) {
                int muteStatus = roomMicInfoObject.getMute();
                MuteMicDTO muteMicDTO = new MuteMicDTO();
                muteMicDTO.setIndex(roomMicInfoObject.getIndex());
                muteMicDTO.setRoomId(roomId);
                muteMicDTO.setUid(RoomUtils.getRoomHostId(roomId));
                muteMicDTO.setOpType(muteStatus == 0 ? 2 : 1);
                iRoomService.muteMic(muteMicDTO);
            }
            sudGameRedis.delWhoIsSpyMicStatus(roomId);
        }catch (Exception e){
            logger.error("whoIsSpyEndMicStatus e={}", e.getMessage(), e);
        }
    }



    private void sendWinnerReward(List<SudGameData.GameResult> gameResults, SudGameData gameData) {
        if (gameData.getCurrency() == 0) {
            logger.info("free game, roomId={} gameId={} gameRoundId={}", gameData.getRoomId(), gameData.getGameId(), gameData.getGameRoundId());
            return;
        }
        // 被踢玩家
        Set<String> kickedSet = new HashSet<>();
        for (SudGamePlayerData sudGamePlayerData : gameData.getPlayerList()) {
            if (sudGamePlayerData.getIsQuit() == 2) {
                kickedSet.add(sudGamePlayerData.getUid());
            }
        }
        // 逃跑玩家数量
        int escapedCount = (int) gameResults.stream().filter(r -> r.getIsEscaped() == 1).count();
        // 赢家奖金
        int totalReward = getWinnerReward(gameData, escapedCount, kickedSet.size());
        // 赢家数量
        int winnerCount = (int) gameResults.stream().filter(r -> r.getIsWin() == 2).count();
        if (winnerCount == 0) {
            winnerCount = (int) gameResults.stream().filter(r -> r.getRank() == 1).count();
        }
        int reward = winnerCount == 0 ? 0 : totalReward / winnerCount;
        for (SudGameData.GameResult result : gameResults) {
            // 如果有玩家在游戏中被踢出房间，则所有游戏费用退回
            if (kickedSet.contains(result.getUid())) {
                gameReward(gameData, result, gameData.getCurrency(), false);
                continue;
            }
            // 逃跑不下发奖励
            if (result.getIsEscaped() == 1 && result.getRank() != 1) {
                result.setAwardValue(-gameData.getCurrency());
                continue;
            }
            if (result.getIsWin() == 2 || result.getRank() == 1) {
                result.setIsEscaped(0);
                result.setAwardValue(reward);
                // 发放奖励
                if (reward > 0) {
                    int gameReward = gameReward(gameData, result, reward, true);
                    // 包含额外奖励的reward
                    result.setAwardValue(gameReward);
                }
            } else {
                // 输家
                result.setAwardValue(-gameData.getCurrency());
            }
        }
        // 排名排序

        if (RoomUtils.isGameRoom(gameData.getRoomId())) {
            gameResults.sort(Comparator.comparing(SudGameData.GameResult::getJoinRank).reversed());
//                gameResultList.sort(Comparator.comparing(SudGameData.GameResult::getIsWin).
//                        thenComparing(SudGameData.GameResult::getJoinRank));
        } else {
            gameResults.sort(awardValueDesc.thenComparing(isEscapedAsc).thenComparing(scoreDesc));
        }
        // 游戏数据埋点
        for (SudGameData.GameResult gameResult : gameResults) {
            GameRecordLogEvent event = new GameRecordLogEvent();
            event.setUid(gameResult.getUid());
            event.setRoom_id(gameData.getRoomId());
            event.setGame_id(gameData.getGameId().toString());
            event.setGame_type(sudService.getEventGameType(gameData.getGameType()));
            event.setIs_creator(gameResult.getUid().equals(gameData.getSelfUid()) ? 1 : 0);
            if (gameData.getCurrencyType() == DIAMOND) {
                event.setCost_diamonds(gameData.getCurrency());
                event.setGet_diamonds(gameResult.getAwardValue());
            } else {
                event.setCost_heart(gameData.getCurrency());
                event.setGet_heart(gameResult.getAwardValue());
            }
            event.setExtra_reward(gameResult.getExtraReward());
            event.setCtime(gameData.getCreateTime());
            try {
                event.setRobot(actorDao.getActorDataFromCache(gameResult.getUid()).getRobot() == 1 ? 1 : 0);
            } catch (Exception ignored) {
            }
            eventReport.track(new com.quhong.analysis.EventDTO(event));
        }
    }

    /**
     * 谁是卧底结算
     * 平民、卧底胜利(卧底被踢出房间、平民都被踢出房间)：
     *   赢家奖金 = 总游戏费 - 被踢玩家游戏费
     *   赢家分成 = 赢家奖金 / （赢家人数 - 赢家逃跑人数 - 赢家被淘汰人数）
     * 平民、卧底胜利(卧底淘汰、平民淘汰)：
     *   赢家奖金 = 总游戏费 - 总游戏费x10%(平台抽成) - 被踢玩家游戏费
     *   赢家分成 = 赢家奖金 / （赢家人数 - 赢家逃跑人数 - 赢家淘汰人数）
     * 平局：
     *   赢家奖金 = 总游戏费 - 总游戏费x10%(平台抽成) - 被踢玩家游戏费
     *   赢家分成 = 赢家奖金 / （总人数 - 逃跑人数 - 淘汰人数）
     */

    private void sendWhoIsSpyWinnerReward(List<SudGameData.GameResult> gameResults, SudGameData gameData) {
        if (gameData.getCurrency() == 0) {
            logger.info("free game, roomId={} gameId={} gameRoundId={}", gameData.getRoomId(), gameData.getGameId(), gameData.getGameRoundId());
            return;
        }
        // 被踢玩家
        Set<String> kickedSet = gameData.getPlayerList().stream().filter(item -> item.getIsQuit() == 2).map(SudGamePlayerData::getUid).collect(Collectors.toSet());
        // 判断平民还是卧底胜利（取result的is_win=2任意一个元素）
        SudGameData.GameResult winResult = gameResults.stream().filter(item -> item.getIsWin() == 2).findFirst().orElse(null);
        SudGameData.WhoIsSpyExtraInfo winExtraInfo = null;
        if (winResult != null) {
            winExtraInfo = JSONObject.parseObject(winResult.getExtras(), SudGameData.WhoIsSpyExtraInfo.class);
        }
        int winFlag = 0;  // 0: 平局 1: 平民胜利 2: 卧底胜利
        int totalReward = 0;
        int kickCount = kickedSet.size();
        int winnerCount = 0; // 总赢家人数
        // 分成玩家
        List<String> divideUidList = new ArrayList<>();
        // 主动逃跑玩家
        List<String> escapedUidList = new ArrayList<>();
        // 淘汰玩家
        List<String> dieOutUidList = new ArrayList<>();
        if (winExtraInfo == null) {
            // 平局
            winFlag = 0;
            winnerCount = gameResults.size();
            totalReward = gameData.getTotalCurrency() - (int) (gameData.getTotalCurrency() * 0.1) - (gameData.getCurrency() * kickCount);
        } else {
            // 平民或卧底胜利
            winFlag = winExtraInfo.getRoleId();
            winnerCount = (int) gameResults.stream().filter(r -> r.getIsWin() == 2 && !kickedSet.contains(r.getUid())).count();
            // 判断卧底或平民是否被踢出房间-存在多个
            List<String> loserUidList = gameResults.stream().filter(item -> item.getIsWin() == 1).map(SudGameData.GameResult::getUid).collect(Collectors.toList());
            if (!CollectionUtils.isEmpty(loserUidList) && kickedSet.containsAll(loserUidList)) {
                totalReward = gameData.getTotalCurrency() - (gameData.getCurrency() * kickCount);
            }else {
                totalReward = gameData.getTotalCurrency() - (int) (gameData.getTotalCurrency() * 0.1) - (gameData.getCurrency() * kickCount);
            }
        }

        for (SudGameData.GameResult result : gameResults) {
            // 如果有玩家在游戏中被踢出房间，则所有游戏费用退回
            if (kickedSet.contains(result.getUid())) {
                gameReward(gameData, result, gameData.getCurrency(), false);
                result.setAwardValue(gameData.getCurrency());
                continue;
            }
            SudGameData.WhoIsSpyExtraInfo resultExtraInfo = JSONObject.parseObject(result.getExtras(), SudGameData.WhoIsSpyExtraInfo.class);
            // 输家不下发奖励
            if((winFlag == 1 && resultExtraInfo.getRoleId() == 2) || (winFlag == 2 && resultExtraInfo.getRoleId() == 1)){
                result.setAwardValue(-gameData.getCurrency());
                continue;
            }

            // 逃跑不下发奖励
            if (result.getIsEscaped() == 1) {
                escapedUidList.add(result.getUid());
                result.setAwardValue(-gameData.getCurrency());
                continue;
            }
            // 淘汰不下发奖励
            if (resultExtraInfo.isSelfDie()){
                dieOutUidList.add(result.getUid());
                result.setAwardValue(-gameData.getCurrency());
                continue;
            }
            divideUidList.add(result.getUid());
        }

        // 分成人数
        int divideNumber = winnerCount - escapedUidList.size() - dieOutUidList.size();
        // 每人奖励
        int reward = divideNumber <= 0 ? 0 : totalReward / divideNumber;
        logger.info("whoIsSpyWinnerReward roomId={} gameId={} totalReward={} kickedCount={} winnerCount={} divideNumber={} reward={}", gameData.getRoomId(), gameData.getGameId(), totalReward, kickCount, winnerCount, divideNumber, reward);
        for (SudGameData.GameResult result : gameResults) {
            if (divideUidList.contains(result.getUid())){
                int gameReward = gameReward(gameData, result, reward, true);
                // 包含额外奖励的reward
                result.setAwardValue(gameReward);
            }
        }
        // 排名排序
        gameResults.sort(awardValueDesc.thenComparing(isEscapedAsc).thenComparing(scoreDesc));
        // 游戏数据埋点
        for (SudGameData.GameResult gameResult : gameResults) {
            GameRecordLogEvent event = new GameRecordLogEvent();
            event.setUid(gameResult.getUid());
            event.setRoom_id(gameData.getRoomId());
            event.setGame_id(gameData.getGameId().toString());
            event.setGame_type(sudService.getEventGameType(gameData.getGameType()));
            event.setIs_creator(gameResult.getUid().equals(gameData.getSelfUid()) ? 1 : 0);
            if (gameData.getCurrencyType() == DIAMOND) {
                event.setCost_diamonds(gameData.getCurrency());
                event.setGet_diamonds(gameResult.getAwardValue());
            } else {
                event.setCost_heart(gameData.getCurrency());
                event.setGet_heart(gameResult.getAwardValue());
            }
            event.setExtra_reward(gameResult.getExtraReward());
            JSONObject gameInfo = new JSONObject();
            gameInfo.put("game_config", JSONObject.toJSONString(gameData.getRule()));
            event.setGame_info(gameInfo.toJSONString());
            event.setCtime(gameData.getCreateTime());
            try {
                event.setRobot(actorDao.getActorDataFromCache(gameResult.getUid()).getRobot() == 1 ? 1 : 0);
            } catch (Exception ignored) {
            }
            eventReport.track(new com.quhong.analysis.EventDTO(event));

        }
    }

    /**
     * 平台抽成：每局抽取游戏费用的20%，且只有一个赢家。
     * 计算公式：总游戏费 - 总游戏费x20%(平台抽成) = 赢家奖金
     * 中途退出游戏判定为输，平台抽取70%该游戏的费用，30%给到赢家。
     */
    private int getWinnerReward(SudGameData gameData, int escapedCount, int kickCount) {
        return gameData.getTotalCurrency() - (int) (gameData.getTotalCurrency() * 0.2) - (int) (gameData.getCurrency() * 0.5 * escapedCount) - (gameData.getCurrency() * kickCount);
    }

    /**
     * 游戏结束下发胜利者的奖励
     *
     * @param reward 是否是游戏奖励
     */
    public int gameReward(SudGameData gameInfo, SudGameData.GameResult result, int changed, boolean reward) {
        String uid = result.getUid();
        SudGameConfigInfo gameConfigInfo = sudGameConfig.getSudGameInfoMap().get(gameInfo.getGameType());
        // 原始奖励
        int tempChange = changed;
        if (reward && RoomUtils.isVoiceRoom(gameInfo.getRoomId())) {
            // 消消乐钻石局游戏，单局游戏人数>4人才有额外奖励
            if (gameInfo.getGameType() != SudGameConstant.MONSTER_CRUSH_GAME || gameInfo.getPlayerList().size() > 4) {
                changed = operationConfigRedis.randomExtraGameReward(uid, gameInfo.getGameType(), changed);
                result.setExtraReward(changed - tempChange);
            }
        }
        // 发放游戏币奖励
        if (COIN == gameInfo.getCurrencyType()) {
            boolean success = heartRecordDao.changeHeart(uid, changed, gameConfigInfo.getRewardTitle(), gameConfigInfo.getRewardDesc());
            if (success) {
                logger.info("sud game reward coin uid={} coin={} gameRoundId={}", uid, changed, gameInfo.getGameRoundId());
            } else {
                logger.error("sud game reward coin error uid={} coin={} gameRoundId={}", uid, changed, gameInfo.getGameRoundId());
            }
        } else if (DIAMOND == gameInfo.getCurrencyType()) {
            if (sudGameRedis.incPrizePoolBeans(gameInfo.getGameId().toString(), -tempChange) < 0) {
                sudGameRedis.setAbnormalUser(gameInfo.getGameType(), uid);
                logger.error("Not enough diamonds in the prize pool. gameId={} uid={} change={}", gameInfo.getGameId(), uid, changed);
                return changed;
            }
            MoneyDetailReq moneyDetailReq = new MoneyDetailReq();
            moneyDetailReq.setRandomId();
            moneyDetailReq.setUid(uid);
            moneyDetailReq.setAtype(gameConfigInfo.getRewardActType());
            moneyDetailReq.setChanged(changed);
            moneyDetailReq.setTitle(gameConfigInfo.getRewardTitle());
            moneyDetailReq.setDesc(gameConfigInfo.getRewardDesc());
            moneyDetailReq.setRoomId(gameInfo.getRoomId());
            mqService.asyncChargeDiamonds(moneyDetailReq);
            // dealWithLudoUmoBadge(uid, changed, gameInfo.getGameType());
            logger.info("sud game reward diamond uid={} coin={} gameRoundId={}", uid, changed, gameInfo.getGameRoundId());
            sudService.gameDiamondAlert(gameInfo.getGameId().toString(), gameInfo.getGameType(), changed);
        }
        return changed;
    }


    public void dealWithLudoUmoBadge(String uid, int earnBeans, int gameType) {
        String countType = gameType == SudGameConstant.LUDO_GAME ? ActorConfigDao.LUDO_COUNT : ActorConfigDao.UMO_COUNT;
        int achieveType = gameType == SudGameConstant.LUDO_GAME ? AchieveBadgeConstant.TYPE_LUDO : AchieveBadgeConstant.TYPE_UMO;
        int moneyType = gameType == SudGameConstant.LUDO_GAME ? MoneyTypeConstant.LUDO_WIN : MoneyTypeConstant.UMO_WIN;

        long ludoOrUmoCount = actorConfigDao.getLongUserConfig(uid, countType, -1);
        if (ludoOrUmoCount > -1) {
            ludoOrUmoCount += earnBeans;
            logger.info("dealWithLudoUmoBadge uid: {} gameType:{}, ludoOrUmoCount:{}", uid, gameType, ludoOrUmoCount);
            actorConfigDao.updateUserConfig(uid, countType, ludoOrUmoCount);
            badgeService.doAchieveBadge(uid, achieveType, ludoOrUmoCount, earnBeans);
        } else {
            try {
                MoneyTypeDTO dto = new MoneyTypeDTO();
                dto.setUid(uid);
                dto.setMoneyType(moneyType);
                ApiResult<Long> result = dataCenterService.esMoneyTypeTotal(dto);

                if (result.isError()) {
                    logger.error("esMoneyTypeTotal LudoUmo error uid:{}, code: {} msg:{}", uid, result.getCode().getCode(), result.getCode().getMsg());
                } else {
                    long totalLudoUmoBean = result.getData();
                    logger.info("esMoneyTypeTotal totalLudoUmoBean: {}, ludoOrUmoCount:{}", totalLudoUmoBean, ludoOrUmoCount);
                    if (totalLudoUmoBean > -1) {
                        badgeService.supplyAchieveBadge(uid, achieveType, totalLudoUmoBean);
                        actorConfigDao.updateUserConfig(uid, countType, totalLudoUmoBean);
                    }
                }
            } catch (Exception e) {
                logger.error("esMoneyTypeTotal totalLudoUmoBean error uid: {}, message:{}", uid, e.getMessage(), e);
            }
        }
    }

    private void updateUserCareerData(GameRoomUserCareerData data) {
        int row = gameRoomUserCareerDao.incNum(data);
        if (row <= 0) {
            gameRoomUserCareerDao.insert(data);
        }
    }
}
