package com.quhong.mongo.data;

import com.quhong.mongo.dao.PkGiftDao;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/12/15
 */
@Document(collection = PkGiftDao.TABLE_NAME)
public class PkGiftData {

    @Id
    private ObjectId _id;

    private int status;

    private List<Integer> gift_id;

    private int mtime;

    public ObjectId get_id() {
        return _id;
    }

    public void set_id(ObjectId _id) {
        this._id = _id;
    }

    public int getStatus() {
        return status;
    }

    public void setStatus(int status) {
        this.status = status;
    }

    public List<Integer> getGift_id() {
        return gift_id;
    }

    public void setGift_id(List<Integer> gift_id) {
        this.gift_id = gift_id;
    }

    public int getMtime() {
        return mtime;
    }

    public void setMtime(int mtime) {
        this.mtime = mtime;
    }
}
