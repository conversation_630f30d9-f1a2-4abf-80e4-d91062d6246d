package com.quhong.mongo.data;

import com.quhong.mongo.dao.LuckyBoxDailyNumDao;
import org.bson.types.ObjectId;
import org.springframework.data.annotation.Id;
import org.springframework.data.mongodb.core.mapping.Document;

/**
 * <AUTHOR>
 * @date 2023/1/13
 */
@Document(collection = LuckyBoxDailyNumDao.TABLE_NAME)
public class LuckyBoxDailyNumData {

    @Id
    private ObjectId _id;

    /**
     * 用户id
     */
    private String uid;

    /**
     * 动作 1 发送 2接收
     */
    private int action;

    /**
     * 房间id
     */
    private String room_id;

    /**
     * 红包id
     */
    private String box_id;

    /**
     * 数量，目前定值1
     */
    private int num;

    /**
     * 钻石数量
     */
    private int beans;

    /**
     * 创建时间
     */
    private int c_time;

    private int x_pos;

    private int y_pos;

    /**
     * 是否是机器人 0否 1是
     */
    private int is_robot;

    /**
    *  0 房间未锁 1 房间已锁
    */
    private int rm_lock;

    /**
     * 房间人数
     */
    private int rm_count;

    public ObjectId get_id() {
        return _id;
    }

    public void set_id(ObjectId _id) {
        this._id = _id;
    }

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public int getAction() {
        return action;
    }

    public void setAction(int action) {
        this.action = action;
    }

    public String getRoom_id() {
        return room_id;
    }

    public void setRoom_id(String room_id) {
        this.room_id = room_id;
    }

    public String getBox_id() {
        return box_id;
    }

    public void setBox_id(String box_id) {
        this.box_id = box_id;
    }

    public int getNum() {
        return num;
    }

    public void setNum(int num) {
        this.num = num;
    }

    public int getBeans() {
        return beans;
    }

    public void setBeans(int beans) {
        this.beans = beans;
    }

    public int getC_time() {
        return c_time;
    }

    public void setC_time(int c_time) {
        this.c_time = c_time;
    }

    public int getX_pos() {
        return x_pos;
    }

    public void setX_pos(int x_pos) {
        this.x_pos = x_pos;
    }

    public int getY_pos() {
        return y_pos;
    }

    public void setY_pos(int y_pos) {
        this.y_pos = y_pos;
    }

    public int getIs_robot() {
        return is_robot;
    }

    public void setIs_robot(int is_robot) {
        this.is_robot = is_robot;
    }

    public int getRm_lock() {
        return rm_lock;
    }

    public void setRm_lock(int rm_lock) {
        this.rm_lock = rm_lock;
    }

    public int getRm_count() {
        return rm_count;
    }

    public void setRm_count(int rm_count) {
        this.rm_count = rm_count;
    }
}
