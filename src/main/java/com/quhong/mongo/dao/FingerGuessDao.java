package com.quhong.mongo.dao;

import com.quhong.mongo.config.MongoBean;
import com.quhong.mongo.data.FingerGuessData;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.domain.Sort;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2022/11/3
 */
@Component
public class FingerGuessDao {

    private static final Logger logger = LoggerFactory.getLogger(FingerGuessDao.class);

    public static final String TABLE_NAME = "finger_guess";

    @Resource(name = MongoBean.MOVIES)
    private MongoTemplate mongoTemplate;

    public void save(FingerGuessData data) {
        try {
            mongoTemplate.save(data);
        } catch (Exception e) {
            logger.error("save finger guess data error. uid={} {}", data.getUid(), e.getMessage(), e);
        }
    }

    public FingerGuessData findData(String id) {
        try {
            Criteria criteria = Criteria.where("_id").is(new ObjectId(id));
            return mongoTemplate.findOne(new Query(criteria), FingerGuessData.class);
        } catch (Exception e) {
            logger.error("find finger guess data error. id={} {}", id, e.getMessage(), e);
        }
        return null;
    }

    public List<FingerGuessData> getHistory(String uid, int start, int pageSize, boolean isNewVer) {
        try {
            Criteria criteria = new Criteria();
            if (!isNewVer) {
                criteria.and("status").is(1);
            }
            criteria.orOperator(Criteria.where("uid").is(uid), Criteria.where("aid").is(uid));
            Sort sort = Sort.by(Sort.Direction.DESC, "c_time");
            Query query = new Query(criteria);
            query.with(sort).skip(start).limit(pageSize);
            return mongoTemplate.find(query, FingerGuessData.class);
        } catch (Exception e) {
            logger.error("find finger guess history data error. uid={} {}", uid, e.getMessage(), e);
        }
        return null;
    }

}
