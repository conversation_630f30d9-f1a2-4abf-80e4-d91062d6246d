package com.quhong.mongo.dao;

import com.quhong.cache.CacheMap;
import com.quhong.mongo.config.MongoBean;
import com.quhong.mongo.data.PkGiftData;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.stereotype.Component;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2022/12/15
 */
@Component
public class PkGiftDao {

    private static final Logger logger = LoggerFactory.getLogger(PkGiftDao.class);

    public static final String TABLE_NAME = "pk_gift";
    private static final long CACHE_TIME_MILLIS = 3 * 60 * 1000L;
    private static final String VALID_KEY = "valid";

    @Resource(name = MongoBean.MOVIES)
    private MongoTemplate mongoTemplate;

    private final CacheMap<String, PkGiftData> cacheMap;

    public PkGiftDao() {
        cacheMap = new CacheMap<>(CACHE_TIME_MILLIS);
    }

    @PostConstruct
    public void postInit() {
        cacheMap.start();
    }

    public PkGiftData findValidData() {
        PkGiftData data = cacheMap.getData(VALID_KEY);
        if (data != null) {
            return data;
        }
        data = findData();
        if (data != null) {
            cacheMap.cacheData(VALID_KEY, data);
        }
        return data;
    }

    public PkGiftData findData() {
        try {
            Criteria criteria = Criteria.where("status").is(1);
            return mongoTemplate.findOne(new Query(criteria), PkGiftData.class);
        } catch (Exception e) {
            logger.error("find pk gift data error. {}", e.getMessage(), e);
        }
        return null;
    }
}
