package com.quhong.mongo.dao;

import com.quhong.core.utils.DateHelper;
import com.quhong.mongo.config.MongoBean;
import com.quhong.mongo.data.LuckyBoxData;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2022/12/21
 */
@Component
public class LuckyBoxDao {

    private static final Logger logger = LoggerFactory.getLogger(LuckyBoxDao.class);

    public static final String TABLE_NAME = "lucky_box";

    @Resource(name = MongoBean.MOVIES)
    private MongoTemplate mongoTemplate;

    public void save(LuckyBoxData data) {
        try {
            mongoTemplate.save(data);
        } catch (Exception e) {
            logger.error("save lucky box data error.uid={} {}", data.getUid(), e.getMessage(), e);
        }
    }

    public void updateValidEnd(String boxId) {
        try {
            Query query = new Query(Criteria.where("_id").is(new ObjectId(boxId)).and("valid").is("active"));
            Update update = new Update();
            update.set("valid", "end");
            update.set("mtime", DateHelper.getNowSeconds());
            mongoTemplate.updateFirst(query, update, TABLE_NAME);
        } catch (Exception e) {
            logger.error("updateValidEnd error. boxId={} {}", e.getMessage(), e);
        }
    }

    public LuckyBoxData findData(String boxId) {
        try {
            Criteria criteria = Criteria.where("_id").is(new ObjectId(boxId));
            return mongoTemplate.findOne(new Query(criteria), LuckyBoxData.class);
        } catch (Exception e) {
            logger.error("find lucky box data error.boxId={} {}", boxId, e.getMessage(), e);
            return null;
        }
    }
}
