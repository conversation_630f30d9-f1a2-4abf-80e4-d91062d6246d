package com.quhong.data.vo;


import java.util.List;

public class PetFeedVO extends OtherRankConfigVO {

    private MyInfoVO myInfoVO;
    private PetInfoVO petInfoVO;
    private List<TaskConfigVO> dailyTaskList; // 每日基础任务
    private List<TaskConfigVO> dailyAdvanceTaskList; // 每日进阶任务

    // 当前活动生涯数据
    public static class CareerInfo {
        private int food; // 食物数量
        private int toys; // 玩具数量
        private int love; // 爱心数量
        private String signDate; // 最后一次的签到日期 yyyy-MM-dd
        private int signDay; // 签到天数
        private int signReminderStatus; // 签到提醒 0已关闭 1已开启


        private String petName; // 宠物名字
        private int petType; // 宠物类型 1 猫咪 2 猎豹 3 狮子
        private int petStatus; // 宠物状态 1 正常领养 2 放养
        private int eatTotalFood; // 已喂养的食物总数量
        private int ctime; // 宠物领取时间

        public int getFood() {
            return food;
        }

        public void setFood(int food) {
            this.food = food;
        }

        public int getToys() {
            return toys;
        }

        public void setToys(int toys) {
            this.toys = toys;
        }

        public int getLove() {
            return love;
        }

        public void setLove(int love) {
            this.love = love;
        }

        public String getSignDate() {
            return signDate;
        }

        public void setSignDate(String signDate) {
            this.signDate = signDate;
        }

        public int getSignDay() {
            return signDay;
        }

        public void setSignDay(int signDay) {
            this.signDay = signDay;
        }

        public int getSignReminderStatus() {
            return signReminderStatus;
        }

        public void setSignReminderStatus(int signReminderStatus) {
            this.signReminderStatus = signReminderStatus;
        }


        public String getPetName() {
            return petName;
        }

        public void setPetName(String petName) {
            this.petName = petName;
        }

        public int getPetType() {
            return petType;
        }

        public void setPetType(int petType) {
            this.petType = petType;
        }

        public int getEatTotalFood() {
            return eatTotalFood;
        }

        public void setEatTotalFood(int eatTotalFood) {
            this.eatTotalFood = eatTotalFood;
        }

        public int getPetStatus() {
            return petStatus;
        }

        public void setPetStatus(int petStatus) {
            this.petStatus = petStatus;
        }

        public int getCtime() {
            return ctime;
        }

        public void setCtime(int ctime) {
            this.ctime = ctime;
        }
    }

    // 每日喂养相关的业务
    public static class DailyInfo {
        private String dayStr; // 日期 yyyy-MM-dd
        private int eatFood; // 宠物当日已喂养的食物数量
        private int playToys; // 宠物当日已玩耍的玩具数量

        // 用户相关
        private int isDoneMyPetFood; // 当日是否完成对自己宠物喂养食物任务 0未完成 1完成
        private int feedOtherPetFood; // 当日喂养Ta人宠物的食物数量
        private int throwBallOtherPet; // 当日投球Ta人宠物的次数

        public String getDayStr() {
            return dayStr;
        }

        public void setDayStr(String dayStr) {
            this.dayStr = dayStr;
        }

        public int getFeedOtherPetFood() {
            return feedOtherPetFood;
        }

        public void setFeedOtherPetFood(int feedOtherPetFood) {
            this.feedOtherPetFood = feedOtherPetFood;
        }

        public int getThrowBallOtherPet() {
            return throwBallOtherPet;
        }

        public void setThrowBallOtherPet(int throwBallOtherPet) {
            this.throwBallOtherPet = throwBallOtherPet;
        }

        public int getEatFood() {
            return eatFood;
        }

        public void setEatFood(int eatFood) {
            this.eatFood = eatFood;
        }

        public int getPlayToys() {
            return playToys;
        }

        public void setPlayToys(int playToys) {
            this.playToys = playToys;
        }

        public int getIsDoneMyPetFood() {
            return isDoneMyPetFood;
        }

        public void setIsDoneMyPetFood(int isDoneMyPetFood) {
            this.isDoneMyPetFood = isDoneMyPetFood;
        }
    }

    // 每日任务完成情况
    public static class DailyTaskInfo {

        private String dayStr; // 日期 yyyy-MM-dd
        // 每日基础任务
        private int shareSnapchatStatus; // 分享到snapchat状态
        private int onMic5Status; // 完成上麦5分钟状态
        private int sendGiftDiamond; // 送礼钻石数,没500钻石算一个单位，每个单位100g粮食
        private int receiveGiftDiamond; // 收礼钻石数
        // 每日进阶任务
        private int rechargeTimes; // 充值110钻以上的笔数，包含所有渠道
        private int inviteNewUser; // 邀请新用户数量
        private int inviteOldUser; // 召回老朋友数量

        public String getDayStr() {
            return dayStr;
        }

        public void setDayStr(String dayStr) {
            this.dayStr = dayStr;
        }

        public int getShareSnapchatStatus() {
            return shareSnapchatStatus;
        }

        public void setShareSnapchatStatus(int shareSnapchatStatus) {
            this.shareSnapchatStatus = shareSnapchatStatus;
        }

        public int getOnMic5Status() {
            return onMic5Status;
        }

        public void setOnMic5Status(int onMic5Status) {
            this.onMic5Status = onMic5Status;
        }

        public int getSendGiftDiamond() {
            return sendGiftDiamond;
        }

        public void setSendGiftDiamond(int sendGiftDiamond) {
            this.sendGiftDiamond = sendGiftDiamond;
        }

        public int getReceiveGiftDiamond() {
            return receiveGiftDiamond;
        }

        public void setReceiveGiftDiamond(int receiveGiftDiamond) {
            this.receiveGiftDiamond = receiveGiftDiamond;
        }

        public int getRechargeTimes() {
            return rechargeTimes;
        }

        public void setRechargeTimes(int rechargeTimes) {
            this.rechargeTimes = rechargeTimes;
        }

        public int getInviteNewUser() {
            return inviteNewUser;
        }

        public void setInviteNewUser(int inviteNewUser) {
            this.inviteNewUser = inviteNewUser;
        }

        public int getInviteOldUser() {
            return inviteOldUser;
        }

        public void setInviteOldUser(int inviteOldUser) {
            this.inviteOldUser = inviteOldUser;
        }
    }


    public static class MyInfoVO {
        private String myName;
        private String myHead;

        private int food; // 食物数量
        private int toys; // 玩具数量
        private int love; // 爱心数量
        private String signDate; // 最后一次的签到日期 yyyy-MM-dd
        private int signDay; // 签到天数
        private int signReminderStatus; // 签到提醒 0已关闭 1已开启

        private int isDoneMyPetFood; // 当日是否完成对自己宠物喂养食物任务,完成后需要展示想玩耍 0未完成 1完成

        private int havePet; // 是否有宠物 0没有 1有

        public String getMyName() {
            return myName;
        }

        public void setMyName(String myName) {
            this.myName = myName;
        }

        public String getMyHead() {
            return myHead;
        }

        public void setMyHead(String myHead) {
            this.myHead = myHead;
        }

        public int getFood() {
            return food;
        }

        public void setFood(int food) {
            this.food = food;
        }

        public int getToys() {
            return toys;
        }

        public void setToys(int toys) {
            this.toys = toys;
        }

        public int getLove() {
            return love;
        }

        public void setLove(int love) {
            this.love = love;
        }

        public String getSignDate() {
            return signDate;
        }

        public void setSignDate(String signDate) {
            this.signDate = signDate;
        }

        public int getSignDay() {
            return signDay;
        }

        public void setSignDay(int signDay) {
            this.signDay = signDay;
        }

        public int getSignReminderStatus() {
            return signReminderStatus;
        }

        public void setSignReminderStatus(int signReminderStatus) {
            this.signReminderStatus = signReminderStatus;
        }

        public int getIsDoneMyPetFood() {
            return isDoneMyPetFood;
        }

        public void setIsDoneMyPetFood(int isDoneMyPetFood) {
            this.isDoneMyPetFood = isDoneMyPetFood;
        }

        public int getHavePet() {
            return havePet;
        }

        public void setHavePet(int havePet) {
            this.havePet = havePet;
        }
    }

    public static class PetInfoVO {
        private String aid; // 宠物主人的uid
        private String aidName; // 宠物主人的昵称
        private String aidHead; // 宠物主人的头像

        private String petName; // 宠物名字
        private int petType; // 宠物类型 1 猫咪 2 猎豹 3 狮子
        private int eatTotalFood; // 已喂养的食物总数量
        private int ctime; // 宠物领取时间
        private int accompanyDay; // 陪伴天数

        private int eatFood; // 宠物当日已喂养的食物数量
        private int playToys; // 宠物当日已玩耍的玩具数量
        private int ageStage; // 年龄阶段 0幼年 1成年
        private int needFood; // 当前需要喂养的最大食物数量
        private int needToys; // 当前需要玩耍的最大玩具数量

        private int addLove; // 喂养或玩耍宠物获得的爱心数量

        public int getAgeStage() {
            return ageStage;
        }

        public void setAgeStage(int ageStage) {
            this.ageStage = ageStage;
        }

        public int getNeedFood() {
            return needFood;
        }

        public void setNeedFood(int needFood) {
            this.needFood = needFood;
        }

        public int getNeedToys() {
            return needToys;
        }

        public void setNeedToys(int needToys) {
            this.needToys = needToys;
        }

        public String getPetName() {
            return petName;
        }

        public void setPetName(String petName) {
            this.petName = petName;
        }

        public int getPetType() {
            return petType;
        }

        public void setPetType(int petType) {
            this.petType = petType;
        }

        public int getEatTotalFood() {
            return eatTotalFood;
        }

        public void setEatTotalFood(int eatTotalFood) {
            this.eatTotalFood = eatTotalFood;
        }

        public int getCtime() {
            return ctime;
        }

        public void setCtime(int ctime) {
            this.ctime = ctime;
        }

        public int getAccompanyDay() {
            return accompanyDay;
        }

        public void setAccompanyDay(int accompanyDay) {
            this.accompanyDay = accompanyDay;
        }

        public int getEatFood() {
            return eatFood;
        }

        public void setEatFood(int eatFood) {
            this.eatFood = eatFood;
        }

        public int getPlayToys() {
            return playToys;
        }

        public void setPlayToys(int playToys) {
            this.playToys = playToys;
        }

        public String getAid() {
            return aid;
        }

        public void setAid(String aid) {
            this.aid = aid;
        }

        public String getAidName() {
            return aidName;
        }

        public void setAidName(String aidName) {
            this.aidName = aidName;
        }

        public String getAidHead() {
            return aidHead;
        }

        public void setAidHead(String aidHead) {
            this.aidHead = aidHead;
        }

        public int getAddLove() {
            return addLove;
        }

        public void setAddLove(int addLove) {
            this.addLove = addLove;
        }
    }

    public static class HistoryRecordPageVO {
        private List<HistoryRecordVO> historyRecordList;
        private int nextPage;

        public List<HistoryRecordVO> getHistoryRecordList() {
            return historyRecordList;
        }

        public void setHistoryRecordList(List<HistoryRecordVO> historyRecordList) {
            this.historyRecordList = historyRecordList;
        }

        public int getNextPage() {
            return nextPage;
        }

        public void setNextPage(int nextPage) {
            this.nextPage = nextPage;
        }
    }

    public static class HistoryRecordVO {
        // 1 喂食 2 投球 3 喂养他人 4 喂养他人达成目标 5 投球他人 6 投球他人达成目标 7 商店兑换 8 别人喂养我的宠物 9 别人投球我的宠物
        private int actionType;
        private int num; // 相应行为的数量
        private String aid; // 目标用户
        private int love; // 相应行为产生或消耗的爱心数量
        private String name; // 目标用户昵称或者资源名称
        private int ctime; // 创建时间

        public int getActionType() {
            return actionType;
        }

        public void setActionType(int actionType) {
            this.actionType = actionType;
        }

        public int getNum() {
            return num;
        }

        public void setNum(int num) {
            this.num = num;
        }

        public String getAid() {
            return aid;
        }

        public void setAid(String aid) {
            this.aid = aid;
        }

        public int getLove() {
            return love;
        }

        public void setLove(int love) {
            this.love = love;
        }

        public String getName() {
            return name;
        }

        public void setName(String name) {
            this.name = name;
        }

        public int getCtime() {
            return ctime;
        }

        public void setCtime(int ctime) {
            this.ctime = ctime;
        }
    }

    public MyInfoVO getMyInfoVO() {
        return myInfoVO;
    }

    public void setMyInfoVO(MyInfoVO myInfoVO) {
        this.myInfoVO = myInfoVO;
    }

    public PetInfoVO getPetInfoVO() {
        return petInfoVO;
    }

    public void setPetInfoVO(PetInfoVO petInfoVO) {
        this.petInfoVO = petInfoVO;
    }

    public List<TaskConfigVO> getDailyTaskList() {
        return dailyTaskList;
    }

    public void setDailyTaskList(List<TaskConfigVO> dailyTaskList) {
        this.dailyTaskList = dailyTaskList;
    }

    public List<TaskConfigVO> getDailyAdvanceTaskList() {
        return dailyAdvanceTaskList;
    }

    public void setDailyAdvanceTaskList(List<TaskConfigVO> dailyAdvanceTaskList) {
        this.dailyAdvanceTaskList = dailyAdvanceTaskList;
    }
}
