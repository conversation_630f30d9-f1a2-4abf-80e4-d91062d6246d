package com.quhong.redis;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.quhong.cache.CacheMap;
import com.quhong.config.CaffeineCacheConfig;
import com.quhong.constant.ExpireTimeConstant;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.StarBeatRedisData;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.context.annotation.Lazy;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.ZSetOperations;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;

@Lazy
@Component
public class StarBeatGameRedis {
    private final static Logger logger = LoggerFactory.getLogger(StarBeatGameRedis.class);
    private final static int ROLL_MAX_SIZE = 300;
    private final static int HISTORY_MAX_SIZE = 3000;
    public static final long EXPIRE_TIME = 12 * 60 * 1000;

    @Resource(name = DataRedisBean.MAIN_CLUSTER)
    private StringRedisTemplate clusterRedis;
    private String rankDateStr = "";
    private String rollDateStr = "";
    private CacheMap<String, String> historyDateCacheMap;


    public StarBeatGameRedis() {
        historyDateCacheMap = new CacheMap<>(EXPIRE_TIME);
    }

    @PostConstruct
    public void postInit() {
        historyDateCacheMap.start();
    }

    public int setLuckyStar(String uid, int type, int num) {
        try {
            String key = getStarUidKey(uid, type);
            clusterRedis.opsForValue().set(key, String.valueOf(num));
            clusterRedis.expire(key, ExpireTimeConstant.EXPIRE_DAYS_THIRTY, TimeUnit.DAYS);
            return num;
        } catch (Exception e) {
            logger.error("incrLuckyStar error.uid={} {}", uid, e.getMessage());
            return 0;
        }
    }

    /**
     * @param uid
     * @param addNum 大于增加， 小于减去
     * @return
     */
    public int incrLuckyStar(String uid, int type, int addNum) {
        try {
            String key = getStarUidKey(uid, type);
            Long value = clusterRedis.opsForValue().increment(key, addNum);
            clusterRedis.expire(key, ExpireTimeConstant.EXPIRE_DAYS_THIRTY, TimeUnit.DAYS);
            return null == value ? 0 : value.intValue();
        } catch (Exception e) {
            logger.error("incrLuckyStar error.uid={} {}", uid, e.getMessage());
            return 0;
        }
    }

    public int getLuckyStar(String uid, int type) {
        int count = 0;
        try {
            String key = getStarUidKey(uid, type);
            String strValue = clusterRedis.opsForValue().get(key);
            if (!StringUtils.isEmpty(strValue)) {
                count = Integer.parseInt(strValue);
            }
            return count;
        } catch (Exception e) {
            logger.error("getLuckyStar error. uid={} {}", uid, e.getMessage(), e);
            return 0;
        }
    }


    /**
     * 获取分数
     */
    public int getCommonZSetRankingScore(String weekStart, String uid) {
        try {
            Double score = clusterRedis.opsForZSet().score(getCommonZSetKey(weekStart), uid);
            return score != null ? score.intValue() : 0;
        } catch (Exception e) {
            logger.info("getCommonZSetRankingScore error weekStart={} uid={}, e={}", weekStart, uid, e.getMessage(), e);
            return 0;
        }
    }


    public void addCommonZSetRankingScore(String weekStart, String uid, int score) {
        try {
            String key = getCommonZSetKey(weekStart);
            clusterRedis.opsForZSet().add(key, uid, score);
            String nowStr = DateHelper.ARABIAN.formatDateInDay();
            if (!nowStr.equals(rankDateStr)) {
                rankDateStr = nowStr;
                clusterRedis.expire(key, ExpireTimeConstant.EXPIRE_DAYS_THIRTY, TimeUnit.DAYS);
            }
        } catch (Exception e) {
            logger.info("addCommonZSetRankingScore error weekStart={} aid={} giftId={} score={}", weekStart, uid, score, e.getMessage(), e);
        }
    }

    public void incrCommonZSetRankingScoreSimple(String weekStart, String uid, int score) {
        try {
            String key = getCommonZSetKey(weekStart);
            clusterRedis.opsForZSet().incrementScore(key, uid, score);
            String nowStr = DateHelper.ARABIAN.formatDateInDay();
            if (!nowStr.equals(rankDateStr)) {
                rankDateStr = nowStr;
                clusterRedis.expire(key, ExpireTimeConstant.EXPIRE_DAYS_THIRTY, TimeUnit.DAYS);
            }
        } catch (Exception e) {
            logger.info("incrCommonZSetRankingScoreSimple error weekStart={} aid={} giftId={} score={}", weekStart, uid, score, e.getMessage(), e);
        }
    }

    /**
     * 获取排名，返回的数据=redis的排行+1
     */
    public int getCommonZSetRank(String weekStart, String uid) {
        try {
            String key = getCommonZSetKey(weekStart);
            Long rank = clusterRedis.opsForZSet().reverseRank(key, uid);
            return rank != null ? rank.intValue() + 1 : 0;
        } catch (Exception e) {
            logger.info("getCommonZSetRank error weekStart={} aid={}", weekStart, uid, e);
            return 0;
        }
    }


    /**
     * 获取带分数排行榜
     * 倒序
     */
    public Map<String, Integer> getCommonRankingMap(String weekStart, int length) {
        Map<String, Integer> linkedRankMap = new LinkedHashMap<>();
        String key = getCommonZSetKey(weekStart);
        Set<ZSetOperations.TypedTuple<String>> rangeWithScores = clusterRedis.opsForZSet().reverseRangeWithScores(key, 0, length - 1);
        if (null == rangeWithScores) {
            return linkedRankMap;
        }
        for (ZSetOperations.TypedTuple<String> rangeWithScore : rangeWithScores) {
            if (null == rangeWithScore.getValue() || null == rangeWithScore.getScore()) {
                continue;
            }
            linkedRankMap.put(rangeWithScore.getValue(), rangeWithScore.getScore().intValue());
        }
        return linkedRankMap;
    }


    public void addRollListRecord(StarBeatRedisData data) {
        String key = getRollListKey();
        String json = JSON.toJSONString(data);
        if (!StringUtils.isEmpty(json)) {
            Long listSize = clusterRedis.opsForList().leftPush(key, json);
            if (null != listSize && listSize > ROLL_MAX_SIZE) {
                clusterRedis.opsForList().trim(key, 0, ROLL_MAX_SIZE);
            }
        }
        String nowStr = DateHelper.ARABIAN.formatDateInDay();
        if (!nowStr.equals(rollDateStr)) {
            rollDateStr = nowStr;
            clusterRedis.expire(key, ExpireTimeConstant.EXPIRE_DAYS_THIRTY, TimeUnit.DAYS);
        }
    }

    @Cacheable(value = "cache", cacheManager = CaffeineCacheConfig.EXPIRE_1M_AFTER_WRITE,
            key = "#root.method.name+T(org.springframework.util.StringUtils).arrayToDelimitedString(args,'-')")
    public List<StarBeatRedisData> getShowRollListRecord(int size) {
        try {
            String key = getRollListKey();
            List<String> jsonList = clusterRedis.opsForList().range(key, 0, -1);
            if (CollectionUtils.isEmpty(jsonList)) {
                return Collections.emptyList();
            }
            List<StarBeatRedisData> resultList = new ArrayList<>();
            Set<String> allUid = new HashSet<>();
            int count = 0;
            for (String json : jsonList) {
                StarBeatRedisData rewardData = JSON.parseObject(json, StarBeatRedisData.class);
                if (!allUid.contains(rewardData.getUid())) {
                    resultList.add(rewardData);
                    allUid.add(rewardData.getUid());
                    count++;
                    if (count >= size) {
                        break;
                    }
                }
            }
            return resultList;
        } catch (Exception e) {
            logger.error("getShowRollListRecord error", e);
            return Collections.emptyList();
        }
    }


    public void leftPushAllHistoryList(String uid, List<StarBeatRedisData> dataList) {
        String key = getHistoryListKey(uid);

        List<String> strList = new ArrayList<>();
        for (StarBeatRedisData data : dataList) {
            strList.add(JSONObject.toJSONString(data));
        }
        if (CollectionUtils.isEmpty(strList)) {
            return;
        }
        Long listSize = clusterRedis.opsForList().leftPushAll(key, strList);
        if (null != listSize && listSize > HISTORY_MAX_SIZE) {
            clusterRedis.opsForList().trim(key, 0, HISTORY_MAX_SIZE);
        }
        String nowStr = DateHelper.ARABIAN.formatDateInDay();
        String historyDateStr = historyDateCacheMap.getData(uid);
        if (!nowStr.equals(historyDateStr)) {
            historyDateCacheMap.cacheData(uid,nowStr);
            clusterRedis.expire(key, ExpireTimeConstant.EXPIRE_DAYS_THIRTY, TimeUnit.DAYS);
        }
    }


    public List<StarBeatRedisData> getHistoryListPageRecord(String uid, int start, int end) {
        try {
            String key = getHistoryListKey(uid);
            List<String> jsonList = clusterRedis.opsForList().range(key, start, end - 1);
            if (CollectionUtils.isEmpty(jsonList)) {
                return Collections.emptyList();
            }
            List<StarBeatRedisData> resultList = new ArrayList<>();
            for (String json : jsonList) {
                StarBeatRedisData rewardData = JSON.parseObject(json, StarBeatRedisData.class);
                resultList.add(rewardData);
            }
            return resultList;
        } catch (Exception e) {
            logger.error("getHistoryListPageRecord error", e);
            return Collections.emptyList();
        }
    }


    private String getCommonZSetKey(String weekStart) {
        return String.format("zset:star:beat:rank:%s", weekStart);
    }


    private String getStarUidKey(String uid, int type) {
        return "str:star:beat:star:" + uid + ":type:" + type;
    }


    private String getRollListKey() {
        return "list:star:beat:roll:key";
    }

    private String getHistoryListKey(String uid) {
        return String.format("list:star:beat:history:%s", uid);
    }
}
