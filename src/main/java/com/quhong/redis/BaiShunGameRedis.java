package com.quhong.redis;

import com.quhong.constant.ExpireTimeConstant;
import com.quhong.core.utils.DateHelper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.ZSetOperations;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.LinkedHashMap;
import java.util.Map;
import java.util.Set;
import java.util.concurrent.TimeUnit;

/**
 * <AUTHOR>
 * @date 2023/8/1
 */
@Component
public class BaiShunGameRedis {

    private static final Logger logger = LoggerFactory.getLogger(BaiShunGameRedis.class);

    @Resource(name = DataRedisBean.MAIN_CLUSTER)
    private StringRedisTemplate clusterTemplate;

    private String expireDate = "";
    private String waringExpireDate = "";

    public boolean checkSignatureNonce(String signatureNonce) {
        try {
            return Boolean.TRUE.equals(clusterTemplate.opsForSet().isMember(getSignatureNonceKey(), signatureNonce));
        } catch (Exception e) {
            logger.error("check signature nonce error. signatureNonce={} {}", signatureNonce, e.getMessage(), e);
            return false;
        }
    }

    public void saveSignatureNonce(String signatureNonce) {
        String key = getSignatureNonceKey();
        try {
            clusterTemplate.opsForSet().add(key, signatureNonce);
            String dateStr = DateHelper.DEFAULT.formatDateInDay();
            if (!dateStr.equals(expireDate)) {
                clusterTemplate.expire(key, ExpireTimeConstant.EXPIRE_DAYS_ONE, TimeUnit.DAYS);
                expireDate = dateStr;
            }
        } catch (Exception e) {
            logger.error("save order id error. signatureNonce={} {}", signatureNonce, e.getMessage(), e);
        }
    }

    public int incPlatformBeansChange(int gameId, int change) {
        String key = getPlatformBeansChangeKey(gameId);
        try {
            Long afterValue = clusterTemplate.opsForValue().increment(key, change);
            clusterTemplate.expire(key, ExpireTimeConstant.EXPIRE_DAYS_ONE, TimeUnit.DAYS);
            return afterValue != null ? afterValue.intValue() : 0;
        } catch (Exception e) {
            logger.error("incPlatformBeansChange error. gameId={} change={} {}", gameId, change, e.getMessage(), e);
            return 0;
        }
    }

    public int incUserBeansChange(String gameId, String uid, int change) {
        String key = getUserBeansChangeKey(gameId);
        try {
            Double afterValue = clusterTemplate.opsForZSet().incrementScore(key, uid, change);
            clusterTemplate.expire(key, ExpireTimeConstant.EXPIRE_DAYS_ONE, TimeUnit.DAYS);
            return afterValue != null ? afterValue.intValue() : 0;
        } catch (Exception e) {
            logger.error("incUserBeansChange error. uid={} change={} {}", uid, change, e.getMessage(), e);
            return 0;
        }
    }

    /**
     * 获取带分数排行榜
     */
    public Map<String, Integer> getUserProfitRankingMap(String gameId, int length) {
        Map<String, Integer> linkedRankMap = new LinkedHashMap<>();
        String key = getUserBeansChangeKey(gameId);
        Set<ZSetOperations.TypedTuple<String>> rangeWithScores = clusterTemplate.opsForZSet().reverseRangeWithScores(key, 0, length - 1);
        if (null == rangeWithScores) {
            return linkedRankMap;
        }
        for (ZSetOperations.TypedTuple<String> rangeWithScore : rangeWithScores) {
            if (null == rangeWithScore.getValue() || null == rangeWithScore.getScore()) {
                continue;
            }
            linkedRankMap.put(rangeWithScore.getValue(), rangeWithScore.getScore().intValue());
        }
        return linkedRankMap;
    }

    public int getPlatformWarnLevel(String gameId) {
        try {
            String value = clusterTemplate.opsForValue().get(getPlatformWarnLevelKey(gameId));
            return StringUtils.isEmpty(value) ? 0 : Integer.parseInt(value);
        } catch (Exception e) {
            logger.error("getPlatformWarnLevel error. gameId={} {}", gameId, e.getMessage(), e);
            return 0;
        }
    }

    public void setPlatformWarnLevel(String gameId, int warnLevel) {
        String key = getPlatformWarnLevelKey(gameId);
        try {
            clusterTemplate.opsForValue().set(key, warnLevel + "", ExpireTimeConstant.EXPIRE_DAYS_THIRTY, TimeUnit.DAYS);
        } catch (Exception e) {
            logger.error("setPlatformWarnLevel error. gameId={} warnLevel={} {}", gameId, warnLevel, e.getMessage(), e);
        }
    }

    public int getPersonalWarnLevel(String uid) {
        try {
            String dateStr = DateHelper.ARABIAN.formatDateInDay();
            String key = getPersonalWarnLevelKey(dateStr);
            String value = (String) clusterTemplate.opsForHash().get(key, uid);
            return StringUtils.isEmpty(value) ? 0 : Integer.parseInt(value);
        } catch (Exception e) {
            logger.error("getPersonalWarnLevel error. uid={} {}", uid, e.getMessage(), e);
            return 0;
        }
    }

    public void setPersonalWarnLevel(String uid, int waringLevel) {
        try {
            String dateStr = DateHelper.ARABIAN.formatDateInDay();
            String key = getPersonalWarnLevelKey(dateStr);
            clusterTemplate.opsForHash().put(key, uid, String.valueOf(waringLevel));
            if (!dateStr.equals(waringExpireDate)) {
                clusterTemplate.expire(key, ExpireTimeConstant.EXPIRE_DAYS_ONE, TimeUnit.DAYS);
                waringExpireDate = dateStr;
            }
        } catch (Exception e) {
            logger.error("setPersonalWarnLevel error. uid={}", uid, e);
        }
    }

    private String getSignatureNonceKey() {
        return "set:baiShunSignatureNonce_" + DateHelper.ARABIAN.formatDateInDay();
    }

    private String getPlatformBeansChangeKey(int gameId) {
        return "str:platformBeansChange:" + gameId;
    }

    private String getUserBeansChangeKey(String gameId) {
        return "zset:UserBeansChange:" + DateHelper.ARABIAN.formatDateInDay() + "_"  + gameId;
    }

    private String getPlatformWarnLevelKey(String gameId) {
        return "str:platformWarnLevel:" + DateHelper.ARABIAN.formatDateInDay() + "_" + gameId;
    }

    private String getPersonalWarnLevelKey(String strDate) {
        return "hash:personalWarnLevel:" + strDate;
    }
}
