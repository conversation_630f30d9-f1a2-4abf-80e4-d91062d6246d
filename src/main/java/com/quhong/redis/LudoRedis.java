package com.quhong.redis;

import com.alibaba.fastjson.JSON;
import com.quhong.ludo.data.GameInfo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.TimeUnit;

@Component
public class LudoRedis {
    private static final Logger logger = LoggerFactory.getLogger(LudoRedis.class);

    private static final int TIME_OUT = 2;

    @Resource(name = DataRedisBean.MAIN_CLUSTER)
    private StringRedisTemplate redisTemplate;

    public String getPlayerData(String uid) {
        String gameId = (String) redisTemplate.opsForHash().get(getLudoPlayerKey(), uid);
        if (StringUtils.isEmpty(gameId)) {
            return null;
        }
        return gameId;
    }

    public void savePlayerData(String uid, String gameId) {
        try {
            redisTemplate.opsForHash().put(getLudoPlayerKey(), uid, gameId);
        } catch (Exception e) {
            logger.error("save player data error. {}", e.getMessage(), e);
        }
    }

    public void removePlayerData(String uid) {
        try {
            redisTemplate.opsForHash().delete(getLudoPlayerKey(), uid);
        } catch (Exception e) {
            logger.error("remove player data error. uid={}", uid, e);
        }
    }

    public List<GameInfo> getAllGameInfo() {
        try {
            List<GameInfo> gameInfoList = new ArrayList<>();
            Map<Object, Object> entries = redisTemplate.opsForHash().entries(getGameInfoKey());
            for (Object gameInfo : entries.values()) {
                gameInfoList.add(JSON.parseObject((String) gameInfo, GameInfo.class));
            }
            return gameInfoList;
        } catch (Exception e) {
            logger.error("get all game info error.", e);
        }
        return new ArrayList<>();
    }

    public String getGameIdByRoomId(String roomId) {
        String gameId = redisTemplate.opsForValue().get(getRoomLudoKey(roomId));
        if (StringUtils.isEmpty(gameId)) {
            return null;
        }
        return gameId;
    }

    public void saveGameInfo(GameInfo gameInfo) {
        try {
            String json = JSON.toJSONString(gameInfo);
            redisTemplate.opsForSet().add(getLudoRoomSetKey(), gameInfo.getRoomId());
            redisTemplate.opsForHash().put(getGameInfoKey(), gameInfo.getGameId(), json);
            redisTemplate.opsForValue().set(getRoomLudoKey(gameInfo.getRoomId()), gameInfo.getGameId(), TIME_OUT, TimeUnit.DAYS);
        } catch (Exception e) {
            logger.error("save game info error. {}", e.getMessage(), e);
        }
    }

    public void removeGameInfo(String gameId, String roomId) {
        redisTemplate.opsForHash().delete(getGameInfoKey(), gameId);
        redisTemplate.opsForSet().remove(getLudoRoomSetKey(), roomId);
    }

    public void removeRoomLudo(String roomId) {
        redisTemplate.delete(getRoomLudoKey(roomId));
    }

    private String getLudoPlayerKey() {
        return "ludo_player";
    }

    private String getGameInfoKey() {
        return "ludo_game_info";
    }

    /**
     * 用于判断房间内是否有ludo游戏
     */
    private String getRoomLudoKey(String roomId) {
        return "room_ludo_" + roomId;
    }

    /**
     * 用于判断房间内是否有ludo游戏
     */
    private String getLudoRoomSetKey() {
        return "set:ludoRoom";
    }
}
