package com.quhong.redis;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.quhong.core.utils.DateHelper;
import com.quhong.vo.RecentPrizeVo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.data.redis.core.ZSetOperations;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.TimeUnit;

@Component
public class CityDiggerRedis {
    private static final Logger logger = LoggerFactory.getLogger(CityDiggerRedis.class);

    private static final int TIME_OUT = 15 * 60;

    @Resource(name = DataRedisBean.MAIN_CLUSTER)
    private StringRedisTemplate redisTemplate;
    private String expireDate = "";


    public void cacheRecentPrize(List<RecentPrizeVo> list) {
        try {
            String key = getKey();
            String json = JSON.toJSONString(list);
            redisTemplate.opsForValue().set(key, json, TIME_OUT, TimeUnit.SECONDS);
        } catch (Exception e) {
            logger.error("cache recent prize error. {}", e.getMessage(), e);
        }
    }

    public List<RecentPrizeVo> getRecentPrizeCache() {
        try {
            String key = getKey();
            String json = redisTemplate.opsForValue().get(key);
            if (StringUtils.isEmpty(json)) {
                return null;
            }
            List<RecentPrizeVo> retList = new ArrayList<>();
            JSONArray jsonArray = JSON.parseArray(json);
            for (Object element : jsonArray) {
                RecentPrizeVo recordData = ((JSONObject) element).toJavaObject(RecentPrizeVo.class);
                retList.add(recordData);
            }
            return retList;
        } catch (Exception e) {
            logger.error("get recent prize from cache error. {}", e.getMessage(), e);
        }
        return null;
    }

    public void deleteRecentPrize() {
        try {
            redisTemplate.delete(getKey());
        } catch (Exception e) {
            logger.error("delete recent prize cache error. {}", e.getMessage(), e);
        }
    }

    private String getKey() {
        return "game_recent_prize";
    }

    /**
     * 获取前20的日排行
     */
    public Map<String, Long> getRanking() {
        Map<String, Long> linkedRankMap = new LinkedHashMap<>();
        String dateStr = DateHelper.DEFAULT.formatDateInDay();
        String key = getRankKey(dateStr);
        Set<ZSetOperations.TypedTuple<String>> rangeWithScores = redisTemplate.opsForZSet().reverseRangeWithScores(key, 0, 19);
        if (null == rangeWithScores) {
            return linkedRankMap;
        }
        for (ZSetOperations.TypedTuple<String> rangeWithScore : rangeWithScores) {
            if (null == rangeWithScore.getValue() || null == rangeWithScore.getScore()) {
                continue;
            }
            linkedRankMap.put(rangeWithScore.getValue(), rangeWithScore.getScore().longValue());
        }
        return linkedRankMap;
    }

    public void logRanking(String uid, int increment) {
        try {
            String dateStr = DateHelper.DEFAULT.formatDateInDay();
            String key = getRankKey(dateStr);
            redisTemplate.opsForZSet().incrementScore(key, uid, increment);
            if (!dateStr.equals(expireDate)) {
                redisTemplate.expire(key, 3, TimeUnit.DAYS);
                expireDate = dateStr;
            }
        } catch (Exception e) {
            logger.error("cache recent prize error. {}", e.getMessage(), e);
        }
    }

    private String getRankKey(String dateStr) {
        return "city_rank_" + dateStr;
    }
}
