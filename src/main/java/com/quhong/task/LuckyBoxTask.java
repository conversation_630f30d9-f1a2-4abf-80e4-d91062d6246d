package com.quhong.task;

import com.quhong.config.AsyncConfig;
import com.quhong.core.config.ServerConfig;
import com.quhong.core.utils.DateHelper;
import com.quhong.mongo.dao.LuckyBoxDailyNumDao;
import com.quhong.mongo.dao.SudGameDao;
import com.quhong.utils.K8sUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2023/2/9
 */
@Component
public class LuckyBoxTask {

    private final static Logger logger = LoggerFactory.getLogger(LuckyBoxTask.class);

    /**
     * 每天清理10天前的数据
     */
    private static final int TEN_DAYS = 10 * 24 * 3600;

    private static final int D_DAYS = 30 * 24 * 3600;

    private static final int D2_DAYS = 31 * 24 * 3600;
    @Resource
    private K8sUtils k8sUtils;
    @Resource
    private LuckyBoxDailyNumDao luckyBoxDailyNumDao;
    @Resource
    private SudGameDao sudGameDao;

    /**
     * 每天北京时间：11:00 执行一次
     */
    @Async(AsyncConfig.ASYNC_TASK)
    @Scheduled(cron = "30 0 3 * * ?")
    public void deleteDailyRecord() {
        if (k8sUtils.isMasterFromCache()) {
            long timeMills = System.currentTimeMillis();
            logger.info("start delete luckyBoxDailyNumData.");
            luckyBoxDailyNumDao.batchDelete(DateHelper.getNowSeconds() - TEN_DAYS);
            logger.info("delete luckyBoxDailyNumData end. cost={}", System.currentTimeMillis() - timeMills);
            int start = DateHelper.getNowSeconds() - D2_DAYS;
            int end = DateHelper.getNowSeconds() - D_DAYS;
            sudGameDao.batchDelete(start, end);
        }
    }
}
