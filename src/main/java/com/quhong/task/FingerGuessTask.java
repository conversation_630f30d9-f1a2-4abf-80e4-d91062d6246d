package com.quhong.task;

import com.quhong.config.AsyncConfig;
import com.quhong.service.FingerGuessService;
import com.quhong.utils.K8sUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.annotation.Async;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2022/11/8
 */
@Component
public class FingerGuessTask {

    private final static Logger logger = LoggerFactory.getLogger(FingerGuessTask.class);

    @Resource
    private K8sUtils k8sUtils;
    @Resource
    private FingerGuessService fingerGuessService;

    /**
     * 重置榜单 （每周日00:00 GMT+3执行）
     */
    @Async(AsyncConfig.ASYNC_TASK)
    @Scheduled(cron = "0 0 21 ? * 7")
    public void clearRanking() {
        logger.info("start clear finger guess ranking.");
        if (k8sUtils.isMasterFromCache()) {
            fingerGuessService.clearRanking();
        }
    }
}
