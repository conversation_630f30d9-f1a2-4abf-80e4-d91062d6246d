package com.quhong.enums;

import lombok.Getter;

/**
 * 返回码枚举定义
 *
 * <AUTHOR>
 */
public enum RetCodeEnum {

    /**
     * 成功
     */
    SUCCESS(0, "成功"),

    /**
     * 失败
     */
    REQUEST_FAILED(1, "失败"),

    /**
     * 用户不存在
     */
    NOT_FIND_USER_INFO(2, "用户不存在");

    @Getter
    private final String name;

    @Getter
    private final int index;

    RetCodeEnum(int index, String name) {
        this.name = name;
        this.index = index;
    }


}
