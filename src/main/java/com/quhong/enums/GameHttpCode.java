package com.quhong.enums;


public class GameHttpCode extends HttpCode {
    public static final HttpCode SUCCESS = new HttpCode(200, "success");
    public static final HttpCode CREATE_GAME_SUCCESS = new HttpCode(0, "create.game.success");
    public static final HttpCode JOIN_GAME_SUCCESS = new HttpCode(0, "join.game.success");
    ;
    public static final HttpCode CREATE_ROSHAMBO_GAME_SUCCESS = new HttpCode(0, "create.roshambo.game.success");
    public static final HttpCode FAILURE = new HttpCode(1, "server error");
    public static final HttpCode NEED_UPGRADE = new HttpCode(1, "need_upgrade");
    public static final HttpCode NOT_ENOUGH_HEART = new HttpCode(3001, "game.player.not.enough.heart");
    public static final HttpCode NOT_ENOUGH_COIN = new HttpCode(55, "not_enough_coin");
    public static final HttpCode NOT_ENOUGH_DIAMOND = new HttpCode(50, "");
    public static final HttpCode NOT_IN_MIC = new HttpCode(3003, "not_in_mic");
    public static final HttpCode PLAYER_NOT_FOUND = new HttpCode(3004, "");
    public static final HttpCode PLAYER_IN_GAME_C = new HttpCode(3006, "game.player.in.game.create");
    public static final HttpCode PLAYER_IN_GAME_J = new HttpCode(3006, "game.player.in.game.join");
    public static final HttpCode GAME_RUNNING = new HttpCode(3007, "game_running");
    public static final HttpCode PLAYER_NOT_READY = new HttpCode(3008, "");
    public static final HttpCode GAME_CLOSED = new HttpCode(3009, "game_closed");
    public static final HttpCode PLAYER_FULL = new HttpCode(3010, "player_full");
    public static final HttpCode GAME_NOT_FOUND = new HttpCode(3011, "");
    public static final HttpCode ONE_GAME = new HttpCode(3014, "");
    public static final HttpCode ROOM_OWNER = new HttpCode(3015, "game.room.owner");
    public static final HttpCode LEVEL_LIMIT = new HttpCode(3016, "game.level.limit");
    public static final HttpCode NOT_ROOM_OWNER = new HttpCode(3016, "game.not.room.owner");
    public static final HttpCode NOT_THE_ROOM_MEMBER = new HttpCode(3017, "game.not.room.member");
    public static final HttpCode NOT_THE_ROOM_ADMIN = new HttpCode(3018, "game.not.room.admin");
    public static final HttpCode NOT_THE_ROOM_OWNER = new HttpCode(3019, "game.only.room.owner");
    public static final HttpCode EXIST_LUDO_GAME = new HttpCode(3021, "game.exist.ludo.game");
    public static final HttpCode EXIST_UMO_GAME = new HttpCode(3022, "game.exist.umo.game");
    public static final HttpCode EXIST_MONSTER_CRUSH_GAME = new HttpCode(3023, "game.exist.monster.crush.game");
    public static final HttpCode EXIST_DOMINO_GAME = new HttpCode(3024, "game_exist_domino_game");
    public static final HttpCode OFFLINE = new HttpCode(1, "game.ludo.upgrading");
    public static final HttpCode CITY_OFFLINE = new HttpCode(1, "game.city.offline");
    public static final HttpCode OWNER_AND_ADMIN_CAN_CREATE = new HttpCode(3023, "owner_and_admin_can_create");
    public static final HttpCode GAME_HAS_ALREADY_EXISTED = new HttpCode(101, "game_has_already_existed");
    public static final HttpCode DIAMOND_NOT_ENOUGH = new HttpCode(50, "diamond_not_enough");
    public static final HttpCode GAME_HAS_BEEN_CANCELLED = new HttpCode(3, "game_has_been_cancelled");
    public static final HttpCode PLAYERS_NUM_REACH_MAX = new HttpCode(3024, "players_num_reach_max");
    public static final HttpCode YOU_HAVE_JOINED_THE_GAME = new HttpCode(3025, "you_have_joined_the_game");
    public static final HttpCode GAME_HAS_BEEN_CREATED = new HttpCode(3026, "game_has_been_created");
    public static final HttpCode WAIT_OTHER_PLAYERS_JOINING = new HttpCode(3027, "wait_other_players_joining");
    public static final HttpCode THE_GAME_IS_RUNNING = new HttpCode(3028, "the_game_is_running");
    public static final HttpCode THE_GAME_IS_NOT_FOUND = new HttpCode(3029, "the_game_is_not_found");
    public static final HttpCode THE_ACTOR_NOT_FOUND = new HttpCode(3030, "the_actor_not_found");
    public static final HttpCode YOU_ARE_NOT_THE_CREATOR = new HttpCode(3031, "you_are_not_the_creator");
    public static final HttpCode THE_GAME_IS_END = new HttpCode(3032, "the_game_is_end");
    public static final HttpCode THE_GAME_CANNOT_CREATE = new HttpCode(3033, "the_game_cannot_create");
    public static final HttpCode THE_GAME_STATUS_ERROR = new HttpCode(3034, "the_game_status_error");
    public static final HttpCode THE_GAME_YOU_SELECTED_MODE = new HttpCode(3034, "the_game_you_selected_mode");
    public static final HttpCode THE_ROOM_NOT_TRUTH_DARE_GAME = new HttpCode(3035, "The room not truth or dare", "The room not truth or dare");
    public static final HttpCode THE_TRUTH_GAME_IS_RUNNING = new HttpCode(3036, "the_truth_game_is_running");
    public static final HttpCode THE_TRUTH_GAME_NEED_ON_MIC = new HttpCode(3037, "the_truth_game_need_on_mic");
    public static final HttpCode THE_TRUTH_GAME_NEED_ON_ORDER_MIC = new HttpCode(3038, "the_truth_game_need_on_order_mic");
    public static final HttpCode THE_GAME_NOT_SUPPORT_LIVE = new HttpCode(3038, "the_game_not_support_live");
    public static final HttpCode THE_GAME_MIC_SIZE_ERROR = new HttpCode(3038, "the_game_mic_size_error");
    public static final HttpCode STARTED_BY_OTHERS = new HttpCode(1, "started_by_others");
    public static final HttpCode GAME_HAS_EXPIRED = new HttpCode(2, "game_has_expired");
    public static final HttpCode CAN_NOT_JOIN_YOUR_OWN_GAME = new HttpCode(3, "can_not_join_your_own_game");
    public static final HttpCode HAVE_BEEN_IN_PK = new HttpCode(4, "have_been_in_pk");
    public static final HttpCode PK_IS_ONGOING_NOW = new HttpCode(5, "pk_is_ongoing_now");
    public static final HttpCode PK_FUNCTION_DISABLED = new HttpCode(6, "pk_function_disabled");
    public static final HttpCode CREATE_PK_FAILED = new HttpCode(7, "create_pk_failed");
    public static final HttpCode GAME_NOT_EXIST = new HttpCode(8, "game_not_exist");
    public static final HttpCode THE_INVITED_IS_IN_PK = new HttpCode(9, "the_invited_is_in_pk");
    public static final HttpCode HAVE_INVITED_HIM = new HttpCode(10, "have_invited_him");
    public static final HttpCode INVITE_SUCCESS = new HttpCode(0, "invite_success");
    public static final HttpCode CANCEL_PK_SUCCESS = new HttpCode(0, "cancel_pk_success");
    public static final HttpCode PK_GAME_IS_RUNNING = new HttpCode(11, "pk_game_is_running");
    public static final HttpCode CAN_NOT_CANCEL_PK_GAME = new HttpCode(12, "can_not_cancel_pk_game");
    public static final HttpCode PK_GAME_NOT_EXIST = new HttpCode(13, "pk_game_not_exist");
    public static final HttpCode PK_GAME_HAS_BEEN_STARTED = new HttpCode(14, "pk_game_has_been_started");
    public static final HttpCode JOIN_PK_GAME_SUCCESS = new HttpCode(15, "join_pk_game_success");
    public static final HttpCode VIP3_OR_HIGHER_CAN_SEND = new HttpCode(41, "vip3_or_higher_can_send");
    public static final HttpCode MSG_TOO_LONG = new HttpCode(21, "msg_too_long");
    public static final HttpCode NOT_MONEY_OR_NUMBER = new HttpCode(22, "not_money_or_number");
    public static final HttpCode BOXES_NUMBER_RANGE = new HttpCode(23, "boxes_number_range");
    public static final HttpCode DIAMONDS_NUMBER_RANGE = new HttpCode(24, "diamonds_number_range");
    public static final HttpCode CAN_NOT_GET_DIAMONDS = new HttpCode(41, "can_not_get_diamonds");
    public static final HttpCode QUOTA_HAS_BEEN_USED_UP = new HttpCode(99, "quota_has_been_used_up");
    public static final HttpCode NO_DIAMONDS_REMAINING = new HttpCode(41, "no_diamonds_remaining");
    public static final HttpCode CAN_NOT_GET_YOURSELF_BOX = new HttpCode(51, "can_not_get_yourself_box");
    public static final HttpCode CAN_NOT_GET_BOX_AGAIN = new HttpCode(51, "can_not_get_box_again");
    public static final HttpCode CAN_NOT_START_THE_GAME = new HttpCode(3032, "can_not_start_the_game");
    public static final HttpCode BOX_INVALID = new HttpCode(3033, "box_invalid");
    public static final HttpCode NOT_RECEIVE_THE_REWARD = new HttpCode(3034, "not_receive_the_reward");
    public static final HttpCode ONLY_ROOM_OWNER_CAN_CANCEL_PK_GAME = new HttpCode(20, "only_room_owner_can_cancel_pk_game");
    public static final HttpCode PLEASE_ENTER_USER_ID = new HttpCode(20, "please_enter_user_id");
    public static final HttpCode USER_NOT_EXISTING = new HttpCode(20, "user_not_existing");
    public static final HttpCode THIS_IS_NOT_YOUR_FRIEND = new HttpCode(20, "this_is_not_your_friend");
    public static final HttpCode YOUR_FRIEND_HAS_NOT_CREATED_ROOM = new HttpCode(20, "your_friend_has_not_created_room");
    public static final HttpCode ROOM_IS_LOCKED_CANNOT_SEND_LUCKY_BOX = new HttpCode(21, "room_is_locked_cannot_send_lucky_box");
    public static final HttpCode YOU_NEED_TO_CREATE_ROOM_FIRST = new HttpCode(20, "you_need_to_create_room_first");
    public static final HttpCode PK_HAS_BEEN_STARTED_OR_CANCELLED = new HttpCode(20, "pk_has_been_started_or_cancelled");
    public static final HttpCode PLEASE_WAIT_OTHER_PLAYERS_JOINING = new HttpCode(58, "please_wait_other_players_joining");
    public static final HttpCode SMART_WHEEL_IS_ONGOING = new HttpCode(102, "smart_wheel_is_ongoing");
    public static final HttpCode ROOM_IS_BLOCKED_CANNOT_SEND_LUCKY_BOX = new HttpCode(22, "room_is_blocked_cannot_send_lucky_box");
    public static final HttpCode PLAYERS_NUM_IS_FULL = new HttpCode(103, "players_num_is_full");
    public static final HttpCode PLEASE_UP_MIC_FIRST = new HttpCode(104, "please_up_mic_first");
    public static final HttpCode EXIST_GAME = new HttpCode(3100, "exist_game");


    public static final HttpCode GAME_ROOM_NOT_HAVE_GAME = new HttpCode(4001, "game_room_not_have_game");
    public static final HttpCode GAME_ROOM_DEDUCT_ERROR = new HttpCode(4002, "game_room_deduct_error");
    public static final HttpCode GAME_START_ERROR = new HttpCode(4003, "game is processing start error");
    public static final HttpCode GAME_CREATE_GAME = new HttpCode(60, "please_create_new_game_room");
    public static final HttpCode GAME_TRUTH_DARE_VIDEO_CLOSE = new HttpCode(4001, "game_truth_dare_video_close");
    public static final HttpCode GAME_TRUTH_DARE_LEAST_TOPIC = new HttpCode(4004, "game_truth_dare_least_topic");
    public static final HttpCode GAME_TRUTH_DARE_MAX_TOPIC = new HttpCode(4005, "game_truth_dare_max_topic");
    public static final HttpCode GAME_TRUTH_DARE_SHOULD_SELECT_TRUTH = new HttpCode(4006, "game_truth_dare_should_select_truth");
    public static final HttpCode GAME_TRUTH_DARE_SHOULD_SELECT_DARE = new HttpCode(4007, "game_truth_dare_should_select_dare");
    public static final HttpCode PLEASE_UPGRADE_VERSION = new HttpCode(126, "please_upgrade_version");
    public static final HttpCode MIC_THEME_OPT_NOT_COMP = new HttpCode(124, "mic_theme_opt_not_comp");
    public static final HttpCode PLEASE_CLOSE_VIDEO = new HttpCode(133, "please_close_video");
    public static final HttpCode AUTH_ERROR = new HttpCode(2001, "No right to operate", "لا تستطيع لتشفيل");
    public static final HttpCode ROOM_HAS_BEEN_CONQUERED = new HttpCode(60, "room_has_been_conquered");
    public static final HttpCode MIC_THEME_OPT_AUTH_3 = new HttpCode(123, "mic_theme_opt_auth_3");
    public static final HttpCode MIC_THEME_OPT_AUTH_QUEEN = new HttpCode(124, "mic_theme_opt_auth_queen");
    public static final HttpCode MIC_THEME_OPT_AUTH_ERROR = new HttpCode(123, "mic_theme_opt_auth_error");

    public GameHttpCode(int code, String... langMsg) {
        super(code, langMsg);
    }

}
