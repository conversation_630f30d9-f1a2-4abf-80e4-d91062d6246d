package com.quhong.config;

import com.quhong.intercepters.H5Interceptor;
import com.quhong.intercepters.H5InterceptorConfig;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistration;
import org.springframework.web.servlet.config.annotation.InterceptorRegistry;

import java.util.*;

/**
 * 拦截内容
 */
@Configuration
public class GameInterceptorConfig extends H5InterceptorConfig {

    @Value("${baseUrl}")
    private String baseUrl;

    @Override
    public void addInterceptors(InterceptorRegistry registry) {
        InterceptorRegistration registration = registry.addInterceptor(new H5Interceptor(getRequestWarnDurationMap()));
        List<String> excludePathList = new ArrayList<>(getExcludePaths());
        registration.addPathPatterns(baseUrl + "/**");
        registration.addPathPatterns("/java_game/sud/matching");
        excludePathList.add("/system/**");
        excludePathList.add(baseUrl + "/active");
        excludePathList.add(baseUrl + "/healthy");
        excludePathList.add("inner" + baseUrl + "/healthy");
        excludePathList.add("inner" + baseUrl + "/active");
        registration.excludePathPatterns(excludePathList);
    }


    @Override
    protected List<String> getExcludePaths() {
        return Arrays.asList(baseUrl + "sud_game/**"
                , baseUrl + "quhong/**"
                , baseUrl + "bai_shun_game/get_sstoken"
                , baseUrl + "bai_shun_game/update_sstoken"
                , baseUrl + "bai_shun_game/get_user_info"
                , baseUrl + "bai_shun_game/change_balance");
    }

    protected Map<String, Long> getRequestWarnDurationMap() {
        Map<String, Long> map = new HashMap<>();
        map.put("/game/sud/start_game", 5000L);
        return map;
    }
}
