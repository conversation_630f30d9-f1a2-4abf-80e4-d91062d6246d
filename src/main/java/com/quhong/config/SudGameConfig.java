package com.quhong.config;

import com.quhong.core.config.ServerConfig;
import com.quhong.enums.SLangType;
import com.quhong.ludo.data.CurrencyInfo;
import com.quhong.sud.data.SudGameConfigInfo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.PropertySource;
import org.springframework.stereotype.Component;

import java.util.Collections;
import java.util.List;
import java.util.Map;

/**
 * 即构小游戏配置
 *
 * <AUTHOR>
 * @date 2022/6/30
 */
@Component
@PropertySource(value = "classpath:sud_game.yml", encoding = "UTF-8", factory = YamlPropertySourceFactory.class)
@ConfigurationProperties(prefix = "config")
public class SudGameConfig {

    private static final Logger logger = LoggerFactory.getLogger(SudGameConfig.class);

    private String appId;
    private String appKey;
    private String appSecret;
    /**
     * 获取即构服务端接口API
     */
    private String getSudApiUrl;
    private String getSudApiUrlProd;

    /**
     * 游戏
     */
    private Map<Integer, SudGameConfigInfo> sudGameInfoMap;

    /**
     * Ludo游戏旧版入场费用（不支持钻石版本）
     */
    private Map<Integer, CurrencyInfo> ludoCurrencyInfoMap;

    /**
     * 通过游戏类型和入场费id获取入参费
     */
    public int getCurrencyByGameTypeAndId(int gameType, int id) {
        try {
            return sudGameInfoMap.get(gameType).getCurrencyInfoMap().get(id).getCurrencyValue();
        } catch (Exception e) {
            logger.error("get currency by id error id={} error message={}", id, e.getMessage());
        }
        return 0;
    }

    public List<Integer> getNewCurrencyList(int gameType, int currencyType) {
        try {
            if (currencyType == 1) {
                return sudGameInfoMap.get(gameType).getCoinFeeList();
            } else {
                return sudGameInfoMap.get(gameType).getDiamondFeeList();
            }
        } catch (Exception e) {
            logger.error("getNewCurrencyList error. gameType={} currencyType={} message={}", gameType, currencyType, e.getMessage());
            return Collections.emptyList();
        }
    }

    /**
     * 机器人获取最低入场费
     */
    public int getLowestCoinCurrency(int gameType) {
        int result = 0;
        Map<Integer, CurrencyInfo> currencyInfoMap = sudGameInfoMap.get(gameType).getCurrencyInfoMap();
        for (CurrencyInfo currencyInfo : currencyInfoMap.values()) {
            if (currencyInfo.getCurrencyType() == 1) {
                if (result == 0) {
                    result = currencyInfo.getCurrencyValue();
                } else {
                    result = Math.min(result, currencyInfo.getCurrencyValue());
                }
            }
        }
        return result;
    }

    /**
     * 通过游戏类型返回图标地址
     */
    public String getIconUrlByGameType(int gameType) {
        try {
            SudGameConfigInfo sudGameInfo = sudGameInfoMap.get(gameType);
            if (sudGameInfo != null) {
                return sudGameInfo.getIconUrl();
            }
        } catch (Exception e) {
            logger.error("get sud game icon url by gameType error. gameType={} error message={}", gameType, e.getMessage());
        }
        return "";
    }

    /**
     * 通过游戏类型返回j即构游戏id
     */
    public String getGameIdByGameType(int gameType) {
        try {
            SudGameConfigInfo sudGameInfo = sudGameInfoMap.get(gameType);
            if (sudGameInfo != null) {
                return sudGameInfo.getGameId();
            }
        } catch (Exception e) {
            logger.error("get sud game id by gameType error. gameType={} error message={}", gameType, e.getMessage());
        }
        return "";
    }

    /**
     * 通过游戏类型返回游戏名
     */
    public String getGameNameByGameType(int gameType, int slang) {
        try {
            SudGameConfigInfo sudGameInfo = sudGameInfoMap.get(gameType);
            if (sudGameInfo != null) {
                return slang == SLangType.ARABIC ? sudGameInfo.getNameAr() : sudGameInfo.getName();
            }
        } catch (Exception e) {
            logger.error("get sud game name by gameType error. gameType={} error message={}", gameType, e.getMessage());
        }
        return "";
    }

    public String getAppId() {
        return appId;
    }

    public void setAppId(String appId) {
        this.appId = appId;
    }

    public String getAppKey() {
        return appKey;
    }

    public void setAppKey(String appKey) {
        this.appKey = appKey;
    }

    public String getAppSecret() {
        return appSecret;
    }

    public void setAppSecret(String appSecret) {
        this.appSecret = appSecret;
    }

    public String getGetSudApiUrl() {
        return ServerConfig.isProduct() ? getSudApiUrlProd : getSudApiUrl;
    }

    public void setGetSudApiUrl(String getSudApiUrl) {
        this.getSudApiUrl = getSudApiUrl;
    }

    public String getGetSudApiUrlProd() {
        return getSudApiUrlProd;
    }

    public void setGetSudApiUrlProd(String getSudApiUrlProd) {
        this.getSudApiUrlProd = getSudApiUrlProd;
    }


    public Map<Integer, SudGameConfigInfo> getSudGameInfoMap() {
        return sudGameInfoMap;
    }

    public void setSudGameInfoMap(Map<Integer, SudGameConfigInfo> sudGameInfoMap) {
        this.sudGameInfoMap = sudGameInfoMap;
    }

    public Map<Integer, CurrencyInfo> getLudoCurrencyInfoMap() {
        return ludoCurrencyInfoMap;
    }

    public void setLudoCurrencyInfoMap(Map<Integer, CurrencyInfo> ludoCurrencyInfoMap) {
        this.ludoCurrencyInfoMap = ludoCurrencyInfoMap;
    }
}
