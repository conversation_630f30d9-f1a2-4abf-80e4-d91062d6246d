package com.quhong.config;

import com.quhong.enums.CommonMqTaskConstant;
import org.springframework.amqp.core.Binding;
import org.springframework.amqp.core.BindingBuilder;
import org.springframework.amqp.core.Queue;
import org.springframework.amqp.core.TopicExchange;
import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;

/**
 * <AUTHOR>
 */
@Configuration
public class GameMqConfig {

    @Bean
    public Queue msgCollectQueue() {
        return new Queue(CommonMqTaskConstant.GAME_MQ_MSG_QUEUE, true);
    }

    // 定义Topic交换机bean
    @Bean
    public TopicExchange topicCommonExchange() {
        return new TopicExchange(CommonMqTaskConstant.TASK_COMMON_EXCHANGE, false, false);
    }

    @Bean
    Binding bindingExchangeRoomMsg(Queue msgCollectQueue, TopicExchange topicCommonExchange) {
        return BindingBuilder.bind(msgCollectQueue).to(topicCommonExchange).with(CommonMqTaskConstant.USER_TASK_ROUTING_KEY);
    }
}
