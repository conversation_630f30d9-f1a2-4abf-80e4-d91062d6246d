package com.quhong.config;

import org.springframework.context.annotation.Bean;
import org.springframework.context.annotation.Configuration;
import tech.sud.mgp.auth.api.SudMGPAuth;

import javax.annotation.Resource;

/**
 * SudMGPAuth配置类
 *
 * <AUTHOR>
 */
@Configuration
public class SudMGPAuthConfig {

    @Resource
    private SudGameConfig sudGameConfig;

    /**
     * 创建SUD服务端SDK鉴权对象
     *
     * @return SUD服务端SDK鉴权对象
     */
    @Bean
    public SudMGPAuth sudMGPAuth() {
        return new SudMGPAuth(sudGameConfig.getAppId(), sudGameConfig.getAppSecret());
    }
}
