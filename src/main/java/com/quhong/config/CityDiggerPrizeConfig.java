package com.quhong.config;

import com.quhong.core.config.ServerConfig;
import com.quhong.vo.PrizeVo;
import org.springframework.boot.context.properties.ConfigurationProperties;
import org.springframework.context.annotation.PropertySource;
import org.springframework.stereotype.Component;

import java.util.List;


@Component
@PropertySource(value = "classpath:city_digger_prize.yml", encoding = "UTF-8", factory = YamlPropertySourceFactory.class)
@ConfigurationProperties(prefix = "prize")
public class CityDiggerPrizeConfig {
    // 礼物盒子：可随机爆钻石、礼物、麦位框、座驾
    private List<PrizeVo> giftBoxRewards;
    // 麦位框：有效期1天，重复获得可累加，数量7个
    private List<PrizeVo> micFrameRewards;
    // 终极奖励99钻礼物池、200钻石、座驾
    private List<PrizeVo> finalRewards;

    // 礼物盒子：可随机爆钻石、礼物、麦位框、座驾
    private List<PrizeVo> giftBoxRewardsTest;
    // 麦位框：有效期1天，重复获得可累加，数量7个
    private List<PrizeVo> micFrameRewardsTest;
    // 终极奖励99钻礼物池、200钻石、座驾
    private List<PrizeVo> finalRewardsTest;

    // 香蕉皮
    private Integer[] bananaPeelings;
    // 炸弹
    private Integer[] bomb;
    // 礼盒
    private Integer[] giftBox;
    // 麦位框
    private Integer[] micFrame;
    // 火箭
    private Integer[] rocket;
    // 终点
    private Integer finalStep;

    public List<PrizeVo> getGiftBoxRewards() {
        return ServerConfig.isProduct() ? giftBoxRewards : giftBoxRewardsTest;
    }

    public void setGiftBoxRewards(List<PrizeVo> giftBoxRewards) {
        this.giftBoxRewards = giftBoxRewards;
    }

    public List<PrizeVo> getMicFrameRewards() {
        return ServerConfig.isProduct() ? micFrameRewards : micFrameRewardsTest;
    }

    public void setMicFrameRewards(List<PrizeVo> micFrameRewards) {
        this.micFrameRewards = micFrameRewards;
    }

    public List<PrizeVo> getFinalRewards() {
        return ServerConfig.isProduct() ? finalRewards : finalRewardsTest;
    }

    public void setFinalRewards(List<PrizeVo> finalRewards) {
        this.finalRewards = finalRewards;
    }

    public Integer[] getBananaPeelings() {
        return bananaPeelings;
    }

    public void setBananaPeelings(Integer[] bananaPeelings) {
        this.bananaPeelings = bananaPeelings;
    }

    public Integer[] getBomb() {
        return bomb;
    }

    public void setBomb(Integer[] bomb) {
        this.bomb = bomb;
    }

    public Integer[] getGiftBox() {
        return giftBox;
    }

    public void setGiftBox(Integer[] giftBox) {
        this.giftBox = giftBox;
    }

    public Integer[] getMicFrame() {
        return micFrame;
    }

    public void setMicFrame(Integer[] micFrame) {
        this.micFrame = micFrame;
    }

    public Integer[] getRocket() {
        return rocket;
    }

    public void setRocket(Integer[] rocket) {
        this.rocket = rocket;
    }

    public Integer getFinalStep() {
        return finalStep;
    }

    public void setFinalStep(Integer finalStep) {
        this.finalStep = finalStep;
    }

    public List<PrizeVo> getGiftBoxRewardsTest() {
        return giftBoxRewardsTest;
    }

    public void setGiftBoxRewardsTest(List<PrizeVo> giftBoxRewardsTest) {
        this.giftBoxRewardsTest = giftBoxRewardsTest;
    }

    public List<PrizeVo> getMicFrameRewardsTest() {
        return micFrameRewardsTest;
    }

    public void setMicFrameRewardsTest(List<PrizeVo> micFrameRewardsTest) {
        this.micFrameRewardsTest = micFrameRewardsTest;
    }

    public List<PrizeVo> getFinalRewardsTest() {
        return finalRewardsTest;
    }

    public void setFinalRewardsTest(List<PrizeVo> finalRewardsTest) {
        this.finalRewardsTest = finalRewardsTest;
    }
}
