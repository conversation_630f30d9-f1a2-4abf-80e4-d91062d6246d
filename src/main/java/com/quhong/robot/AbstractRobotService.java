package com.quhong.robot;

import com.quhong.redis.RobotRedis;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.List;
import java.util.concurrent.ThreadLocalRandom;


public abstract class AbstractRobotService {

    @Resource
    public RobotRedis robotRedis;


    public String getRandomRobot() {
        List<String> bizRobotSet = robotRedis.getBizRobotSet();
        return CollectionUtils.isEmpty(bizRobotSet) ? null : bizRobotSet.get(ThreadLocalRandom.current().nextInt(bizRobotSet.size()));
    }
}
