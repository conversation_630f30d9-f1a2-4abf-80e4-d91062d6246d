package com.quhong.robot;

import com.quhong.core.timers.DelayTask;
import com.quhong.core.timers.TimerService;
import com.quhong.core.utils.DateHelper;
import com.quhong.dto.FingerGuessDTO;
import com.quhong.exception.GameException;
import com.quhong.redis.FingerGuessRedis;
import com.quhong.service.FingerGuessService;
import com.quhong.utils.CollectionUtil;
import com.quhong.vo.FingerGuessGiftVO;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.scheduling.annotation.Scheduled;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.LinkedList;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ThreadLocalRandom;

/**
 * 三、机器人自动创建、应战猜拳
 * 1、机器人来源：朋友圈点赞的机器人规则
 * 2、机器人创建猜拳规则：
 * - 猜拳广场的12钻礼物猜拳记录小于等于3条时触发机器人创建猜拳，触发时每间隔5秒依次创建随机的5-10条记录，随机创建12钻和99钻礼物；
 * - 每条创建记录使用不同的机器人用户信息，每7天跟换一次机器人，更换规则和朋友圈点赞机器人同步
 * - 机器人创建的猜拳和真实用户创建的猜拳具有效期，过期自动失效
 * 3、机器人猜拳规则：
 * - 机器人可以应战真实用户创建的猜拳，不可应战机器人创建的猜拳
 * - 真实用户猜拳创建成功后，等待60秒无人应战，再派机器人应战。
 * - 机器人可以应战所有礼物的猜拳
 * 4、机器人创建和应战猜拳输赢规则
 * - 70%赢、30%输；每创建和应战10局，有3局输给用户；
 * - 用户赢得的钻石，系统每日0点（GMT+3）从用户账户扣除。
 * 5、数据统计：机器人应战次数、发起猜拳次数；机器人应战猜拳消耗钻石数；机器人创建猜拳赢得钻石数
 */
@Service
public class FingerGuessRobotService extends AbstractRobotService {
    private static final Logger logger = LoggerFactory.getLogger(FingerGuessRobotService.class);
    private static Map<Integer, FingerGuessGiftVO.Gift> GIFT_MAP = null;
    private static final List<String> ROBOT_CREATED = new LinkedList<>();
    @Resource
    private FingerGuessRedis fingerGuessRedis;
    @Resource
    private FingerGuessService fingerGuessService;


    @PostConstruct
    public void postInit() {
        GIFT_MAP = CollectionUtil.listToKeyMap(fingerGuessService.giftList().getList(), FingerGuessGiftVO.Gift::getPrice);
    }

    @Scheduled(fixedDelay = 99 * 1000, initialDelay = 10000)
    private void onTick() {
        int nowSeconds = DateHelper.getNowSeconds();
        logger.info("finger guess robot check...");
        Map<String, Integer> allGame = fingerGuessRedis.getAllWithScores();
        if (allGame.size() < 5) {
            createFingerGuess();
        } else {
            int joinCount = 0;
            for (Map.Entry<String, Integer> entry : allGame.entrySet()) {
                int createTime = entry.getValue() - FingerGuessService.GAME_EXPIRE_SECONDS;
                if (nowSeconds - createTime > 60 && !ROBOT_CREATED.contains(entry.getKey())) {
                    if (joinCount > 20) {
                        break;
                    }
                    joinFingerGuess(entry.getKey(), joinCount);
                    joinCount++;
                }
            }
        }
    }

    private FingerGuessGiftVO.Gift getGiftToCreate() {
//        return GIFT_MAP.get(ThreadLocalRandom.current().nextInt(22) >= 2 ? 12 : 99);
        return GIFT_MAP.get(12);
    }

    /**
     * 猜拳广场的12钻礼物猜拳记录小于等于3条时触发机器人创建猜拳，触发时每间隔5秒依次创建随机的5-10条记录，随机创建12钻和99钻礼物
     */
    public void createFingerGuess() {
        try {
            for (int i = 0; i < ThreadLocalRandom.current().nextInt(5, 11); i++) {
                String robotUid = getRandomRobot();
                if (StringUtils.isEmpty(robotUid)) {
                    continue;
                }
                if (i > 0) {
                    // noinspection BusyWait
                    Thread.sleep(ThreadLocalRandom.current().nextInt(5555, 8888));
                }
                FingerGuessDTO dto = new FingerGuessDTO();
                dto.setUid(robotUid);
                FingerGuessGiftVO.Gift giftToCreate = getGiftToCreate();
                if (null == giftToCreate) {
                    logger.error("cannot find gift to create finger guess.");
                    return;
                }
                dto.setGift_id(giftToCreate.getGid());
                dto.setG_icon(giftToCreate.getIcon());
                dto.setG_type(0);
                dto.setRobot(true);
                String gameId = fingerGuessService.create(dto);
                if (ROBOT_CREATED.size() > 800) {
                    logger.info("remove robot created list.");
                    ROBOT_CREATED.removeIf(id -> new ObjectId(id).getTimestamp() < DateHelper.getNowSeconds() - 60 * 60);
                }
                ROBOT_CREATED.add(gameId);
                logger.info("robot create finger guess game. robot uid={} gameId={}", robotUid, gameId);
            }
        } catch (GameException e) {
            logger.info("robot create finger guess. msg={}", e.getData());
        } catch (Exception e) {
            logger.error("robot create finger guess error. msg={}", e.getMessage());
        }
    }

    /**
     * 机器人可以应战真实用户创建的猜拳，不可应战机器人创建的猜拳
     * 真实用户猜拳创建成功后，等待60秒无人应战，再派机器人应战
     */
    public void joinFingerGuess(String gameId, int joinCount) {
        int random = ThreadLocalRandom.current().nextInt(5000, 7000);
        TimerService.getService().addDelay(new DelayTask(random + 5000 * joinCount) {
            @Override
            protected void execute() {
                try {
                    FingerGuessDTO dto = new FingerGuessDTO();
                    String robotUid = getRandomRobot();
                    if (StringUtils.isEmpty(robotUid)) {
                        return;
                    }
                    dto.setUid(robotUid);
                    dto.setRobot(true);
                    dto.setG_type(0);
                    dto.setG_id(gameId);
                    fingerGuessService.join(dto);
                    logger.info("robot join finger guess game. robot uid={}", robotUid);
                } catch (GameException e) {
                    logger.info("robot join finger guess. msg={}", e.getData());
                } catch (Exception e) {
                    logger.error("robot join finger guess error. msg={}", e.getMessage());
                }
            }
        });
    }
}
