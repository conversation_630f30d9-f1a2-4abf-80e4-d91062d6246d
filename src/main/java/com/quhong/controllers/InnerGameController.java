package com.quhong.controllers;

import com.alibaba.fastjson.JSONObject;
import com.quhong.core.distribution.DistributeLock;
import com.quhong.data.dto.TruthDareV2DTO;
import com.quhong.data.vo.TruthDareV2GameVO;
import com.quhong.datas.HttpResult;
import com.quhong.dto.InnerSudGameDTO;
import com.quhong.enums.GameHttpCode;
import com.quhong.enums.HttpCode;
import com.quhong.enums.SudGameConstant;
import com.quhong.exception.GameException;
import com.quhong.handler.BaseController;
import com.quhong.mongo.dao.SudGameDao;
import com.quhong.mongo.data.SudGameData;
import com.quhong.service.TruthDareV2Service;
import com.quhong.sud.dto.SudGameDTO;
import com.quhong.sud.service.SudService;
import com.quhong.sud.vo.CreateSudGameVO;
import com.quhong.utils.RoomUtils;
import com.quhong.vo.InnerSudGameVO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.http.MediaType;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;

@RestController
@RequestMapping(value = "inner/game/", produces = MediaType.APPLICATION_JSON_VALUE)
public class InnerGameController extends BaseController {
    private static final Logger logger = LoggerFactory.getLogger(InnerGameController.class);

    @Resource
    private SudService sudService;
    @Resource
    private SudGameDao sudGameDao;
    @Resource
    private TruthDareV2Service truthDareV2Service;

    @RequestMapping("escape_game")
    private HttpResult<Object> escapeGame(@RequestParam(value = "uid") String uid,
                                          @RequestParam(value = "roomId") String roomId,
                                          @RequestParam(value = "gameId") String gameId) {
        logger.info("inner escape game. roomId={} uid={} gameId={}", roomId, uid, gameId);
        try (DistributeLock lock = new DistributeLock(SudController.SUD_LOCK_KEY + roomId, 20)) {
            lock.lock();
            SudGameData gameData = sudGameDao.findData(gameId);
            if (null == gameData) {
                logger.info("cannot fin gameData gameId={}", gameId);
                throw new GameException(GameHttpCode.FAILURE);
            }
            if (gameData.getStatus() == SudGameConstant.GAME_PROCESSING) {
                if (!RoomUtils.isGameRoom(roomId)) {
                    // 被房间管理员踢出房间逃跑
                    sudService.escapeGame(gameData, uid, 2);
                }
            } else if (SudService.GAME_MATCHING_LIST.contains(gameData.getStatus())) {
                if (RoomUtils.isGameRoom(roomId)) {
                    // 在组队状态，按玩家自己退出游戏处理
                    sudService.quitAndReturnCoin(uid, uid, gameData);
                    sudService.gameChange(gameData);
                }
            }
            return HttpResult.getOk();
        } catch (GameException e) {
            throw e;
        } catch (Exception e) {
            logger.error("inner escape game. roomId={} uid={} gameId={}", roomId, uid, gameId, e);
            throw new GameException(GameHttpCode.FAILURE);
        }
    }

    /**
     * 退出游戏,用户主动退出游戏
     */
    @PostMapping("quiteGame")
    public HttpResult<InnerSudGameVO> quiteGame(@RequestBody InnerSudGameDTO reqDTO) {
        if (checkParam(reqDTO.getUid(), reqDTO.getRoomId())) {
            logger.error("check param not pass. uid={} roomId={}", reqDTO.getUid(), reqDTO.getRoomId());
            InnerSudGameVO vo = new InnerSudGameVO();
            vo.setErrorCode(HttpCode.PARAM_ERROR);
            return HttpResult.getOk(vo);
        }
        try (DistributeLock lock = new DistributeLock(SudController.SUD_LOCK_KEY + reqDTO.getRoomId(), 30)) {
            lock.lock();
            InnerSudGameVO vo = new InnerSudGameVO();
            SudGameDTO req = new SudGameDTO();
            req.setUid(reqDTO.getUid());
            req.setAid(reqDTO.getUid());
            req.setGameId(reqDTO.getGameId());
            CreateSudGameVO oldVO = sudService.quitGame(req);
            BeanUtils.copyProperties(oldVO, vo);
            return HttpResult.getOk(vo);
        } catch (GameException e) {
            InnerSudGameVO vo = new InnerSudGameVO();
            vo.setErrorCode(e.getHttpCode());
            return HttpResult.getOk(vo);
        } catch (Exception e) {
            logger.error("inner quiteGame. reqDTO={}", JSONObject.toJSONString(reqDTO), e);
            throw new GameException(GameHttpCode.FAILURE);
        }
    }

    /**
     *
     */
    @PostMapping("createOrJoin")
    public HttpResult<InnerSudGameVO> createOrJoin(@RequestBody InnerSudGameDTO reqDTO) {
        if (checkParam(reqDTO.getUid(), reqDTO.getRoomId())) {
            logger.error("check param not pass. uid={} roomId={}", reqDTO.getUid(), reqDTO.getRoomId());
            InnerSudGameVO vo = new InnerSudGameVO();
            vo.setErrorCode(HttpCode.PARAM_ERROR);
            return HttpResult.getOk(vo);
        }
        try (DistributeLock lock = new DistributeLock(SudController.SUD_LOCK_KEY + reqDTO.getRoomId(), 30)) {
            lock.lock();
//            InnerSudGameVO vo = sudService.createOrJoin(reqDTO);
            InnerSudGameVO vo = new InnerSudGameVO();
            return HttpResult.getOk(vo);
        } catch (GameException e) {
            throw e;
        } catch (Exception e) {
            logger.error("inner createOrJoin. reqDTO={}", JSONObject.toJSONString(reqDTO), e);
            throw new GameException(GameHttpCode.FAILURE);
        }
    }

    /**
     * 简单校验uid和roomId
     */
    private boolean checkParam(String uid, String roomId) {
        return StringUtils.isEmpty(uid) || StringUtils.isEmpty(roomId) || roomId.length() < 2;
    }

    /**
     * 真心话大冒险相关内部接口
     */
    @PostMapping("truthDareCloseGame")
    public HttpResult<TruthDareV2GameVO> truthDareCloseGame(@RequestBody TruthDareV2DTO reqDTO) {
        logger.info("truthDareCloseGame reqDTO={}", JSONObject.toJSONString(reqDTO));
        return HttpResult.getOk(truthDareV2Service.closeGame(reqDTO));
    }
}
