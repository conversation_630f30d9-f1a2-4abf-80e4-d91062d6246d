package com.quhong.controllers;

import com.quhong.core.distribution.DistributeLock;
import com.quhong.enums.CityDiggerConstant;
import com.quhong.enums.GameHttpCode;
import com.quhong.handler.H5Controller;
import com.quhong.image.ImageUrlGenerator;
import com.quhong.mongo.data.Actor;
import com.quhong.mysql.data.CityDiggerData;
import com.quhong.service.ActorService;
import com.quhong.service.CityDiggerService;
import com.quhong.vo.CityDiggerVo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import java.util.HashMap;
import java.util.Map;

/**
 * 城市寻宝
 */
@RestController
@RequestMapping("${baseUrl}/city_digger/")
public class CityDiggerController extends H5Controller {
    private static final Logger logger = LoggerFactory.getLogger(CityDiggerController.class);

    @Value("${city.offline:false}")
    private boolean offline;
    @Resource
    private CityDiggerService cityDiggerService;
    @Resource
    private ActorService actorService;


    /**
     * 进入寻宝页面时返回个人信息：心心数据、当前步数
     */
    @GetMapping("info")
    public String info(String uid) {
        try {
            logger.info("get city digger info uid={}", uid);
            CityDiggerData cityDiggerData = cityDiggerService.getCityDiggerData(uid);
            Actor actor = actorService.getActor(uid);
            Map<String, Object> result = new HashMap<>();
            result.put("heart", actor.getHeartGot());
            result.put("head", ImageUrlGenerator.generateRoomUserUrl(actor.getHead()));
            result.put("curStep", cityDiggerData.getStep());
            result.put("received", cityDiggerService.stringToIntegerSet(cityDiggerData.getReceived()));
            return createResult(GameHttpCode.SUCCESS, result);
        } catch (Exception e) {
            logger.error("get city digger info error uid={}", uid, e);
            return createResult(GameHttpCode.FAILURE, null);
        }
    }

    /**
     * 掷骰子：后台返回最终的格子数，如果有奖励则发放奖励及返回中奖信息
     */
    @GetMapping("throw_dice")
    public String throwDice(String uid) {
        if (offline) {
            return createResult(GameHttpCode.CITY_OFFLINE, null);
        }
        logger.info("city digger throw dice uid={}", uid);
        String lockKey = getLockKey(uid);
        DistributeLock lock = new DistributeLock(lockKey);
        try {
            lock.lock();
            CityDiggerVo vo = new CityDiggerVo();
            Actor actor = actorService.getActor(uid);
            int heart = actor.getHeartGot() - CityDiggerConstant.HEART_CONSUME;
            if (heart < 0) {
                return createResult(GameHttpCode.NOT_ENOUGH_HEART, null);
            }
            vo.setUid(uid);
            vo.setName(actor.getName());
            vo.setHeart(heart);
            cityDiggerService.throwDice(vo, CityDiggerConstant.HEART_CONSUME, heart, actor.getHeartReceived());
            return createResult(GameHttpCode.SUCCESS, vo);
        } catch (Exception e) {
            logger.error("throw dice error uid={}", uid, e);
            return createResult(GameHttpCode.FAILURE, null);
        } finally {
            lock.unlock();
        }
    }

    /**
     * 我的奖励：倒序排列，展示字段：奖品图标、名称、数量或有效期、奖励获得日期和时间
     */
    @GetMapping("prize")
    public String prize(String uid, Integer curPage, Integer pageSize) {
        try {
            logger.info("get city digger prize uid={} curPage={} pageSize={}", uid, curPage, pageSize);
            return createResult(GameHttpCode.SUCCESS, cityDiggerService.getPrizeList(uid, curPage, pageSize));
        } catch (Exception e) {
            logger.error("get my prize error uid={} curPage={} pageSize={}", uid, curPage, pageSize, e);
            return createResult(GameHttpCode.FAILURE, null);
        }
    }

    /**
     * 通知交互：返回最近20条中奖用户信息
     */
    @GetMapping("recent_prize")
    public String recentPrize(String uid) {
        try {
            logger.info("get city digger recent prize  uid={}", uid);
            return createResult(GameHttpCode.SUCCESS, cityDiggerService.getRecentPrize());
        } catch (Exception e) {
            logger.error("get recent prize error uid={}", uid, e);
            return createResult(GameHttpCode.FAILURE, null);
        }
    }

    /**
     * 排行榜：排行榜展示前20名，倒序排列，展示字段信息：用户头像、昵称、消费心心数量
     */
    @GetMapping("ranking")
    public String ranking(String uid) {
        try {
            logger.info("get city digger ranking uid={}", uid);
            return createResult(GameHttpCode.SUCCESS, cityDiggerService.getRanking());
        } catch (Exception e) {
            logger.error("get ranking error uid={}", uid, e);
            return createResult(GameHttpCode.FAILURE, null);
        }
    }

    private String getLockKey(String uid) {
        return "change_heart_" + uid;
    }

}
