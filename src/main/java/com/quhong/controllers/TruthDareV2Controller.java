package com.quhong.controllers;

import com.alibaba.fastjson.JSONObject;
import com.quhong.data.dto.TruthDareV2DTO;
import com.quhong.enums.HttpCode;
import com.quhong.handler.WebController;
import com.quhong.service.TruthDareV2Service;
import com.quhong.utils.RequestUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

@RestController
@RequestMapping("${baseUrl}/truthDare/")
public class TruthDareV2Controller extends WebController {

    private static final Logger logger = LoggerFactory.getLogger(TruthDareV2Controller.class);

    @Resource
    private TruthDareV2Service truthDareV2Service;

    @RequestMapping("gameCheck")
    public String gameCheck(HttpServletRequest request) {
        TruthDareV2DTO reqDTO = RequestUtils.getSendData(request, TruthDareV2DTO.class);
        logger.info("gameCheck reqDTO={}", JSONObject.toJSONString(reqDTO));
        return createResult(reqDTO, HttpCode.SUCCESS, truthDareV2Service.gameCheck(reqDTO));
    }

    @RequestMapping("updateTopic")
    public String updateTopic(HttpServletRequest request) {
        TruthDareV2DTO reqDTO = RequestUtils.getSendData(request, TruthDareV2DTO.class);
        logger.info("updateTopic reqDTO={}", JSONObject.toJSONString(reqDTO));
        return createResult(reqDTO, HttpCode.SUCCESS, truthDareV2Service.updateTopic(reqDTO));
    }

    @RequestMapping("createGame")
    public String createGame(HttpServletRequest request) {
        TruthDareV2DTO reqDTO = RequestUtils.getSendData(request, TruthDareV2DTO.class);
        logger.info("createGame reqDTO={}", JSONObject.toJSONString(reqDTO));
        return createResult(reqDTO, HttpCode.SUCCESS, truthDareV2Service.createGame(reqDTO));
    }

    @RequestMapping("gameInfo")
    public String gameInfo(HttpServletRequest request) {
        TruthDareV2DTO reqDTO = RequestUtils.getSendData(request, TruthDareV2DTO.class);
        logger.info("gameCheck reqDTO={}", JSONObject.toJSONString(reqDTO));
        return createResult(reqDTO, HttpCode.SUCCESS, truthDareV2Service.gameInfo(reqDTO));
    }

    @RequestMapping("startGame")
    public String startGame(HttpServletRequest request) {
        TruthDareV2DTO reqDTO = RequestUtils.getSendData(request, TruthDareV2DTO.class);
        logger.info("startGame reqDTO={}", JSONObject.toJSONString(reqDTO));
        return createResult(reqDTO, HttpCode.SUCCESS, truthDareV2Service.startGame(reqDTO));
    }

    @RequestMapping("chooseTopic")
    public String chooseTopic(HttpServletRequest request) {
        TruthDareV2DTO reqDTO = RequestUtils.getSendData(request, TruthDareV2DTO.class);
        logger.info("chooseTopic reqDTO={}", JSONObject.toJSONString(reqDTO));
        return createResult(reqDTO, HttpCode.SUCCESS, truthDareV2Service.chooseTopic(reqDTO));
    }

    @RequestMapping("closeGame")
    public String closeGame(HttpServletRequest request) {
        TruthDareV2DTO reqDTO = RequestUtils.getSendData(request, TruthDareV2DTO.class);
        logger.info("closeGame reqDTO={}", JSONObject.toJSONString(reqDTO));
        return createResult(reqDTO, HttpCode.SUCCESS, truthDareV2Service.closeGame(reqDTO));
    }



}
