package com.quhong.controllers;

import com.quhong.core.distribution.DistributeLock;
import com.quhong.dto.GiftBoxDTO;
import com.quhong.dto.LuckyBoxDTO;
import com.quhong.enums.GameHttpCode;
import com.quhong.enums.HttpCode;
import com.quhong.exception.GameException;
import com.quhong.handler.WebController;
import com.quhong.service.LuckyBoxService;
import com.quhong.utils.RequestUtils;
import com.quhong.vo.SendLuckyBoxVO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

/**
 * 红包
 *
 * <AUTHOR>
 * @date 2022/12/21
 */
@RestController
@RequestMapping("${baseUrl}/box/")
public class LuckyBoxController extends WebController {

    private static final Logger logger = LoggerFactory.getLogger(LuckyBoxController.class);

    private static final String GET_BOX_KEY = "get_lucky_box_";
    private static final String GET_GIFT_BOX_KEY = "get_gift_box_";

    @Resource
    private LuckyBoxService luckyBoxService;

    /**
     * 发送红包
     */
    @PostMapping("send")
    public String sendLuckyBox(HttpServletRequest request) {
        LuckyBoxDTO reqDTO = RequestUtils.getSendData(request, LuckyBoxDTO.class);
        long timeMillis = System.currentTimeMillis();
        logger.info("send lucky box. uid={} roomId={} money={} num={}", reqDTO.getUid(), reqDTO.getRoom_id(), reqDTO.getMoney(), reqDTO.getNum());
        SendLuckyBoxVO vo = luckyBoxService.sendLuckyBox(reqDTO);
        logger.info("send lucky box. uid={} roomId={} money={} num={} cost={}", reqDTO.getUid(), reqDTO.getRoom_id(), reqDTO.getMoney(), reqDTO.getNum(), System.currentTimeMillis() - timeMillis);
        return createResult(reqDTO, HttpCode.SUCCESS, vo);
    }

    /**
     * 抢红包
     */
    @PostMapping("get")
    public String getLuckyBox(HttpServletRequest request) {
        LuckyBoxDTO reqDTO = RequestUtils.getSendData(request, LuckyBoxDTO.class);
        logger.info("get lucky box. uid={} boxId={} xPos={} yPos={} robot={}", reqDTO.getUid(), reqDTO.getBox_id(), reqDTO.getX_pos(), reqDTO.getY_pos(), reqDTO.getRobot());
        DistributeLock lock = new DistributeLock(GET_BOX_KEY + reqDTO.getUid() + "_" + reqDTO.getBox_id());
        try {
            lock.lock();
            return createResult(reqDTO, HttpCode.SUCCESS, luckyBoxService.getLuckyBox(reqDTO));
        } catch (GameException e) {
            throw e;
        } catch (Exception e) {
            logger.error("get lucky box error. uid={} boxId={}", reqDTO.getUid(), reqDTO.getBox_id());
            throw e;
        } finally {
            lock.unlock();
        }
    }

    /**
     * 检查房间红包
     */
    @PostMapping("check")
    public String checkRoomLuckyBox(HttpServletRequest request) {
        LuckyBoxDTO reqDTO = RequestUtils.getSendData(request, LuckyBoxDTO.class);
        logger.info("check room lucky box. uid={} roomId={} ", reqDTO.getUid(), reqDTO.getRoom_id());
        return createResult(reqDTO, HttpCode.SUCCESS, luckyBoxService.checkRoomLuckyBox(reqDTO));
    }

    /**
     * 房间内红包领取记录
     */
    @PostMapping("inroom/record")
    public String getLuckyBoxRecord(HttpServletRequest request) {
        LuckyBoxDTO reqDTO = RequestUtils.getSendData(request, LuckyBoxDTO.class);
        if (StringUtils.isEmpty(reqDTO.getBox_id())) {
            logger.error("getLuckyBoxRecord boxId is empty.");
            throw new GameException(GameHttpCode.PARAM_ERROR);
        }
        logger.info("get room lucky box record. boxId={}", reqDTO.getBox_id());
        return createResult(reqDTO, HttpCode.SUCCESS, luckyBoxService.getLuckyBoxRecord(reqDTO));
    }

    /**
     * 房间红包和礼物红包配置
     */
    @PostMapping("gift_box")
    public String getGiftBoxConfig(HttpServletRequest request) {
        GiftBoxDTO reqDTO = RequestUtils.getSendData(request, GiftBoxDTO.class);
        logger.info("get room gift lucky box config. uid={}", reqDTO.getUid());
        return createResult(reqDTO, HttpCode.SUCCESS, luckyBoxService.getGiftBoxConfig());
    }

    /**
     * 发送礼物红包
     */
    @PostMapping("send_gift_box")
    public String sendGiftBox(HttpServletRequest request) {
        GiftBoxDTO reqDTO = RequestUtils.getSendData(request, GiftBoxDTO.class);
        if (StringUtils.isEmpty(reqDTO.getRoom_id()) || reqDTO.getBox_type() == null) {
            logger.error("sendGiftBox param error. uid={} roomId={} boxType={} msg={}", reqDTO.getUid(), reqDTO.getRoom_id(), reqDTO.getBox_type(), reqDTO.getMsg());
            throw new GameException(GameHttpCode.PARAM_ERROR);
        }
        logger.info("send gift box. uid={} roomId={} boxType={} msg={}", reqDTO.getUid(), reqDTO.getRoom_id(), reqDTO.getBox_type(), reqDTO.getMsg());
        return createResult(reqDTO, HttpCode.SUCCESS, luckyBoxService.sendGiftBox(reqDTO));
    }

    /**
     * 抢礼物红包
     */
    @PostMapping("get_gift_box")
    public String getGiftBox(HttpServletRequest request) {
        GiftBoxDTO reqDTO = RequestUtils.getSendData(request, GiftBoxDTO.class);
        logger.info("get gift box. uid={} roomId={} boxId={}", reqDTO.getUid(), reqDTO.getRoom_id(), reqDTO.getBox_id());
        if (StringUtils.isEmpty(reqDTO.getRoom_id()) || reqDTO.getBox_id() == null) {
            logger.error("getGiftBox param error. uid={} roomId={} boxId={}", reqDTO.getUid(), reqDTO.getRoom_id(), reqDTO.getBox_id());
            throw new GameException(GameHttpCode.PARAM_ERROR);
        }
        DistributeLock lock = new DistributeLock(GET_GIFT_BOX_KEY + reqDTO.getUid() + "_" + reqDTO.getBox_id());
        try {
            lock.lock();
            return createResult(reqDTO, HttpCode.SUCCESS, luckyBoxService.getGiftBox(reqDTO));
        } catch (GameException e) {
            throw e;
        } catch (Exception e) {
            logger.error("get gift box error. uid={} roomId={} boxId={}", reqDTO.getUid(), reqDTO.getRoom_id(), reqDTO.getBox_id());
            throw e;
        } finally {
            lock.unlock();
        }
    }

    /**
     * 礼物红包领取记录
     */
    @PostMapping("gift_box_record")
    public String getGiftBoxRecord(HttpServletRequest request) {
        GiftBoxDTO reqDTO = RequestUtils.getSendData(request, GiftBoxDTO.class);
        if (reqDTO.getBox_id() == null) {
            logger.error("getGiftBoxRecord param boxId is null");
            throw new GameException(GameHttpCode.PARAM_ERROR);
        }
        logger.info("get gift box record. boxId={}", reqDTO.getBox_id());
        return createResult(reqDTO, HttpCode.SUCCESS, luckyBoxService.getGiftBoxRecord(reqDTO));
    }
}

