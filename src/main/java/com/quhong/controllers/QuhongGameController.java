package com.quhong.controllers;

import com.alibaba.fastjson.JSONObject;
import com.quhong.handler.WebController;
import com.quhong.qh.*;
import com.quhong.service.QhGameService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 自研小游戏
 */
@RestController
@RequestMapping("${baseUrl}/quhong")
public class QuhongGameController extends WebController {

    private static final Logger logger = LoggerFactory.getLogger(QuhongGameController.class);

    @Resource
    private QhGameService qhGameService;

    /**
     * 获取用户token
     */
    @PostMapping("/get_token")
    public QhGameVO<TokenVO> getToken(@RequestBody BaseDTO dto) {
        logger.info("getToken. dto={}", JSONObject.toJSONString(dto));
        try {
            return qhGameService.getToken(dto);
        } catch (Exception e) {
            logger.error("getToken error. {}", e.getMessage(), e);
            return QhGameVO.getError(1021, "get_token server error");
        }
    }

    /**
     * 获取用户信息
     */
    @PostMapping("/get_player_info")
    public QhGameVO<PlayerInfoVO> getPlayerInfo(@RequestBody BaseDTO dto) {
        logger.info("getPlayerInfo. dto={}", JSONObject.toJSONString(dto));
        try {
            return qhGameService.getPlayerInfo(dto);
        } catch (Exception e) {
            logger.error("getPlayerInfo error. {}", e.getMessage(), e);
            return QhGameVO.getError(1021, "get_player_info server error");
        }
    }

    /**
     * 用户钱包操作
     */
    @PostMapping("/change_balance")
    public QhGameVO<UserBalanceVO> changeBalance(@RequestBody QhGameDTO.ChangeBalanceDTO dto) {
        logger.info("change user balance. reqParam={}", JSONObject.toJSONString(dto));
        try {
            return qhGameService.changeBalance(dto);
        } catch (Exception e) {
            logger.error("change user balance error. reqParam={} {}", JSONObject.toJSONString(dto), e.getMessage(), e);
            return QhGameVO.getError(1021, "change_balance server error");
        }
    }

    /**
     * 获取用户信息
     */
    @PostMapping("/get_robot")
    public QhGameVO<RobotVO> getRobot(@RequestBody QhGameDTO.RobotDTO dto) {
        logger.info("getRobot. dto={}", JSONObject.toJSONString(dto));
        try {
            return qhGameService.getRobot(dto);
        } catch (Exception e) {
            logger.error("getRobot error. {}", e.getMessage(), e);
            return QhGameVO.getError(1021, "getRobot server error");
        }
    }
}
