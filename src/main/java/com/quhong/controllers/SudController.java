package com.quhong.controllers;

import com.alibaba.fastjson.JSONObject;
import com.quhong.api.SudGameApi;
import com.quhong.config.SudGameConfig;
import com.quhong.core.config.ServerConfig;
import com.quhong.core.distribution.DistributeLock;
import com.quhong.datas.HttpResult;
import com.quhong.enums.*;
import com.quhong.exception.GameException;
import com.quhong.handler.HttpEnvData;
import com.quhong.handler.WebController;
import com.quhong.mongo.dao.MongoRoomDao;
import com.quhong.mongo.dao.RoomConfigDao;
import com.quhong.mongo.data.MongoRoomData;
import com.quhong.mongo.data.SudGameData;
import com.quhong.sud.data.SudGameConfigInfo;
import com.quhong.sud.dto.*;
import com.quhong.sud.service.SudGameRoomService;
import com.quhong.sud.service.SudService;
import com.quhong.sud.vo.CheckVO;
import com.quhong.sud.vo.CreateSudGameVO;
import com.quhong.sud.vo.MatchingVO;
import com.quhong.utils.AppVersionUtils;
import com.quhong.utils.RequestUtils;
import com.quhong.utils.RoomUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import tech.sud.mgp.auth.api.SudCode;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;

/**
 * 即构小游戏
 *
 * <AUTHOR>
 * @date 2022/6/30
 */
@RestController
@RequestMapping("${baseUrl}sud/")
public class SudController extends WebController {

    private static final Logger logger = LoggerFactory.getLogger(SudController.class);
    public static final String SUD_LOCK_KEY = "sud_game_";

    @Value("${sud.ludo.defaultCurrencyIndex:0}")
    private int defaultCurrencyIndex;
    @Value("${sud.umo.defaultCurrencyIndex:0}")
    private int defaultUmoCurrencyIndex;
    @Resource
    private SudGameConfig sudGameConfig;
    @Resource
    private SudService sudService;
    @Resource
    private MongoRoomDao mongoRoomDao;
    @Resource
    private RoomConfigDao roomConfigDao;
    @Resource
    private SudGameApi sudGameApi;
    @Resource
    private SudGameRoomService sudGameRoomService;

    /**
     * 获取游戏code
     */
    @PostMapping("get_code")
    public String getGameCode(HttpServletRequest request) {
        SudGameDTO reqDTO = RequestUtils.getSendData(request, SudGameDTO.class);
        logger.info("get game code. uid={} requestId={}", reqDTO.getUid(), reqDTO.getRequestId());
        if (StringUtils.isEmpty(reqDTO.getUid())) {
            return createResult(reqDTO, HttpCode.PARAM_ERROR, null);
        }
        SudCode sudCode = sudService.getGameCode(reqDTO);
        JSONObject result = new JSONObject();
        result.put("code", sudCode.getCode());
        result.put("expire_date", sudCode.getExpireDate());
        return createResult(reqDTO, HttpCode.SUCCESS, result);
    }

    /**
     * 房主设置游戏的权限
     */
    @PostMapping("set_permissions")
    public String setPermissions(HttpServletRequest request) {
        GamePermissionsDTO reqDTO = RequestUtils.getSendData(request, GamePermissionsDTO.class);
        logger.info("check create game permissions. uid={} roomId={} gameType={} createGame={} requestId={}", reqDTO.getUid(), reqDTO.getRoomId(), reqDTO.getGameType(), reqDTO.getCreateGame(), reqDTO.getRequestId());
        if (checkParam(reqDTO.getUid(), reqDTO.getRoomId())) {
            logger.error("check param not pass. uid={} roomId={}", reqDTO.getUid(), reqDTO.getRoomId());
            return createResult(reqDTO, HttpCode.PARAM_ERROR, null);
        }
        sudService.setPermissions(reqDTO);
        return createResult(reqDTO, HttpCode.SUCCESS, null);
    }


    /**
     * 创建游戏
     */
    @PostMapping("create_game")
    public String create(HttpServletRequest request) {
        CreateSudGameDTO reqDTO = RequestUtils.getSendData(request, CreateSudGameDTO.class);
        logger.info("create sud game. uid={} roomId={} gameType={} playerNumber={}", reqDTO.getUid(), reqDTO.getRoomId(), reqDTO.getGameType(), reqDTO.getPlayerNumber());
        if (checkParam(reqDTO.getUid(), reqDTO.getRoomId())) {
            logger.error("check param not pass. uid={} roomId={}", reqDTO.getUid(), reqDTO.getRoomId());
            return createResult(reqDTO, HttpCode.PARAM_ERROR, null);
        }
        int onlineVersion = sudGameConfig.getSudGameInfoMap().get(reqDTO.getGameType()).getOnlineVersion();
        if (!AppVersionUtils.versionCheck(onlineVersion, reqDTO)) {
            return createResult(reqDTO, GameHttpCode.NEED_UPGRADE, null);
        }
        DistributeLock lock = new DistributeLock(SUD_LOCK_KEY + reqDTO.getRoomId(), 30);
        try {
            lock.lock();
            return createResult(reqDTO, HttpCode.SUCCESS, sudService.createGame(reqDTO));
        } catch (GameException e) {
            throw e;
        } catch (Exception e) {
            logger.error("create sud game error roomId={} uid={}", reqDTO.getRoomId(), reqDTO.getUid());
            throw e;
        } finally {
            lock.unlock();
        }
    }

    /**
     * 加入游戏
     */
    @PostMapping("join_game")
    public String joinGame(HttpServletRequest request) {
        SudGameDTO reqDTO = RequestUtils.getSendData(request, SudGameDTO.class);
        logger.info("join sud game uid={} roomId={} gameType={} index={}", reqDTO.getUid(), reqDTO.getRoomId(), reqDTO.getGameType(), reqDTO.getIndex());
        if (checkParam(reqDTO.getUid(), reqDTO.getRoomId())) {
            logger.error("check param not pass. uid={} roomId={}", reqDTO.getUid(), reqDTO.getRoomId());
            return createResult(reqDTO, HttpCode.PARAM_ERROR, null);
        }
        DistributeLock lock = new DistributeLock(SUD_LOCK_KEY + reqDTO.getRoomId(), 20);
        try {
            lock.lock();
            reqDTO.setIsAppJoinGameRoom(RoomUtils.isGameRoom(reqDTO.getRoomId()) ? 1 : 0);
            return createResult(reqDTO, HttpCode.SUCCESS, sudService.joinGame(reqDTO));
        } catch (GameException e) {
            throw e;
        } catch (Exception e) {
            logger.error("join sud game error roomId={} uid={} {}", reqDTO.getRoomId(), reqDTO.getUid(), e.getMessage(), e);
            throw new GameException(GameHttpCode.FAILURE);
        } finally {
            lock.unlock();
        }
    }

    /**
     * 变更队伍
     */
    @PostMapping("change_team")
    public String changeTeam(HttpServletRequest request) {
        SudGameDTO reqDTO = RequestUtils.getSendData(request, SudGameDTO.class);
        logger.info("sud game change team uid={} gameId={} gameType={}", reqDTO.getUid(), reqDTO.getGameId(), reqDTO.getGameType());
        if (checkParam(reqDTO.getUid(), reqDTO.getRoomId())) {
            logger.error("check param not pass. uid={} roomId={}", reqDTO.getUid(), reqDTO.getRoomId());
            return createResult(reqDTO, HttpCode.PARAM_ERROR, null);
        }
        DistributeLock lock = new DistributeLock(SUD_LOCK_KEY + reqDTO.getRoomId(), 20);
        try {
            lock.lock();
            return createResult(reqDTO, HttpCode.SUCCESS, sudService.changeTeam(reqDTO));
        } catch (GameException e) {
            throw e;
        } catch (Exception e) {
            logger.error("sud game change team error gameId={} uid={} {}", reqDTO.getGameId(), reqDTO.getUid(), e.getMessage(), e);
            throw new GameException(GameHttpCode.FAILURE);
        } finally {
            lock.unlock();
        }
    }

    /**
     * 修改游戏模式
     */
    @PostMapping("change_mode")
    public String changeMode(HttpServletRequest request) {
        SudGameDTO reqDTO = RequestUtils.getSendData(request, SudGameDTO.class);
        logger.info("sud game change game mode uid={} gameId={} gameType={} rule={}", reqDTO.getUid(), reqDTO.getGameId(), reqDTO.getGameType() , reqDTO.getRule());
        if (checkParam(reqDTO.getUid(), reqDTO.getRoomId())) {
            logger.error("check param not pass. uid={} roomId={}", reqDTO.getUid(), reqDTO.getRoomId());
            return createResult(reqDTO, HttpCode.PARAM_ERROR, null);
        }
        DistributeLock lock = new DistributeLock(SUD_LOCK_KEY + reqDTO.getRoomId(), 20);
        try {
            lock.lock();
            return createResult(reqDTO, HttpCode.SUCCESS, sudService.changeMode(reqDTO));
        } catch (GameException e) {
            throw e;
        } catch (Exception e) {
            logger.error("sud game change game mode error gameId={} uid={} {}", reqDTO.getGameId(), reqDTO.getUid(), e.getMessage(), e);
            throw new GameException(GameHttpCode.FAILURE);
        } finally {
            lock.unlock();
        }
    }


    /**
     * 开始游戏
     */
    @PostMapping("start_game")
    public String startSudGame(HttpServletRequest request) {
        SudGameDTO reqDTO = RequestUtils.getSendData(request, SudGameDTO.class);
        logger.info("start sud game. uid={} roomId={} gameId={}", reqDTO.getUid(), reqDTO.getRoomId(), reqDTO.getGameId());
        if (checkParam(reqDTO.getUid(), reqDTO.getRoomId())) {
            logger.error("check param not pass. uid={} roomId={}", reqDTO.getUid(), reqDTO.getRoomId());
            return createResult(reqDTO, HttpCode.PARAM_ERROR, null);
        }
        DistributeLock lock = new DistributeLock(SUD_LOCK_KEY + reqDTO.getRoomId(), 20);
        try {
            lock.lock();
            return createResult(reqDTO, HttpCode.SUCCESS, sudService.startGame(reqDTO));
        } catch (GameException e) {
            throw e;
        } catch (Exception e) {
            logger.error("start sud game error roomId={} uid={}", reqDTO.getRoomId(), reqDTO.getUid(), e);
            throw new GameException(GameHttpCode.FAILURE);
        } finally {
            lock.unlock();
        }
    }

    /**
     * 退出游戏
     */
    @PostMapping("quit_game")
    public String quitGame(HttpServletRequest request) {
        SudGameDTO reqDTO = RequestUtils.getSendData(request, SudGameDTO.class);
        logger.info("player quit sud game roomId={} uid={} aid={} gameId={}", reqDTO.getRoomId(), reqDTO.getUid(), reqDTO.getAid(), reqDTO.getGameId());
        if (checkParam(reqDTO.getUid(), reqDTO.getRoomId())) {
            logger.error("check param not pass. uid={} roomId={}", reqDTO.getUid(), reqDTO.getRoomId());
            return createResult(reqDTO, HttpCode.PARAM_ERROR, null);
        }
        DistributeLock lock = new DistributeLock(SUD_LOCK_KEY + reqDTO.getRoomId(), 20);
        try {
            lock.lock();
            return createResult(reqDTO, HttpCode.SUCCESS, sudService.quitGame(reqDTO));
        } catch (GameException e) {
            throw e;
        } catch (Exception e) {
            logger.error("quit sud game error roomId={} uid={}", reqDTO.getRoomId(), reqDTO.getUid());
            throw e;
        } finally {
            lock.unlock();
        }
    }

    /**
     * 检查房间游戏状态
     */
    @PostMapping("check")
    public String check(HttpServletRequest request) {
        JSONObject paramObj = RequestUtils.getSendDataFromCache(request);
        HttpEnvData envData = HttpEnvData.create(paramObj);
        String roomId = paramObj.getString("roomId");
        int gameType = paramObj.getIntValue("gameType");
        gameType = gameType != 0 ? gameType : 2;
        logger.info("check sud game roomId={} gameType={} uid={} os={} versionCode={}",
                roomId, gameType, envData.getUid(), envData.getOs(), envData.getVersioncode());
        if (StringUtils.isEmpty(roomId)) {
            return createResult(envData, HttpCode.PARAM_ERROR, null);
        }
        SudGameConfigInfo sudGameConfigInfo = sudGameConfig.getSudGameInfoMap().get(gameType);
        if (sudGameConfigInfo == null) {
            logger.info("not find sudGame gameType={}", gameType);
            return createResult(envData, HttpCode.PARAM_ERROR, null);
        }
        int onlineVersion = sudGameConfigInfo.getOnlineVersion();
        if (!AppVersionUtils.versionCheck(onlineVersion, envData)) {
            return createResult(envData, GameHttpCode.NEED_UPGRADE, null);
        }
        MongoRoomData roomData = mongoRoomDao.getDataFromCache(roomId);
        if (null == roomData) {
            return createResult(envData, HttpCode.PARAM_ERROR, null);
        }
        // 即构小游戏只能同时存在一种
        sudService.checkExistOtherSudGame(envData, roomId, gameType);
        try {
            CheckVO checkVO = new CheckVO();
            setGameConfigInfo(roomId, gameType, envData, roomData, checkVO);
            CreateSudGameVO createSudGameVO = new CreateSudGameVO();
            SudGameData data = sudService.checkGame(roomId);
            if (data == null || data.getStatus() == SudGameConstant.GAME_CLOSED
                    || data.getStatus() == SudGameConstant.GAME_FINISH || gameType != data.getGameType()) {
                logger.info("check game, can not find sud game data, roomId ={}", roomId);
                data = new SudGameData();
            }
            BeanUtils.copyProperties(data, createSudGameVO);
            createSudGameVO.setGameId(null == data.getGameId() ? null : data.getGameId().toString());
            checkVO.setGameInfo(createSudGameVO);
            return createResult(envData, HttpCode.SUCCESS, checkVO);
        } catch (Exception e) {
            logger.error("check sud game error uid={} roomId={}", envData.getUid(), roomId, e);
            return createResult(envData, GameHttpCode.SERVER_ERROR, null);
        }
    }

    private void setGameConfigInfo(String roomId, int gameType, HttpEnvData envData, MongoRoomData roomData, CheckVO checkVO) {
        SudGameConfigInfo sudGameConfigInfo = sudGameConfig.getSudGameInfoMap().get(gameType);
        checkVO.getConfigInfo().setCoinFeeList(sudGameConfigInfo.getCoinFeeList()); // v8.50 新版本金币费用列表
        checkVO.getConfigInfo().setDiamondFeeList(sudGameConfigInfo.getDiamondFeeList()); // v8.50 新版本钻石费用列表
        if (gameType == SudGameConstant.LUDO_GAME) {
            // Ludo的游戏配置
            checkVO.getConfigInfo().setCreateGame(roomData.getCreate_game());
            checkVO.getConfigInfo().setCurrencyIndex(defaultCurrencyIndex);
            checkVO.getConfigInfo().setCurrencyInfoList(AppVersionUtils.versionCheck(8361, envData) ? new ArrayList<>(sudGameConfigInfo.getCurrencyInfoMap().values()) : new ArrayList<>(sudGameConfig.getLudoCurrencyInfoMap().values()));
        } else if (gameType == SudGameConstant.UMO_GAME) {
            // UMO的游戏配置
            checkVO.getConfigInfo().setCreateGame(roomConfigDao.getIntRoomConfig(roomId, RoomConfigDao.CREATE_UMO, 0));
            checkVO.getConfigInfo().setCurrencyIndex(defaultUmoCurrencyIndex);
            checkVO.getConfigInfo().setCurrencyInfoList(new ArrayList<>(sudGameConfigInfo.getCurrencyInfoMap().values()));
        } else if (gameType == SudGameConstant.MONSTER_CRUSH_GAME) {
            // Monster Crush的游戏配置
            checkVO.getConfigInfo().setCreateGame(roomConfigDao.getIntRoomConfig(roomId, RoomConfigDao.CREATE_MONSTER_CRUSH, 0));
            checkVO.getConfigInfo().setCurrencyIndex(defaultUmoCurrencyIndex);
            checkVO.getConfigInfo().setCurrencyInfoList(new ArrayList<>(sudGameConfigInfo.getCurrencyInfoMap().values()));
            checkVO.getConfigInfo().setRuleUrl("https://static.youstar.live/gameplay/");
        } else if (gameType == SudGameConstant.DOMINO_GAME) {
            // Domino的游戏配置
            checkVO.getConfigInfo().setCreateGame(roomConfigDao.getIntRoomConfig(roomId, RoomConfigDao.CREATE_DOMINO, 0));
            checkVO.getConfigInfo().setRuleUrl("https://static.youstar.live/gp_rule/");
        } else {
            checkVO.getConfigInfo().setCreateGame(roomConfigDao.getIntRoomConfig(roomId, RoomConfigDao.CREATE_SUD_GAME + gameType, 0));
            checkVO.getConfigInfo().setCurrencyIndex(defaultUmoCurrencyIndex);
            checkVO.getConfigInfo().setRuleUrl("https://static.youstar.live/game_rule" + gameType + "/");
        }
    }

    /**
     * 获取签名(不对外开放)
     */
    @PostMapping("get_signature")
    public String getAppServiceSignature(HttpServletRequest request) {
        JSONObject paramObj = RequestUtils.getSendDataFromCache(request);
        HttpEnvData envData = HttpEnvData.create(paramObj);
        logger.info("get app service signature. requestId={}", envData.getRequestId());
        if (ServerType.PRODUCT.equals(ServerConfig.getServerType())) {
            return createResult(envData, HttpCode.SUCCESS, null);
        }
        String signature = sudService.getAppServiceSignature();
        return createResult(envData, HttpCode.SUCCESS, signature);
    }

    /**
     * 获取即构游戏列表(不对外开放)
     */
    @PostMapping("get_game_list")
    public String getGameList(HttpServletRequest request) {
        JSONObject paramObj = RequestUtils.getSendDataFromCache(request);
        HttpEnvData envData = HttpEnvData.create(paramObj);
        if (ServerType.PRODUCT.equals(ServerConfig.getServerType())) {
            return createResult(envData, HttpCode.SUCCESS, null);
        }
        GetGameListDTO dto = new GetGameListDTO();
        dto.setApp_id(sudGameConfig.getAppId());
        dto.setApp_secret(sudGameConfig.getAppSecret());
        ApiResult<JSONObject> gameList = sudGameApi.getGameList(dto);
        return createResult(envData, HttpCode.SUCCESS, gameList);
    }

    /**
     * 获取即构游戏列表(不对外开放，机器人调用)
     */
    @PostMapping("add_ai")
    public String addAi(HttpServletRequest request) {
        SudGameDTO req = RequestUtils.getSendData(request, SudGameDTO.class);
        logger.info("add ai uid={} gameId={}", req.getUid(), req.getGameId());
        return createResult(req, HttpCode.SUCCESS, sudService.addAi(req));
    }

    /**
     * 快速匹配接口
     */
    @PostMapping("matching")
    public String matching(HttpServletRequest request) {
        SudGameDTO req = RequestUtils.getSendData(request, SudGameDTO.class);
        long startTime = System.currentTimeMillis();
        MatchingVO matching = sudService.matching(req);
        logger.info("matching game uid={} gameType={} cost={}", req.getUid(), req.getGameType(), System.currentTimeMillis() - startTime);
        return createResult(req, HttpCode.SUCCESS, matching);
    }


    /**
     * 扣费事件
     */
    @PostMapping("create_order")
    public String createOrder(HttpServletRequest request) {
        SudGameCreateOrderDTO reqDTO = RequestUtils.getSendData(request, SudGameCreateOrderDTO.class);
        logger.info("create order. gameId={} gameType={} roomId={} uid={} aid={}  sud reqDTO={}",
                reqDTO.getGameId(), reqDTO.getGameType(), reqDTO.getRoomId(), reqDTO.getUid(), reqDTO.getAid(),
                reqDTO);
        if (checkParam(reqDTO.getUid(), reqDTO.getRoomId())) {
            logger.error("create order param not pass. uid={} roomId={}", reqDTO.getUid(), reqDTO.getRoomId());
            return createResult(reqDTO, HttpCode.PARAM_ERROR, null);
        }
//        int onlineVersion = sudGameConfig.getSudGameInfoMap().get(reqDTO.getGameType()).getOnlineVersion();
//        if (!AppVersionUtils.versionCheck(onlineVersion, reqDTO)) {
//            return createResult(reqDTO, GameHttpCode.NEED_UPGRADE, null);
//        }
        sudService.creatOrder(reqDTO);
        return createResult(reqDTO, HttpCode.SUCCESS, new Object());
    }


    /**
     * 快速匹新版游戏房接口
     */
    @PostMapping("gameRoomMatching")
    public String gameRoomMatching(HttpServletRequest request) {
        SudGameDTO req = RequestUtils.getSendData(request, SudGameDTO.class);
        logger.info("gameRoomMatching req={}", JSONObject.toJSONString(req));
        long startTime = System.currentTimeMillis();
        MatchingVO matching = sudGameRoomService.gameRoomMatching(req);
        logger.info("gameRoomMatching uid={} gameType={} cost={}", req.getUid(), req.getGameType(), System.currentTimeMillis() - startTime);
        return createResult(req, HttpCode.SUCCESS, matching);
    }


    /**
     * 简单校验uid和roomId
     */
    private boolean checkParam(String uid, String roomId) {
        return StringUtils.isEmpty(uid) || StringUtils.isEmpty(roomId) || roomId.length() < 2;
    }


    /**
     * 快速匹配接口
     */
    @PostMapping("sudGameRoomClear")
    public HttpResult<?> sudGameRoomClear(HttpServletRequest request) {
        SudGameDTO req = RequestUtils.getSendData(request, SudGameDTO.class);
        sudService.sudGameRoomClear(req);
        logger.info("sudGameRoomClear uid={} roomId={}", req.getUid(), req.getRoomId());
        return HttpResult.getOk();
    }
}
