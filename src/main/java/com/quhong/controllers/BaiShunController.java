package com.quhong.controllers;

import com.alibaba.fastjson.JSONObject;
import com.quhong.core.distribution.DistributeLock;
import com.quhong.dto.*;
import com.quhong.enums.HttpCode;
import com.quhong.handler.WebController;
import com.quhong.service.BaiShunService;
import com.quhong.utils.RequestUtils;
import com.quhong.vo.BaiShunResult;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

/**
 * 百顺小游戏
 *
 * <AUTHOR>
 * @date 2023/7/31
 */
@RestController
@RequestMapping("${baseUrl}bai_shun_game/")
public class BaiShunController extends WebController {

    private static final Logger logger = LoggerFactory.getLogger(BaiShunController.class);

    private static final String LOCK_KEY = "baiShunGameChangeBeans_";

    @Resource
    private BaiShunService baiShunService;

    /**
     * 获取调⽤接⼝的⻓期令牌
     * 调用方：百顺游戏服务
     */
    @PostMapping("/get_sstoken")
    public Object getSSToken(@RequestBody BsGetSSTokenDTO reqParam) {
        logger.info("get sstoken. reqParam={}", JSONObject.toJSONString(reqParam));
        try {
            return baiShunService.getSSToken(reqParam);
        } catch (Exception e) {
            logger.error("get sstoken error. {}", e.getMessage(), e);
            return BaiShunResult.getError(1021, "server error");
        }
    }

    /**
     * 刷新长期令牌
     * 调用方：百顺游戏服务
     */
    @PostMapping("/update_sstoken")
    public Object updateSSToken(@RequestBody BsUpdateSSTokenDTO reqParam) {
        logger.info("update sstoken. reqParam={}", JSONObject.toJSONString(reqParam));
        try {
            return baiShunService.updateSSToken(reqParam);
        } catch (Exception e) {
            logger.error("update sstoken error. {}", e.getMessage(), e);
            return BaiShunResult.getError(1021, "server error");
        }
    }

    /**
     * 获取用户信息
     * 调用方：百顺游戏服务
     */
    @PostMapping("/get_user_info")
    public Object getUserInfo(@RequestBody BsGetUserInfoDTO reqParam) {
        logger.info("get user info. reqParam={}", JSONObject.toJSONString(reqParam));
        try {
            return baiShunService.getUserInfo(reqParam);
        } catch (Exception e) {
            logger.error("get user info error. {}", e.getMessage(), e);
            return BaiShunResult.getError(1021, "server error");
        }
    }

    /**
     * 游戏下注和结算修改app的平台货币
     * 调用方：游戏服务
     */
    @PostMapping("/change_balance")
    public Object changeBalance(@RequestBody BsChangeBalanceDTO reqParam) {
        // logger.info("change user balance. reqParam={}", JSONObject.toJSONString(reqParam));
        DistributeLock lock = new DistributeLock(LOCK_KEY + reqParam.getUser_id());
        try {
            lock.lock();
            return baiShunService.changeBalance(reqParam);
        } catch (Exception e) {
            logger.error("change user balance error. reqParam={} {}", JSONObject.toJSONString(reqParam), e.getMessage(), e);
            return BaiShunResult.getError(1021, "server error");
        } finally {
            lock.unlock();
        }
    }

    /**
     * 获取百顺⼩游戏信息
     * 调用方：客户端
     */
    @PostMapping("/one_game_info")
    public String getGameInfo(HttpServletRequest request) {
        BsGameDTO reqDTO = RequestUtils.getSendData(request, BsGameDTO.class);
        logger.info("get game info. uid={} gameId={}", reqDTO.getUid(), reqDTO.getGame_id());
        return createResult(reqDTO, HttpCode.SUCCESS, baiShunService.getGameInfo(reqDTO));
    }

    /**
     * 获取百顺⼩游戏列表
     * 调用方：客户端
     */
    @PostMapping("/gamelist")
    public String getGameList(HttpServletRequest request) {
        BsGameDTO reqDTO = RequestUtils.getSendData(request, BsGameDTO.class);
        logger.info("get game info list. uid={} gameListType={}", reqDTO.getUid(), reqDTO.getGame_list_type());
        return createResult(reqDTO, HttpCode.SUCCESS, baiShunService.getGameList(reqDTO));
    }
}
