package com.quhong.controllers;

import com.quhong.core.distribution.DistributeLock;
import com.quhong.dto.PkGameDTO;
import com.quhong.enums.GameHttpCode;
import com.quhong.enums.HttpCode;
import com.quhong.exception.GameException;
import com.quhong.handler.WebController;
import com.quhong.service.PkGameService;
import com.quhong.utils.RequestUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

/**
 * PK游戏
 *
 * <AUTHOR>
 * @date 2022/11/3
 */
@RestController
@RequestMapping("${baseUrl}/pk/")
public class PkGameController extends WebController {

    private static final Logger logger = LoggerFactory.getLogger(PkGameController.class);

    public static final String PK_LOCK_KEY = "pk_game_";

    @Resource
    private PkGameService pkGameService;

    /**
     * 检查房间内所有游戏状态，红包，转盘，pk，真心话
     */
    @PostMapping("room/status")
    public String checkRoomGameStatus(HttpServletRequest request) {
        PkGameDTO reqDTO = RequestUtils.getSendData(request, PkGameDTO.class);
        logger.info("check room game status. uid={} roomId={}", reqDTO.getUid(), reqDTO.getRoom_id());
        return createResult(reqDTO, HttpCode.SUCCESS, pkGameService.checkRoomGameStatus(reqDTO));
    }

    /**
     * pk大厅
     */
    @PostMapping("list")
    public String pkList(HttpServletRequest request) {
        PkGameDTO reqDTO = RequestUtils.getSendData(request, PkGameDTO.class);
        logger.info("get pk game list. uid={} page={} ", reqDTO.getUid(), reqDTO.getPage());
        return createResult(reqDTO, HttpCode.SUCCESS, pkGameService.pkList());
    }

    /**
     * pk准备，pk创建之前调用
     */
    @PostMapping("prepare")
    public String pkPrepare(HttpServletRequest request) {
        PkGameDTO reqDTO = RequestUtils.getSendData(request, PkGameDTO.class);
        logger.info("pk game prepare. uid={} roomId={} ", reqDTO.getUid(), reqDTO.getRoom_id());
        return createResult(reqDTO, HttpCode.SUCCESS, pkGameService.pkPrepare());
    }

    /**
     * 创建一个pk游戏
     */
    @PostMapping("create")
    public String createPk(HttpServletRequest request) {
        PkGameDTO reqDTO = RequestUtils.getSendData(request, PkGameDTO.class);
        logger.info("create pk game. uid={} roomId={} pkType={} pkTime={} pkGid={} pkGIcon={}", reqDTO.getUid(),
                reqDTO.getRoom_id(), reqDTO.getPk_type(), reqDTO.getPk_time(), reqDTO.getPk_gid(), reqDTO.getPk_gicon());
        DistributeLock lock = new DistributeLock(PK_LOCK_KEY + reqDTO.getRoom_id());
        try {
            lock.lock();
            return createResult(reqDTO, HttpCode.SUCCESS, pkGameService.createPk(reqDTO));
        } catch (GameException e) {
            throw e;
        } catch (Exception e) {
            logger.error("create pk game. error. uid={} roomId={}", reqDTO.getUid(), reqDTO.getRoom_id());
            throw e;
        } finally {
            lock.unlock();
        }
    }

    /**
     * 邀请好友pk
     */
    @PostMapping("invite_friend")
    public String inviteFriendPk(HttpServletRequest request) {
        PkGameDTO reqDTO = RequestUtils.getSendData(request, PkGameDTO.class);
        logger.info("invite friend pk. uid={} aid={} pkGameId={} ", reqDTO.getUid(), reqDTO.getAid(), reqDTO.getPk_game_id());
        return createResult(reqDTO, GameHttpCode.INVITE_SUCCESS, pkGameService.inviteFriendPk(reqDTO));
    }

    /**
     * 接受pk (已废弃)
     */
    @PostMapping("receive")
    public String receivePk(HttpServletRequest request) {
        PkGameDTO reqDTO = RequestUtils.getSendData(request, PkGameDTO.class);
        logger.info("receive pk game. uid={} pkGameId={} ", reqDTO.getUid(), reqDTO.getPk_game_id());
        return createResult(reqDTO, HttpCode.SUCCESS, pkGameService.receivePk(reqDTO));
    }

    /**
     * 取消pk
     */
    @PostMapping("cancel")
    public String cancelPk(HttpServletRequest request) {
        PkGameDTO reqDTO = RequestUtils.getSendData(request, PkGameDTO.class);
        logger.info("cancel pk game. roomId={} uid={} pkGameId={} ", reqDTO.getRoomId(), reqDTO.getUid(), reqDTO.getPk_game_id());
        DistributeLock lock = new DistributeLock(PK_LOCK_KEY + reqDTO.getPk_game_id());
        try {
            lock.lock();
            pkGameService.cancelPk(reqDTO);
        } catch (GameException e) {
            throw e;
        } catch (Exception e) {
            logger.error("cancel pk game error. uid={} gameId={}", reqDTO.getUid(), reqDTO.getPk_game_id());
            throw e;
        } finally {
            lock.unlock();
        }
        return createResult(reqDTO, GameHttpCode.CANCEL_PK_SUCCESS, null);
    }

    /**
     * 结束pk (已废弃)
     */
    @PostMapping("end")
    public String endPk(HttpServletRequest request) {
        PkGameDTO reqDTO = RequestUtils.getSendData(request, PkGameDTO.class);
        logger.info("end pk game. pid={} ", reqDTO.getPid());
        pkGameService.endPk(reqDTO);
        return createResult(reqDTO, HttpCode.SUCCESS, null);
    }

    /**
     * 加入pk
     */
    @PostMapping("join_game")
    public String joinPk(HttpServletRequest request) {
        PkGameDTO reqDTO = RequestUtils.getSendData(request, PkGameDTO.class);
        logger.info("join pk game. uid={} pkGameId={}", reqDTO.getUid(), reqDTO.getPk_game_id());
        if (StringUtils.isEmpty(reqDTO.getPk_game_id())) {
            logger.error("join pk game param error. uid={} pkGameId={}", reqDTO.getUid(), reqDTO.getPk_game_id());
            return createResult(reqDTO, HttpCode.PARAM_ERROR, null);
        }
        DistributeLock lock = new DistributeLock(PK_LOCK_KEY + reqDTO.getPk_game_id());
        try {
            lock.lock();
            return createResult(reqDTO, GameHttpCode.JOIN_PK_GAME_SUCCESS, pkGameService.joinPk(reqDTO));
        } catch (GameException e) {
            throw e;
        } catch (Exception e) {
            logger.error("join pk game error. uid={} gameId={}", reqDTO.getUid(), reqDTO.getPk_game_id());
            throw e;
        } finally {
            lock.unlock();
        }
    }

    /**
     * 加入房间时检查是否存在pk游戏
     */
    @PostMapping("check_pk")
    public String checkPk(HttpServletRequest request) {
        PkGameDTO reqDTO = RequestUtils.getSendData(request, PkGameDTO.class);
        logger.info("check pk game. roomId={} uid={} pid={}", reqDTO.getRoom_id(), reqDTO.getUid(), reqDTO.getPid());
        return createResult(reqDTO, HttpCode.SUCCESS, pkGameService.checkPk(reqDTO));
    }

    /**
     * 获取PK排行榜
     */
    @PostMapping("mic/ranking")
    public String pkRanking(HttpServletRequest request) {
        PkGameDTO reqDTO = RequestUtils.getSendData(request, PkGameDTO.class);
        logger.info("get pk ranking.opt_type={}", reqDTO.getOpt_type());
        return createResult(reqDTO, HttpCode.SUCCESS, pkGameService.pkRanking(reqDTO));
    }

    /* =============================以下为房间pk接口================================== */

    /**
     * 创建房间pk
     */
    @PostMapping("room/create")
    public String createRoomPk(HttpServletRequest request) {
        PkGameDTO reqDTO = RequestUtils.getSendData(request, PkGameDTO.class);
        logger.info("create room pk game. uid={} roomId={} pkType={} pkTime={}", reqDTO.getUid(),
                reqDTO.getRoom_id(), reqDTO.getPk_type(), reqDTO.getPk_time());
        DistributeLock lock = new DistributeLock(PK_LOCK_KEY + reqDTO.getRoom_id());
        try {
            lock.lock();
            return createResult(reqDTO, HttpCode.SUCCESS, pkGameService.createRoomPk(reqDTO));
        } catch (GameException e) {
            throw e;
        } catch (Exception e) {
            logger.error("create room pk game. uid={} roomId={}", reqDTO.getUid(), reqDTO.getRoom_id());
            throw e;
        } finally {
            lock.unlock();
        }
    }

    /**
     * 房间pk大厅(展示所有竞赛，除了邀请好友在等待好友回应的pk游戏)
     */
    @PostMapping("room/list")
    public String roomPkList(HttpServletRequest request) {
        PkGameDTO reqDTO = RequestUtils.getSendData(request, PkGameDTO.class);
        logger.info("get room pk game list. uid={} page={}", reqDTO.getUid(), reqDTO.getPage());
        return createResult(reqDTO, HttpCode.SUCCESS, pkGameService.roomPkList());
    }

    /**
     * 邀请好友加入房间pk
     */
    @PostMapping("room/invite")
    public String inviteFriendRoomPk(HttpServletRequest request) {
        PkGameDTO reqDTO = RequestUtils.getSendData(request, PkGameDTO.class);
        logger.info("invite friend room pk. roomId={} uid={} aid={} pid={}", reqDTO.getRoom_id(), reqDTO.getUid(), reqDTO.getAid(), reqDTO.getPid());
        return createResult(reqDTO, GameHttpCode.INVITE_SUCCESS, pkGameService.inviteFriendRoomPk(reqDTO));
    }

    /**
     * 接受房间pk
     */
    @PostMapping("room/accept")
    public String acceptRoomPk(HttpServletRequest request) {
        PkGameDTO reqDTO = RequestUtils.getSendData(request, PkGameDTO.class);
        logger.info("accept room pk. uid={} pid={}", reqDTO.getUid(), reqDTO.getPid());
        if (StringUtils.isEmpty(reqDTO.getPid())) {
            logger.error("accept room pk param error. uid={} pid={}", reqDTO.getUid(), reqDTO.getPid());
            return createResult(reqDTO, HttpCode.PARAM_ERROR, null);
        }
        DistributeLock lock = new DistributeLock(PK_LOCK_KEY + reqDTO.getPid());
        try {
            lock.lock();
            return createResult(reqDTO, HttpCode.SUCCESS, pkGameService.acceptRoomPk(reqDTO));
        } catch (GameException e) {
            throw e;
        } catch (Exception e) {
            logger.error("accept room pk error. uid={} gameId={}", reqDTO.getUid(), reqDTO.getPid());
            throw e;
        } finally {
            lock.unlock();
        }
    }

    /**
     * 取消房间pk
     */
    @PostMapping("room/cancle")
    public String cancelRoomPk(HttpServletRequest request) {
        PkGameDTO reqDTO = RequestUtils.getSendData(request, PkGameDTO.class);
        logger.info("cancel room pk. roomId={} uid={} pid={}", reqDTO.getRoom_id(), reqDTO.getUid(), reqDTO.getPid());
        DistributeLock lock = new DistributeLock(PK_LOCK_KEY + reqDTO.getPid());
        try {
            lock.lock();
            pkGameService.cancelRoomPk(reqDTO);
        } catch (GameException e) {
            throw e;
        } catch (Exception e) {
            logger.error("cancel room pk. uid={} pid={}", reqDTO.getUid(), reqDTO.getPid());
            throw e;
        } finally {
            lock.unlock();
        }
        return createResult(reqDTO, HttpCode.SUCCESS, null);
    }

    /**
     * 搜索房间好友
     */
    @PostMapping("room/search")
    public String searchRoomFriend(HttpServletRequest request) {
        PkGameDTO reqDTO = RequestUtils.getSendData(request, PkGameDTO.class);
        logger.info("search room friend. uid={} key={}", reqDTO.getUid(), reqDTO.getKey());
        return createResult(reqDTO, HttpCode.SUCCESS, pkGameService.searchRoomFriend(reqDTO));
    }

    /**
     * 展示在线好友房间
     */
    @PostMapping("room/list/friend_room")
    public String onlineFriendRoomList(HttpServletRequest request) {
        PkGameDTO reqDTO = RequestUtils.getSendData(request, PkGameDTO.class);
        logger.info("show online friend room list. uid={} page={}", reqDTO.getUid(), reqDTO.getPage());
        return createResult(reqDTO, HttpCode.SUCCESS, pkGameService.onlineFriendRoomList(reqDTO));
    }

    /**
     * 加入房间时检查是否存在房间pk游戏
     */
    @PostMapping("room/check_pk")
    public String checkRoomPk(HttpServletRequest request) {
        PkGameDTO reqDTO = RequestUtils.getSendData(request, PkGameDTO.class);
        logger.info("check room pk game. roomId={} uid={} pid={}", reqDTO.getRoom_id(), reqDTO.getUid(), reqDTO.getPid());
        return createResult(reqDTO, HttpCode.SUCCESS, pkGameService.checkRoomPk(reqDTO));
    }

    /**
     * 获取房间PK排行榜
     */
    @PostMapping("room/ranking")
    public String roomPkRanking(HttpServletRequest request) {
        PkGameDTO reqDTO = RequestUtils.getSendData(request, PkGameDTO.class);
        logger.info("get room pk ranking. opt_type={}", reqDTO.getOpt_type());
        return createResult(reqDTO, HttpCode.SUCCESS, pkGameService.roomPkRanking(reqDTO));
    }
}
