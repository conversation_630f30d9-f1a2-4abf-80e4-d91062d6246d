package com.quhong.controllers;

import com.quhong.enums.HttpCode;
import com.quhong.handler.WebController;
import com.quhong.sud.dto.SudGameDTO;
import com.quhong.sud.service.SudService;
import com.quhong.sud.vo.MatchingVO;
import com.quhong.utils.RequestUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

/**
 * 即构小游戏
 *
 * <AUTHOR>
 * @date 2022/6/30
 */
@RestController
@RequestMapping("/java_game/sud/")
public class SudIOSController extends WebController {

    private static final Logger logger = LoggerFactory.getLogger(SudIOSController.class);
    @Resource
    private SudService sudService;

    /**
     * 快速匹配接口
     */
    @PostMapping("matching")
    public String matching(HttpServletRequest request) {
        SudGameDTO req = RequestUtils.getSendData(request, SudGameDTO.class);
        long startTime = System.currentTimeMillis();
        MatchingVO matching = sudService.matching(req);
        logger.info("matching game uid={} gameType={} cost={}", req.getUid(), req.getGameType(), System.currentTimeMillis() - startTime);
        return createResult(req, HttpCode.SUCCESS, matching);
    }
}
