package com.quhong.controllers;

import com.quhong.core.distribution.DistributeLock;
import com.quhong.dto.TurntableDTO;
import com.quhong.enums.GameHttpCode;
import com.quhong.enums.HttpCode;
import com.quhong.exception.GameException;
import com.quhong.handler.WebController;
import com.quhong.service.TurntableService;
import com.quhong.utils.RequestUtils;
import com.quhong.vo.CreateTurntableVO;
import com.quhong.vo.TurntableGameVO;
import com.quhong.vo.StartTurntableVO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

/**
 * 转盘游戏
 *
 * <AUTHOR>
 * @date 2022/10/13
 */
@RestController
@RequestMapping("${baseUrl}/game_turntable/")
public class TurntableController extends WebController {

    private static final Logger logger = LoggerFactory.getLogger(TurntableController.class);

    private static final String TURNTABLE_LOCK_KEY = "create_turntable_game_";

    @Resource
    private TurntableService turntableService;

    /**
     * 创建游戏
     */
    @PostMapping("create_game")
    public String create(HttpServletRequest request) {
        TurntableDTO reqDTO = RequestUtils.getSendData(request, TurntableDTO.class);
        logger.info("create turntable game. uid={} roomId={} maxPlayers={} joinBeans={} joinSelf={}", reqDTO.getUid(), reqDTO.getRoom_id(), reqDTO.getMax_players(), reqDTO.getJoin_beans(), reqDTO.getJoin_self());
        if (!checkParam(reqDTO)) {
            return createResult(reqDTO, HttpCode.PARAM_ERROR, null);
        }
        DistributeLock lock = new DistributeLock(TURNTABLE_LOCK_KEY + reqDTO.getRoom_id());
        try {
            lock.lock();
            CreateTurntableVO vo = turntableService.createGame(reqDTO);
            return createResult(reqDTO, GameHttpCode.CREATE_GAME_SUCCESS, vo);
        } catch (GameException e) {
            throw e;
        } catch (Exception e) {
            logger.error("create turntable game error. roomId={} uid={} {}", reqDTO.getRoom_id(), reqDTO.getUid(), e.getMessage(), e);
            throw e;
        } finally {
            lock.unlock();
        }
    }

    /**
     * 加入转盘游戏
     */
    @PostMapping("join_game")
    public String join(HttpServletRequest request) {
        TurntableDTO reqDTO = RequestUtils.getSendData(request, TurntableDTO.class);
        logger.info("join turntable game. uid={} roomId={}", reqDTO.getUid(), reqDTO.getRoom_id());
        if (StringUtils.isEmpty(reqDTO.getUid()) || StringUtils.isEmpty(reqDTO.getGame_id())) {
            logger.info("uid and game_id can not be empty.");
            return createResult(reqDTO, HttpCode.PARAM_ERROR, null);
        }
        DistributeLock lock = new DistributeLock(TURNTABLE_LOCK_KEY + reqDTO.getRoom_id());
        try {
            lock.lock();
            TurntableGameVO vo = turntableService.joinGame(reqDTO);
            return createResult(reqDTO, HttpCode.SUCCESS, vo);
        } catch (GameException e) {
            throw e;
        } catch (Exception e) {
            logger.error("join turntable game error. roomId={} uid={}", reqDTO.getRoom_id(), reqDTO.getUid());
            throw e;
        } finally {
            lock.unlock();
        }
    }

    /**
     * 开始转盘游戏
     */
    @PostMapping("start_game")
    public String start(HttpServletRequest request) {
        TurntableDTO reqDTO = RequestUtils.getSendData(request, TurntableDTO.class);
        logger.info("start turntable game. uid={} roomId={}", reqDTO.getUid(), reqDTO.getRoom_id());
        if (StringUtils.isEmpty(reqDTO.getUid()) || StringUtils.isEmpty(reqDTO.getGame_id())) {
            logger.info("uid and game_id can not be empty.");
            return createResult(reqDTO, HttpCode.PARAM_ERROR, null);
        }
        DistributeLock lock = new DistributeLock(TURNTABLE_LOCK_KEY + reqDTO.getRoom_id());
        try {
            lock.lock();
            StartTurntableVO vo = turntableService.startGame(reqDTO);
            return createResult(reqDTO, HttpCode.SUCCESS, vo);
        } catch (GameException e) {
            throw e;
        } catch (Exception e) {
            logger.error("start turntable game error. roomId={} uid={}", reqDTO.getRoom_id(), reqDTO.getUid());
            throw e;
        } finally {
            lock.unlock();
        }
    }

    /**
     * 转盘游戏等待中点击关闭
     */
    @PostMapping("close_at_waiting")
    public String closeAtWaiting(HttpServletRequest request) {
        TurntableDTO reqDTO = RequestUtils.getSendData(request, TurntableDTO.class);
        logger.info("close at waiting turntable game. uid={} gameId={}", reqDTO.getUid(), reqDTO.getGame_id());
        if (StringUtils.isEmpty(reqDTO.getUid()) || StringUtils.isEmpty(reqDTO.getGame_id())) {
            logger.info("uid and game_id can not be empty.");
            return createResult(reqDTO, HttpCode.PARAM_ERROR, null);
        }
        DistributeLock lock = new DistributeLock(TURNTABLE_LOCK_KEY + reqDTO.getRoom_id());
        try {
            lock.lock();
            turntableService.closeAtWaiting(reqDTO);
            return createResult(reqDTO, HttpCode.SUCCESS, null);
        } catch (GameException e) {
            throw e;
        } catch (Exception e) {
            logger.error("close turntable game error. roomId={} uid={}", reqDTO.getRoom_id(), reqDTO.getUid());
            throw e;
        } finally {
            lock.unlock();
        }
    }

    /**
     * 进入房间时，请求当前房间是否存在转盘游戏
     */
    @PostMapping("check_game_for_enter_room")
    public String checkGameForEnterRoom(HttpServletRequest request) {
        TurntableDTO reqDTO = RequestUtils.getSendData(request, TurntableDTO.class);
        logger.info("check turntable game for enter room. uid={} roomId={}", reqDTO.getUid(), reqDTO.getRoom_id());
        if (StringUtils.isEmpty(reqDTO.getUid()) || StringUtils.isEmpty(reqDTO.getRoom_id()) || reqDTO.getRoom_id().length() < 2) {
            logger.error("uid or room_id param error. uid={} roomId={}", reqDTO.getUid(), reqDTO.getRoom_id());
            return createResult(reqDTO, HttpCode.PARAM_ERROR, null);
        }
        return createResult(reqDTO, HttpCode.SUCCESS, turntableService.checkGameForEnterRoom(reqDTO));
    }

    /**
     * 检测房间内是否存在游戏
     */
    @PostMapping("check_all")
    public String checkAll(HttpServletRequest request) {
        TurntableDTO reqDTO = RequestUtils.getSendData(request, TurntableDTO.class);
        logger.info("check all game for enter room. uid={} roomId={}", reqDTO.getUid(), reqDTO.getRoom_id());
        if (StringUtils.isEmpty(reqDTO.getUid()) || StringUtils.isEmpty(reqDTO.getRoom_id()) || reqDTO.getRoom_id().length() < 2) {
            logger.error("uid or room_id param error. uid={} roomId={}", reqDTO.getUid(), reqDTO.getRoom_id());
            return createResult(reqDTO, HttpCode.PARAM_ERROR, null);
        }
        return createResult(reqDTO, HttpCode.SUCCESS, turntableService.checkAll(reqDTO));
    }

    /**
     * 校验创建游戏参数
     */
    private boolean checkParam(TurntableDTO reqDTO) {
        if (StringUtils.isEmpty(reqDTO.getUid()) || StringUtils.isEmpty(reqDTO.getRoom_id())) {
            logger.info("uid and room_id can not be empty.");
            return false;
        }
        int maxPlayers = reqDTO.getMax_players();
        if (maxPlayers != 6 && maxPlayers != 8 && maxPlayers != 10) {
            logger.info("maxPlayers can only be 6 or 8 or 10. maxPlayers={}", maxPlayers);
            return false;
        }
        int joinBeans = reqDTO.getJoin_beans();
        if (joinBeans < 0 || joinBeans > 500) {
            return false;
        }
        return reqDTO.getJoin_self() == 0 || reqDTO.getJoin_self() == 1;
    }
}
