package com.quhong.controllers;

import com.quhong.core.distribution.DistributeLock;
import com.quhong.enums.HttpCode;
import com.quhong.exception.GameException;
import com.quhong.handler.WebController;
import com.quhong.sud.dto.*;
import com.quhong.sud.service.SudGameService;
import com.quhong.sud.vo.BaseVO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;

/**
 * 即构游戏（第三方调用）
 *
 * <AUTHOR>
 * @date 2022/6/29
 */
@RestController
@RequestMapping("${baseUrl}/sud_game")
public class SudGameController extends WebController {

    private static final Logger logger = LoggerFactory.getLogger(SudGameController.class);

    private static final String SUD_LOCK_KEY = "sud_game_";

    @Resource
    private SudGameService sudGameService;

    /**
     * 登录接口，获取针对当前用户(UID)的短期令牌Code
     * 调用方：接入端APP
     *
     * @param reqParam
     * @return
     */
    @PostMapping("/login")
    public Object loginGame(@RequestBody LoginDTO reqParam) {
        logger.info("login game.");
        return sudGameService.login(reqParam.getUserId());
    }

    /**
     * 短期令牌Code更换长期令牌SSToken
     * 调用方：游戏服务
     *
     * @param reqParam
     * @return
     */
    @PostMapping("/get_sstoken")
    public Object getSSToken(@RequestBody GetSSTokenDTO reqParam) {
        logger.info("get sstoken. code={}",reqParam.getCode());
        return sudGameService.getSSToken(reqParam.getCode());
    }

    /**
     * 刷新长期令牌
     * 调用方：游戏服务
     *
     * @param reqParam
     * @return
     */
    @PostMapping("/update_sstoken")
    public Object updateSSToken(@RequestBody UpdateSSTokenDTO reqParam) {
        logger.info("update sstoken.");
        return sudGameService.updateSSToken(reqParam.getSsToken());
    }

    /**
     * 获取用户信息
     * 调用方：游戏服务
     *
     * @param reqParam
     * @return
     */
    @PostMapping("/get_user_info")
    public Object getUserInfo(@RequestBody GetUserInfoDTO reqParam) {
        return sudGameService.getUserInfo(reqParam.getSsToken());
    }

    /**
     * 游戏服务端上报游戏接入方的游戏的数据
     * 调用方：游戏服务
     * <a href="https://docs.sud.tech/zh-CN/app/Server/HttpsCallback/report_game_info.html">...</a>
     *
     * @param reqParam
     * @return
     */
    @PostMapping("/report_game_info")
    public Object reportGameInfo(@RequestBody ReportGameInfoDTO reqParam) {
        logger.info("report game info.");
        ReportGameInfoDTO.ReportMsg reportMsg = reqParam.getReportMsg();
        DistributeLock lock = new DistributeLock(SUD_LOCK_KEY + reportMsg.getRoomId(), 20);
        try {
            lock.lock();
            return sudGameService.reportGameInfo(reqParam);
        } catch (GameException e) {
            throw e;
        } catch (Exception e) {
            logger.error("report game info error. roomId={} gameId={}", reportMsg.getRoomId(), reportMsg.getReportGameInfoKey());
            throw e;
        } finally {
            lock.unlock();
        }
    }


}
