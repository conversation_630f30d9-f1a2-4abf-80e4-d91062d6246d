package com.quhong.controllers;

import com.alibaba.fastjson.JSONObject;
import com.quhong.dto.FingerGuessDTO;
import com.quhong.enums.GameHttpCode;
import com.quhong.enums.HttpCode;
import com.quhong.handler.WebController;
import com.quhong.service.FingerGuessService;
import com.quhong.utils.RequestUtils;
import com.quhong.vo.FingerGuessRankingVO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.*;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;

/**
 * 猜拳游戏
 *
 * <AUTHOR>
 * @date 2022/11/3
 */
@RestController
@RequestMapping("${baseUrl}/finger_guess/")
public class FingerGuessController extends WebController {

    private static final Logger logger = LoggerFactory.getLogger(FingerGuessController.class);

    @Resource
    private FingerGuessService fingerGuessService;

    /**
     * 创建猜拳
     */
    @PostMapping("create")
    public String create(HttpServletRequest request) {
        FingerGuessDTO reqDTO = RequestUtils.getSendData(request, FingerGuessDTO.class);
        logger.info("create finger guess game. req={}", JSONObject.toJSONString(reqDTO));
        if (checkCreateGameParam(reqDTO)) {
            return createResult(reqDTO, HttpCode.PARAM_ERROR, null);
        }
        reqDTO.setRobot(false);
        fingerGuessService.create(reqDTO);
        return createResult(reqDTO, GameHttpCode.CREATE_ROSHAMBO_GAME_SUCCESS, null);
    }

    /**
     * 加入猜拳
     */
    @PostMapping("join")
    public String join(HttpServletRequest request) {
        FingerGuessDTO reqDTO = RequestUtils.getSendData(request, FingerGuessDTO.class);
        logger.info("join finger guess game. req={}", JSONObject.toJSONString(reqDTO));
        if (checkJoinGameParam(reqDTO)) {
            return createResult(reqDTO, HttpCode.PARAM_ERROR, null);
        }
        reqDTO.setRobot(false);
        return createResult(reqDTO, HttpCode.SUCCESS, fingerGuessService.join(reqDTO));
    }

    /**
     * 猜拳大厅
     */
    @PostMapping("hall")
    public String hall(HttpServletRequest request) {
        FingerGuessDTO reqDTO = RequestUtils.getSendData(request, FingerGuessDTO.class);
        logger.info("get finger guess game hall. uid={}, page={}", reqDTO.getUid(), reqDTO.getPage());
        return createResult(reqDTO, HttpCode.SUCCESS, fingerGuessService.hall(reqDTO));
    }

    /**
     * 猜拳礼物列表
     */
    @PostMapping("gift/list")
    public String giftList(HttpServletRequest request) {
        FingerGuessDTO reqDTO = RequestUtils.getSendData(request, FingerGuessDTO.class);
        logger.info("get finger guess gift list. uid={}", reqDTO.getUid());
        return createResult(reqDTO, HttpCode.SUCCESS, fingerGuessService.giftList());
    }

    /**
     * 猜拳礼物列表
     */
    @GetMapping("giftH5/list")
    public String giftList(@RequestParam String uid) {
        logger.info("get finger guess gift list. uid={}", uid);
        return createResult(HttpCode.SUCCESS, fingerGuessService.giftList());
    }

    /**
     * 我的历史记录
     */
    @PostMapping("my_history")
    public String myHistory(HttpServletRequest request) {
        FingerGuessDTO reqDTO = RequestUtils.getSendData(request, FingerGuessDTO.class);
        logger.info("get my finger guess game history. uid={} page={}", reqDTO.getUid(), reqDTO.getPage());
        return createResult(reqDTO, HttpCode.SUCCESS, fingerGuessService.myHistory(reqDTO.getUid(), reqDTO.getPage(), false));
    }

    /**
     * 我的历史记录
     */
    @GetMapping("my_history/h5")
    public String myHistory(@RequestParam String uid, @RequestParam Integer page) {
        logger.info("get my finger guess game history. uid={} page={}", uid, page);
        return createResult( HttpCode.SUCCESS, fingerGuessService.myHistory(uid, page, true));
    }

    /**
     * 猜拳排行榜
     */
    @GetMapping( "ranking")
    public String ranking(@RequestParam String uid, @RequestParam Integer rankType, @RequestParam Integer giftId) {
        long timeMillis = System.currentTimeMillis();
        logger.info("get finger guess game ranking. uid={} rankType={} giftId={}", uid, rankType, giftId);
        FingerGuessRankingVO vo = fingerGuessService.ranking(uid, rankType, giftId);
        logger.info("get finger guess game ranking. uid={} rankType={} giftId={} timeMillis={}", uid, rankType, giftId, System.currentTimeMillis() - timeMillis);
        return createResult(HttpCode.SUCCESS, vo);
    }

    /**
     * 猜拳动画结束后发送广播
     */
    @PostMapping("to_socketio")
    public String sendBroadcastMessage(HttpServletRequest request) {
        FingerGuessDTO reqDTO = RequestUtils.getSendData(request, FingerGuessDTO.class);
        logger.info("send broadcast message. uid={} g_id={}", reqDTO.getUid(), reqDTO.getG_id());
        fingerGuessService.sendBroadcastMessage(reqDTO);
        return createResult(reqDTO, HttpCode.SUCCESS, null);
    }

    /**
     * 校验加入游戏的参数
     */
    private boolean checkJoinGameParam(FingerGuessDTO reqDTO) {
        if (StringUtils.isEmpty(reqDTO.getRoom_id()) || StringUtils.isEmpty(reqDTO.getUid()) ||
                StringUtils.isEmpty(reqDTO.getG_icon()) || StringUtils.isEmpty(reqDTO.getG_id())) {
            return true;
        }
        return reqDTO.getBeans() == null || reqDTO.getG_type() == null || reqDTO.getGift_id() == null;
    }

    /**
     * 校验创建游戏的参数
     */
    private boolean checkCreateGameParam(FingerGuessDTO reqDTO) {
        if (StringUtils.isEmpty(reqDTO.getRoom_id()) || StringUtils.isEmpty(reqDTO.getUid()) || StringUtils.isEmpty(reqDTO.getG_icon())) {
            return true;
        }
        return reqDTO.getBeans() == null || reqDTO.getG_type() == null || reqDTO.getGift_id() == null;
    }
}
