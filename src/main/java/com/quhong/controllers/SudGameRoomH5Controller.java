package com.quhong.controllers;

import com.alibaba.fastjson.JSONObject;
import com.quhong.core.distribution.DistributeLock;
import com.quhong.datas.HttpResult;
import com.quhong.dto.InnerSudGameDTO;
import com.quhong.dto.StarBeatGameDTO;
import com.quhong.enums.GameHttpCode;
import com.quhong.enums.HttpCode;
import com.quhong.exception.GameException;
import com.quhong.handler.H5Controller;
import com.quhong.service.StarBeatGameService;
import com.quhong.sud.dto.CreateSudGameDTO;
import com.quhong.sud.dto.SudGameCreateOrderDTO;
import com.quhong.sud.dto.SudGameDTO;
import com.quhong.sud.service.SudGameRoomService;
import com.quhong.sud.service.SudService;
import com.quhong.sud.vo.CreateSudGameV2VO;
import com.quhong.sud.vo.CreateSudGameVO;
import com.quhong.utils.RequestUtils;
import com.quhong.vo.InnerSudGameVO;
import com.quhong.vo.StarBeatGameVO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;
import tech.sud.mgp.auth.api.SudCode;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;


@RestController
@RequestMapping("${baseUrl}")
public class SudGameRoomH5Controller extends H5Controller {
    private static final Logger logger = LoggerFactory.getLogger(SudGameRoomH5Controller.class);

    @Resource
    private SudService sudService;
    @Resource
    private SudGameRoomService sudGameRoomService;


    /**
     * 获取游戏code
     */
    @PostMapping("/gameRoomSud/getCode")
    public String getGameCode(@RequestBody SudGameDTO reqDTO) {
        logger.info("get game code.uid={} requestId={}", reqDTO.getUid(), reqDTO.getRequestId());
        if (StringUtils.isEmpty(reqDTO.getUid())) {
            throw new GameException(GameHttpCode.PARAM_ERROR);
        }
        SudCode sudCode = sudService.getGameCode(reqDTO);
        JSONObject result = new JSONObject();
        result.put("code", sudCode.getCode());
        result.put("expire_date", sudCode.getExpireDate());
        return createResult(HttpCode.SUCCESS, result);
    }

    /**
     * 游戏基本信息
     * info
     */
    @PostMapping("/gameRoomSud/info")
    public String info(@RequestBody SudGameDTO dto) {
        logger.info("info. dto={}", dto);
        long start = System.currentTimeMillis();
        if (dto == null) {
            return createResult(HttpCode.PARAM_ERROR, new Object());
        }
        CreateSudGameV2VO vo = sudGameRoomService.info(dto);
        return createResult(HttpCode.SUCCESS, vo);
    }

    /**
     * 加入游戏
     * join
     */
    @PostMapping("/gameRoomSud/join")
    public String join(@RequestBody SudGameDTO dto) {
        logger.info("join. dto={}", JSONObject.toJSONString(dto));
        long start = System.currentTimeMillis();
        if (dto == null || StringUtils.isEmpty(dto.getRoomId())) {
            return createResult(HttpCode.PARAM_ERROR, new Object());
        }
        if (checkParam(dto.getUid(), dto.getRoomId())) {
            logger.error("check param not pass. uid={} roomId={}", dto.getUid(), dto.getRoomId());
            return createResult(HttpCode.PARAM_ERROR, null);
        }
        try (DistributeLock lock = new DistributeLock(SudController.SUD_LOCK_KEY + dto.getRoomId(), 30)) {
            lock.lock();
            CreateSudGameVO vo = sudService.joinGame(dto);
            return createResult(HttpCode.SUCCESS, vo);
        } catch (GameException e) {
            throw e;
        } catch (Exception e) {
            logger.error("join. reqDTO={}", JSONObject.toJSONString(dto), e);
            throw new GameException(GameHttpCode.FAILURE);
        }
    }

    @PostMapping("/gameRoomSud/createOrJoin")
    public String createOrJoin(@RequestBody CreateSudGameDTO reqDTO) {
        logger.info(" createOrJoin. reqDTO={}", JSONObject.toJSONString(reqDTO));
        if (checkParam(reqDTO.getUid(), reqDTO.getRoomId())) {
            logger.error("check param not pass. uid={} roomId={}", reqDTO.getUid(), reqDTO.getRoomId());
            return createResult(HttpCode.PARAM_ERROR, new Object());
        }
        try (DistributeLock lock = new DistributeLock(SudController.SUD_LOCK_KEY + reqDTO.getRoomId(), 30)) {
            lock.lock();
            CreateSudGameV2VO vo = sudService.createOrJoin(reqDTO);
            return createResult(HttpCode.SUCCESS, vo);
        } catch (GameException e) {
            throw e;
        } catch (Exception e) {
            logger.error(" createOrJoin. reqDTO={}", JSONObject.toJSONString(reqDTO), e);
            throw new GameException(GameHttpCode.FAILURE);
        }
    }


    /**
     * 克罗姆游戏才有
     * 变更队伍
     */
    @PostMapping("/gameRoomSud/changeTeam")
    public String changeTeam(@RequestBody SudGameDTO reqDTO) {

        logger.info("changeTeam. dto={}", reqDTO);
        logger.info("sud game change team uid={} gameId={} gameType={}", reqDTO.getUid(), reqDTO.getGameId(), reqDTO.getGameType());
        if (checkParam(reqDTO.getUid(), reqDTO.getRoomId())) {
            logger.error("check param not pass. uid={} roomId={}", reqDTO.getUid(), reqDTO.getRoomId());
            return createResult(HttpCode.PARAM_ERROR, new Object());
        }
        DistributeLock lock = new DistributeLock(SudController.SUD_LOCK_KEY + reqDTO.getRoomId(), 20);
        try {
            lock.lock();
            CreateSudGameVO vo = sudService.changeTeam(reqDTO);
            return createResult(HttpCode.SUCCESS, vo);
        } catch (GameException e) {
            throw e;
        } catch (Exception e) {
            logger.error("sud game change team error gameId={} uid={} {}", reqDTO.getGameId(), reqDTO.getUid(), e.getMessage(), e);
            throw new GameException(GameHttpCode.FAILURE);
        } finally {
            lock.unlock();
        }
    }

    /**
     * 开始游戏
     * start
     */
    @PostMapping("/gameRoomSud/start")
    public String start(@RequestBody SudGameDTO dto) {
        logger.info("start. dto={}", dto);
        long start = System.currentTimeMillis();
        if (dto == null || checkParam(dto.getUid(), dto.getRoomId()) || StringUtils.isEmpty(dto.getGameId())) {
            return createResult(HttpCode.PARAM_ERROR, new Object());
        }
        try (DistributeLock lock = new DistributeLock(SudController.SUD_LOCK_KEY + dto.getRoomId(), 30)) {
            lock.lock();
            CreateSudGameVO vo = sudService.startGame(dto);
            return createResult(HttpCode.SUCCESS, vo);
        } catch (GameException e) {
            throw e;
        } catch (Exception e) {
            logger.error("start. reqDTO={}", JSONObject.toJSONString(dto), e);
            throw new GameException(GameHttpCode.FAILURE);
        }
    }

    /**
     * 更改匹配状态，暂停或者开始
     */
    @PostMapping("/gameRoomSud/pauseMatch")
    public String matchPause(@RequestBody SudGameDTO dto) {
        logger.info("pauseMatch. dto={}", dto);
        long start = System.currentTimeMillis();
        if (dto == null || dto.getPauseType() == null) {
            return createResult(HttpCode.PARAM_ERROR, new Object());
        }
        try (DistributeLock lock = new DistributeLock(SudController.SUD_LOCK_KEY + dto.getRoomId(), 30)) {
            lock.lock();
            CreateSudGameVO vo = sudGameRoomService.matchPause(dto);
            return createResult(HttpCode.SUCCESS, vo);
        } catch (GameException e) {
            throw e;
        } catch (Exception e) {
            logger.error("pauseMatch. reqDTO={}", JSONObject.toJSONString(dto), e);
            throw new GameException(GameHttpCode.FAILURE);
        }
    }

    /**
     * 退出游戏
     */
    @PostMapping("/gameRoomSud/quit")
    public String quite(@RequestBody SudGameDTO dto) {
        logger.info("quite. dto={}", dto);
        long start = System.currentTimeMillis();
        if (dto == null) {
            return createResult(HttpCode.PARAM_ERROR, new Object());
        }
        try (DistributeLock lock = new DistributeLock(SudController.SUD_LOCK_KEY + dto.getRoomId(), 30)) {
            lock.lock();
            CreateSudGameVO vo = sudService.quitGame(dto);
            return createResult(HttpCode.SUCCESS, vo);
        } catch (GameException e) {
            throw e;
        } catch (Exception e) {
            logger.error("quite. reqDTO={}", JSONObject.toJSONString(dto), e);
            throw new GameException(GameHttpCode.FAILURE);
        }
    }

    /**
     * 提示消息
     */
    @PostMapping("/gameRoomSud/remind")
    public String remind(@RequestBody SudGameDTO dto) {
        logger.info("remind. dto={}", dto);
        long start = System.currentTimeMillis();
        if (dto == null) {
            return createResult(HttpCode.PARAM_ERROR, new Object());
        }
        sudGameRoomService.remind(dto);
        return createResult(HttpCode.SUCCESS, new Object());
    }

    /**
     * 扣费事件
     */
    @PostMapping("/gameRoomSud/createOrder")
    public String remind(@RequestBody SudGameCreateOrderDTO dto) {
        logger.info("h5 createOrder. dto={}", dto);
        if (checkParam(dto.getUid(), dto.getRoomId())) {
            logger.error("create order param not pass. uid={} roomId={}", dto.getUid(), dto.getRoomId());
            return createResult(HttpCode.PARAM_ERROR, new Object());
        }
        sudService.creatOrder(dto);
        return createResult(HttpCode.SUCCESS, new Object());
    }

    /**
     * 简单校验uid和roomId
     */
    private boolean checkParam(String uid, String roomId) {
        return StringUtils.isEmpty(uid) || StringUtils.isEmpty(roomId) || roomId.length() < 2;
    }
}
