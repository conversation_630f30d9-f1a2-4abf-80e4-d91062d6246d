package com.quhong.controllers;

import com.alibaba.fastjson.JSONObject;
import com.quhong.config.LudoConfig;
import com.quhong.data.SudGameInfo;
import com.quhong.enums.GameHttpCode;
import com.quhong.enums.HttpCode;
import com.quhong.exception.GameException;
import com.quhong.handler.HttpEnvData;
import com.quhong.handler.WebController;
import com.quhong.ludo.data.CheckData;
import com.quhong.ludo.data.ConfigInfo;
import com.quhong.ludo.data.GameInfo;
import com.quhong.ludo.data.ZipInfo;
import com.quhong.redis.SudGameRedis;
import com.quhong.service.LudoService;
import com.quhong.utils.RequestUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.ArrayList;


@RestController
@RequestMapping("${baseUrl}/ludo/")
public class LudoController extends WebController {
    private static final Logger logger = LoggerFactory.getLogger(LudoController.class);

    private static final String OTHRE_GAME_IN_PROGRESS = "The %s game is playing in the room, please wait for the game to end and try again."; // 有其他游戏正在进行提示
    private static final String OTHRE_GAME_IN_PROGRESS_AR = "هناك لعبة %s تلعب في الغرفة ، يرجى الانتظار حتى تنتهي اللعبة والمحاولة مرة أخرى."; // 有其他游戏正在进行提示阿语

    @Resource
    private LudoService ludoService;
    @Resource
    private LudoConfig ludoConfig;
    @Resource
    private SudGameRedis sudGameRedis;
    @Value("${ludo.md5}")
    private String md5;
    @Value("${ludo.url}")
    private String url;
    @Value("${ludo.defaultCurrencyIndex}")
    private int defaultCurrencyIndex;
    @Value("${ludo.ruleUrl}")
    private String ruleUrl;
    @Value("${ludo.squareUrl}")
    private String squareUrl;
    @Value("${ludo.offline:true}")
    private boolean offline;

    /**
     * 创建游戏
     */
    @PostMapping("create")
    public String create(HttpServletRequest request) {
        JSONObject paramObj = RequestUtils.getSendDataFromCache(request);
        GameInfo gameInfo = paramObj.toJavaObject(GameInfo.class);
        HttpEnvData envData = HttpEnvData.create(paramObj);
        if (offline) {
            throw new GameException(GameHttpCode.OFFLINE);
        }
        String sudGameId = sudGameRedis.getGameIdByRoomId(gameInfo.getRoomId());
        if (!StringUtils.isEmpty(sudGameId)) {
            SudGameInfo sudGameInfo = sudGameRedis.getSudGameInfo(sudGameId);
            return createResult(envData, new HttpCode(3023, String.format(OTHRE_GAME_IN_PROGRESS, sudGameInfo.getGameName()), String.format(OTHRE_GAME_IN_PROGRESS_AR, sudGameInfo.getGameNameAr())), null);
        }
        logger.info("create ludo game roomId={} uid={}", gameInfo.getRoomId(), envData.getUid());
        if (gameInfo.getPlayerNumber() < 2 || gameInfo.getPlayerNumber() > 4) {
            return createResult(envData, GameHttpCode.PARAM_ERROR, null);
        }
        GameInfo game = ludoService.createGame(envData.getUid(), gameInfo);
        return createResult(envData, HttpCode.SUCCESS, game.toWebData());
    }

    /**
     * 加入游戏
     */
    @PostMapping("join")
    public String join(HttpServletRequest request) {
        JSONObject paramObj = RequestUtils.getSendDataFromCache(request);
        String gameId = paramObj.getString("gameId");
        String roomId = paramObj.getString("roomId");
        HttpEnvData envData = HttpEnvData.create(paramObj);
        if (offline && envData.getVersioncode() != 738) {
            return createResult(envData, GameHttpCode.OFFLINE, null);
        }
        logger.info("join ludo game roomId={} uid={} gameId={}", roomId, envData.getUid(), gameId);
        if (StringUtils.isEmpty(gameId) || StringUtils.isEmpty(roomId)) {
            return createResult(envData, HttpCode.PARAM_ERROR, null);
        }
        GameInfo gameInfo = ludoService.joinGame(envData.getUid(), gameId);
        return createResult(envData, HttpCode.SUCCESS, gameInfo.toWebData());
    }

    /**
     * 组队界面
     * 游戏创建者踢人或玩家收到退出游戏
     */
    @PostMapping("kick_out")
    public String kickOut(HttpServletRequest request) {
        JSONObject paramObj = RequestUtils.getSendDataFromCache(request);
        String gameId = paramObj.getString("gameId");
        String roomId = paramObj.getString("roomId");
        String aid = paramObj.getString("aid");
        HttpEnvData envData = HttpEnvData.create(paramObj);
        logger.info("kick out player uid={} aid={} gameId={}", envData.getUid(), aid, gameId);
        if (StringUtils.isEmpty(gameId) || StringUtils.isEmpty(aid) || StringUtils.isEmpty(roomId)) {
            return createResult(envData, HttpCode.PARAM_ERROR, null);
        }
        GameInfo gameInfo = ludoService.kickOut(envData.getUid(), aid, gameId);
        return createResult(envData, HttpCode.SUCCESS, null == gameInfo ? null : gameInfo.toWebData());
    }

    /**
     * 玩家主动退出游戏，创建者退出时解散游戏
     */
    @PostMapping("quit")
    public String quit(HttpServletRequest request) {
        JSONObject paramObj = RequestUtils.getSendDataFromCache(request);
        String gameId = paramObj.getString("gameId");
        String roomId = paramObj.getString("roomId");
        HttpEnvData envData = HttpEnvData.create(paramObj);
        logger.info("player quit game uid={} gameId={}", envData.getUid(), gameId);
        if (StringUtils.isEmpty(gameId) || StringUtils.isEmpty(roomId)) {
            return createResult(envData, HttpCode.PARAM_ERROR, null);
        }
        GameInfo gameInfo = ludoService.quitGame(envData.getUid(), gameId);
        return createResult(envData, HttpCode.SUCCESS, null == gameInfo ? null : gameInfo.toWebData());
    }

    /**
     * 开始游戏
     */
    @PostMapping("start")
    public String start(HttpServletRequest request) {
        JSONObject paramObj = RequestUtils.getSendDataFromCache(request);
        String gameId = paramObj.getString("gameId");
        String roomId = paramObj.getString("roomId");
        HttpEnvData envData = HttpEnvData.create(paramObj);
        logger.info("start ludo game roomId={} uid={} gameId={}", roomId, envData.getUid(), gameId);
        if (StringUtils.isEmpty(gameId)) {
            return createResult(envData, HttpCode.PARAM_ERROR, null);
        }
        GameInfo gameInfo = ludoService.startGame(envData.getUid(), gameId);
        return createResult(envData, HttpCode.SUCCESS, gameInfo.toWebData());
    }

    /**
     * 检查房间游戏状态
     */
    @PostMapping("check")
    public String check(HttpServletRequest request) {
        JSONObject paramObj = RequestUtils.getSendDataFromCache(request);
        String roomId = paramObj.getString("roomId");
        HttpEnvData envData = HttpEnvData.create(paramObj);
        logger.info("check ludo game roomId={} uid={} os={} versionCode={}", roomId, envData.getUid(), envData.getOs(), envData.getVersioncode());
        if (StringUtils.isEmpty(roomId)) {
            return createResult(envData, HttpCode.PARAM_ERROR, null);
        }
        try {
            GameInfo gameInfo = ludoService.check(roomId, envData.getUid());
            if (null == gameInfo) {
                gameInfo = new GameInfo();
            }
            CheckData checkData = new CheckData();
            checkData.setGameInfo(gameInfo.toWebData());

            ConfigInfo configInfo = new ConfigInfo();
            configInfo.setCurrencyIndex(defaultCurrencyIndex);
            configInfo.setCurrencyInfoList(new ArrayList<>(ludoConfig.getCurrencyInfoMap().values()));
            configInfo.setZipInfo(new ZipInfo(md5, url));
            configInfo.setRuleUrl(ruleUrl);
            configInfo.setSquareUrl(squareUrl);

            checkData.setConfigInfo(configInfo);
            return createResult(envData, HttpCode.SUCCESS, checkData);
        } catch (Exception e) {
            logger.error("check ludo game error uid={} roomId={}", envData.getUid(), roomId, e);
            return createResult(envData, GameHttpCode.SERVER_ERROR, null);
        }
    }

    /**
     * 掷骰子
     */
    @PostMapping("throw_dice")
    public String throwDice(HttpServletRequest request) {
        JSONObject paramObj = RequestUtils.getSendDataFromCache(request);
        String gameId = paramObj.getString("gameId");
        String roomId = paramObj.getString("roomId");
        HttpEnvData envData = HttpEnvData.create(paramObj);
        logger.info("ludo game throw dice roomId={} uid={} gameId={}", roomId, envData.getUid(), gameId);
        if (StringUtils.isEmpty(gameId) || StringUtils.isEmpty(roomId)) {
            return createResult(envData, HttpCode.PARAM_ERROR, null);
        }
        GameInfo gameInfo = ludoService.throwDice(envData.getUid(), gameId);
        return createResult(envData, HttpCode.SUCCESS, gameInfo.toWebData());
    }

    /**
     * 游戏操作
     */
    @PostMapping("action")
    public String action(HttpServletRequest request) {
        JSONObject paramObj = RequestUtils.getSendDataFromCache(request);
        String gameId = paramObj.getString("gameId");
        String roomId = paramObj.getString("roomId");
        int diceIndex = paramObj.getIntValue("diceIndex");
        String chessmanId = paramObj.getString("chessmanId");
        HttpEnvData envData = HttpEnvData.create(paramObj);
        logger.info("ludo game action roomId={} uid={} gameId={} diceIndex={} chessmanId={}",
                roomId, envData.getUid(), gameId, diceIndex, chessmanId);
        if (StringUtils.isEmpty(gameId)) {
            return createResult(envData, HttpCode.PARAM_ERROR, null);
        }
        GameInfo gameInfo = ludoService.action(envData.getUid(), gameId, diceIndex, chessmanId);
        return createResult(envData, HttpCode.SUCCESS, gameInfo.toWebData());
    }

    /**
     * 游戏观战
     */
    @PostMapping("stop_watch")
    public String stopWatch(HttpServletRequest request) {
        JSONObject paramObj = RequestUtils.getSendDataFromCache(request);
        String roomId = paramObj.getString("roomId");
        HttpEnvData envData = HttpEnvData.create(paramObj);
        logger.info("stop watch ludo game roomId={} uid={}", roomId, envData.getUid());
        ludoService.stopWatch(envData.getUid(), roomId);
        return createResult(envData, HttpCode.SUCCESS, null);
    }
}
