package com.quhong.service;

import com.quhong.analysis.EventDTO;
import com.quhong.analysis.EventReport;
import com.quhong.analysis.GameRecordLogEvent;
import com.quhong.api.BaiShunGameApi;
import com.quhong.core.concurrency.BaseTaskFactory;
import com.quhong.core.concurrency.tasks.Task;
import com.quhong.core.config.ServerConfig;
import com.quhong.core.redis.BasePlayerRedis;
import com.quhong.core.utils.DateHelper;
import com.quhong.dailyTask.CommonTaskService;
import com.quhong.data.ActorData;
import com.quhong.data.BaiShunGameData;
import com.quhong.data.BaiShunGameInfo;
import com.quhong.data.CommonMqTopicData;
import com.quhong.data.dto.MoneyDetailReq;
import com.quhong.dto.*;
import com.quhong.enums.*;
import com.quhong.exception.GameException;
import com.quhong.feign.DataCenterService;
import com.quhong.image.ImageUrlGenerator;
import com.quhong.mongo.dao.ActorDao;
import com.quhong.monitor.MonitorSender;
import com.quhong.mq.MqSenderService;
import com.quhong.msg.obj.HighlightTextObject;
import com.quhong.msg.room.RoomNotificationMsg;
import com.quhong.mysql.dao.VipInfoDao;
import com.quhong.mysql.dao.WhiteTestDao;
import com.quhong.redis.BaiShunGameRedis;
import com.quhong.redis.GamblingGameRedis;
import com.quhong.redis.GameKingRedis;
import com.quhong.redis.GameRoomRedis;
import com.quhong.room.RoomWebSender;
import com.quhong.vo.BaiShunResult;
import com.quhong.vo.BsChangeBalanceVO;
import com.quhong.vo.BsSSTokenVO;
import com.quhong.vo.BsUserInfoVO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.DigestUtils;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2023/7/31
 */
@Service
public class BaiShunService {

    private static final Logger logger = LoggerFactory.getLogger(BaiShunService.class);

    private static final String APP_CHANNEL = "youstar";

    private static final int BIG_DIAMONDS = ServerConfig.isProduct() ? 5000 : 100;
    private static final String GAME_WIN_BIG_DIAMONDS_MSG = "Congratulations to %s for playing the game and winning a huge diamond reward. Play Now >>";
    private static final String GAME_WIN_BIG_DIAMONDS_MSG_AR = "تهانينا لـ %s على لعب اللعبة والفوز بمكافأة ماسية ضخمة. العب الآن >>";

    private static final Map<Integer, BaiShunGameData> GAME_INFO_MAP = new HashMap<>();

    private static final int PLATFORM_WARNING_LINE = 25 * 10000; // 单款游戏平台亏损25万钻以上时触发告警
    private static final int PERSONAL_WARNING_LINE = 10 * 10000; // 用户个人钻石亏损10万钻时触发告警

    @Resource
    private BasePlayerRedis basePlayerRedis;
    @Resource
    private ActorDao actorDao;
    @Resource
    private DataCenterService dataCenterService;
    @Resource
    private BaiShunGameApi baiShunGameApi;
    @Resource
    private BaiShunGameRedis baiShunGameRedis;
    @Resource
    private RoomWebSender roomWebSender;
    @Resource
    private VipInfoDao vipInfoDao;
    @Resource
    private MonitorSender monitorSender;
    @Resource
    private EventReport eventReport;
    @Resource
    private MqSenderService mqSenderService;
    @Resource
    private GameKingRedis gameKingRedis;
    @Resource
    private WhiteTestDao whiteTestDao;
    @Resource
    private GamblingGameRedis gamblingGameRedis;
    @Resource
    private CommonTaskService commonTaskService;
    @Resource
    private GameMsgService gameMsgService;
    @Resource
    private GameRoomRedis gameRoomRedis;

    private long iosAppId;
    private String iosAppKey;
    private long androidAppId;
    private String androidAppKey;

    @PostConstruct
    public void init() {
        if (ServerConfig.isNotProduct()) {
            iosAppId = 7633847994L;
            iosAppKey = "2c1UX2dzTd3e59anSaKWUzmegOx7Mmht";
            androidAppId = 2135757682L;
            androidAppKey = "Ra8sIfzxayQFM0GEMsrHQc4AnJe5cGH4";

            GAME_INFO_MAP.put(1004, new BaiShunGameData(1004,
                    "Slots",
                    "slots",
                    8471,
                    1,
                    "https://bobilive-com-test.jieyou.shop/game-packages/common-web/slots/1.0.3/web-mobile/index.html?gameMode=3&appId=2135757682",
                    944,
                    "Slots game fee",
                    945,
                    "Slots game rewards"));
            GAME_INFO_MAP.put(1018, new BaiShunGameData(1018,
                    "horse racing",
                    "horse_racing",
                    847,
                    1,
                    "https://bobilive-com-test.jieyou.shop/game-packages/youstar/2135757682/horseracing2/1.0.1/web-mobile/index.html?gameMode=3&appId=2135757682",
                    937,
                    "Horse Racing fee",
                    938,
                    "Horse Racing rewards"));
            GAME_INFO_MAP.put(1016, new BaiShunGameData(1016,
                    "crash",
                    "crash",
                    847,
                    1,
                    "https://bobilive-com-test.jieyou.shop/game-packages/youstar/2135757682/rocket/1.0.4/web-mobile/index.html?gameMode=3&appId=2135757682",
                    939,
                    "Crash game fee",
                    940,
                    "Crash game rewards"));
            GAME_INFO_MAP.put(1022, new BaiShunGameData(1022,
                    "fishing",
                    "fishing",
                    850,
                    3,
                    "https://bobilive-com-test.jieyou.shop/game-packages/common-web/fishing/1.5.1/web-mobile/index.html?gameMode=3&appId=2135757682",
                    946,
                    "Fishing game fee",
                    947,
                    "Fishing game rewards"));
            GAME_INFO_MAP.put(1014, new BaiShunGameData(1014,
                    "fast3",
                    "fast3",
                    853,
                    1,
                    "https://game-center-test.jieyou.shop/game-packages/common-web/threedice/1.1.4/web-mobile/index.html?gameMode=3&appId=2135757682",
                    951,
                    "Fast game fee",
                    952,
                    "Fast game rewards"));
            GAME_INFO_MAP.put(1017, new BaiShunGameData(1017,
                    "Greedy2",
                    "greedy",
                    853,
                    1,
                    "https://game-center-test.jieyou.shop/game-packages/common-web/greedy2/1.1.3/web-mobile/index.html?gameMode=3&appId=2135757682",
                    953,
                    "Greedy2 game fee",
                    954,
                    "Greedy2 game rewards"));
        } else {
            iosAppId = 3749996810L;
            iosAppKey = "c3e2TLSsFqiUJVPNy6Lj5NAU7n8buC1Z";
            androidAppId = 1476635168L;
            androidAppKey = "Q6NuWKIIVXnzAIY3Bkbrx1kN2HZWTiU5";

            GAME_INFO_MAP.put(1004, new BaiShunGameData(1004,
                    "Slots",
                    "slots",
                    8471,
                    1,
                    "https://gamecdn.qmovies.tv/game-packages/common-web/slots/1.0.7/web-mobile/index.html?gameMode=3&appId=1476635168",
                    944,
                    "Slots game fee",
                    945,
                    "Slots game rewards"));
            GAME_INFO_MAP.put(1007, new BaiShunGameData(1007,
                    "angel or devil",
                    "angel_or_devil",
                    847,
                    1,
                    "https://gamecdn.qmovies.tv/game-packages/common-web/angel/1.0.5/web-mobile/index.html?gameMode=3&appId=1476635168",
                    935,
                    "Angel or Devil fee",
                    936,
                    "Angel or Devil rewards"));
            GAME_INFO_MAP.put(1013, new BaiShunGameData(1013,
                    "horse racing",
                    "horse_racing",
                    847,
                    1,
                    "https://gamecdn.qmovies.tv/game-packages/common-web/horseracing/1.0.2/web-mobile/index.html?gameMode=3&appId=1476635168",
                    937,
                    "Horse Racing fee",
                    938,
                    "Horse Racing rewards"));
            GAME_INFO_MAP.put(1018, new BaiShunGameData(1018,
                    "horse racing",
                    "horse_racing",
                    847,
                    1,
                    "https://gamecdn.qmovies.tv/game-packages/common-web/horseracing2/1.0.4/web-mobile/index.html?gameMode=3&appId=1476635168",
                    937,
                    "Horse Racing fee",
                    938,
                    "Horse Racing rewards"));
            GAME_INFO_MAP.put(1016, new BaiShunGameData(1016,
                    "crash",
                    "crash",
                    847,
                    1,
                    "https://gamecdn.qmovies.tv/game-packages/common-web/rocket/1.1.1/web-mobile/index.html?gameMode=3&appId=1476635168",
                    939,
                    "Crash game fee",
                    940,
                    "Crash game rewards"));
            GAME_INFO_MAP.put(1022, new BaiShunGameData(1022,
                    "fishing",
                    "fishing",
                    850,
                    3,
                    "https://gamecdn.qmovies.tv/game-packages/common-web/fishing/1.2.5/web-mobile/index.html?gameMode=3&appId=1476635168",
                    946,
                    "Fishing game fee",
                    947,
                    "Fishing game rewards"));
            GAME_INFO_MAP.put(1014, new BaiShunGameData(1014,
                    "fast3",
                    "fast3",
                    853,
                    1,
                    "https://gamecdn.qmovies.tv/game-packages/common-web/threedice/1.0.3/web-mobile/index.html?gameMode=3&appId=1476635168",
                    951,
                    "Fast game fee",
                    952,
                    "Fast game rewards"));
            GAME_INFO_MAP.put(1017, new BaiShunGameData(1017,
                    "Greedy2",
                    "greedy",
                    853,
                    1,
                    "https://gamecdn.qmovies.tv/game-packages/common-web/greedy2/1.0.8/web-mobile/index.html?gameMode=3&appId=1476635168",
                    953,
                    "Greedy2 game fee",
                    954,
                    "Greedy2 game rewards"));
        }
    }

    public BaiShunResult<BsSSTokenVO> getSSToken(BsGetSSTokenDTO dto) {
        BaiShunResult<BsSSTokenVO> result = checkParam(dto.getUser_id(), dto.getSignature(), dto.getSignature_nonce(), dto.getTimestamp());
        if (result.isError()) {
            return result;
        }
        return BaiShunResult.getOk(BsSSTokenVO.builder()
                .ss_token(basePlayerRedis.getToken(dto.getUser_id()))
                .expire_date((DateHelper.getNowSeconds() + basePlayerRedis.getTokenExpireTime(dto.getUser_id())) * 1000L)
                .build());
    }

    public BaiShunResult<BsSSTokenVO> updateSSToken(BsUpdateSSTokenDTO dto) {
        BaiShunResult<BsSSTokenVO> result = checkParam(dto.getUser_id(), dto.getSignature(), dto.getSignature_nonce(), dto.getTimestamp());
        if (result.isError()) {
            return result;
        }
        return BaiShunResult.getOk(BsSSTokenVO.builder()
                .ss_token(basePlayerRedis.getToken(dto.getUser_id()))
                .expire_date((DateHelper.getNowSeconds() + basePlayerRedis.getTokenExpireTime(dto.getUser_id())) * 1000L)
                .build());
    }

    public BaiShunResult<BsUserInfoVO> getUserInfo(BsGetUserInfoDTO dto) {
        BaiShunResult<BsUserInfoVO> result = checkParam(dto.getUser_id(), dto.getSignature(), dto.getSignature_nonce(), dto.getTimestamp());
        if (result.isError()) {
            return result;
        }
        ActorData actorData = actorDao.getActorData(dto.getUser_id());
        Integer[] dataArr = getUserTypeAndReleaseCond(actorData);
        int userType = dataArr[0];
        int releaseCond = dataArr[1];
        logger.info("getUserInfo. uid={} userType={} releaseCond={}", dto.getUser_id(), userType, releaseCond);
        return BaiShunResult.getOk(BsUserInfoVO.builder()
                .user_id(actorData.getUid())
                .user_name(actorData.getName())
                .user_avatar(ImageUrlGenerator.generateRoomUserUrl(actorData.getHead()))
                .balance(actorData.getBeans())
                .user_type(userType)
                .release_cond(releaseCond)
                .build());
    }

    public BaiShunResult<BsChangeBalanceVO> changeBalance(BsChangeBalanceDTO dto) {
        BaiShunResult<BsChangeBalanceVO> result = checkParam(dto.getUser_id(), dto.getSignature(), dto.getSignature_nonce(), dto.getTimestamp());
        if (result.isError()) {
            return result;
        }
        ActorData actorData = actorDao.getActorData(dto.getUser_id());
        if (dto.getCurrency_diff() != 0) {
            MoneyDetailReq moneyDetailReq = new MoneyDetailReq();
            moneyDetailReq.setRandomId();
            moneyDetailReq.setRoomId(dto.getRoom_id());
            moneyDetailReq.setUid(dto.getUser_id());
            moneyDetailReq.setChanged(dto.getCurrency_diff());
            moneyDetailReq.setMtime(DateHelper.getNowSeconds());
            BaiShunGameData gameInfo = GAME_INFO_MAP.get(dto.getGame_id());
            if (dto.getCurrency_diff() > 0) {
                moneyDetailReq.setAtype(gameInfo.getIncBeansActType());
                moneyDetailReq.setTitle(gameInfo.getIncBeansTitle());
                moneyDetailReq.setDesc(gameInfo.getIncBeansTitle());
                mqSenderService.asyncChargeDiamonds(moneyDetailReq);
                if (dto.getCurrency_diff() >= BIG_DIAMONDS) {
                    // 发送游戏赢得大额钻石消息
                    sendGameWinBigDiamondsMsg(dto.getGame_id(), dto.getRoom_id(), dto.getUser_id(), dto.getCurrency_diff());
                }
                if (!whiteTestDao.isMemberByType(dto.getUser_id(), WhiteTestDao.WHITE_TYPE_RID)) {
                    // 游戏王活动
                    gameKingRedis.updateRanking(dto.getUser_id(), dto.getCurrency_diff());
                }
                gameRoomRedis.updateLastPlayGameTime(dto.getUser_id(), gameInfo.getGameType());
                commonTaskService.sendCommonTaskMq(new CommonMqTopicData(dto.getUser_id(), dto.getRoom_id(), "", "", CommonMqTaskConstant.WIN_BAI_SHUN_GAME, dto.getCurrency_diff()));
            } else {
                if (gameInfo.getGameId() == 1022) {
                    return BaiShunResult.getError(1019, "The game will be removed soon and betting is not supported at this time");
                }
                moneyDetailReq.setAtype(gameInfo.getReduceBeansActType());
                moneyDetailReq.setTitle(gameInfo.getReduceBeansTitle());
                moneyDetailReq.setDesc(gameInfo.getReduceBeansTitle());
                ApiResult<String> apiResult = dataCenterService.reduceBeans(moneyDetailReq);
                if (apiResult.isError()) {
                    if (1 == apiResult.getCode().getCode()) {
                        return BaiShunResult.getError(1008, "diamond not enough");
                    }
                    logger.error("reduce beans error, msg={}", apiResult.getCode().getMsg());
                    return BaiShunResult.getError(1021, "server error");
                }
                // EnterRoomMqData playGameMqData = new  EnterRoomMqData();
                // playGameMqData.setUid(dto.getUser_id());
                // playGameMqData.setRoomId(dto.getRoom_id());
                // playGameMqData.setItem(gameInfo.getGameType());
                // mqSenderService.sendTopicMsgToMq(MqSenderService.ROUTE_KEY_USER_PLAY_BAI_SHUN_GAME, playGameMqData);
            }
            // 赢钱减少用户盈利额度，下注增加用户盈利额度
            gamblingGameRedis.incrementProfit(dto.getUser_id(), -dto.getCurrency_diff());
            // 赢钱增加用户亏损额度，下注减少用户亏损额度
            gamblingGameRedis.incrementLoss(dto.getUser_id(), dto.getCurrency_diff());
            gameDiamondAlert(dto.getGame_id(), dto.getUser_id(), dto.getCurrency_diff());
        }
        // 游戏埋点
        doEventReport(dto);
        return BaiShunResult.getOk(BsChangeBalanceVO.builder()
                .currency_balance(actorData.getBeans() + dto.getCurrency_diff())
                .build());
    }

    public BaiShunGameInfo getGameInfo(BsGameDTO dto) {
        BsGetGameInfoDTO reqParam = buildBsGetGameInfoDTO(dto.getOs());
        reqParam.setGame_id(dto.getGame_id());
        ApiResult<BaiShunGameInfo> result = baiShunGameApi.getGameInfo(reqParam);
        if (result.isError()) {
            throw new GameException(HttpCode.SERVER_ERROR);
        }
        return result.getData();
    }

    public List<BaiShunGameInfo> getGameList(BsGameDTO dto) {
        BsGetGameInfoDTO reqParam = buildBsGetGameInfoDTO(dto.getOs());
        reqParam.setGame_list_type(dto.getGame_list_type());
        ApiResult<List<BaiShunGameInfo>> result = baiShunGameApi.getGameList(reqParam);
        if (result.isError()) {
            throw new GameException(HttpCode.SERVER_ERROR);
        }
        return result.getData();
    }

    private Integer[] getUserTypeAndReleaseCond(ActorData actorData) {
        // 手动设置用户盈亏
        int userLoss = gamblingGameRedis.getUserLoss(actorData.getUid());
        if (userLoss > 0) {
            // 亏损黑名单
            return new Integer[]{3, userLoss};
        } else {
            // 盈利白名单
            int userProfit = gamblingGameRedis.getUserProfit(actorData.getUid());
            if (userProfit > 0) {
                return new Integer[]{2, userProfit};
            }
        }
        return new Integer[]{0, 0};
    }

    private <T> BaiShunResult<T> checkParam(String userId, String signature, String signatureNonce, long timestamp) {
        ActorData actorData = actorDao.getActorDataFromCache(userId);
        if (actorData == null) {
            return BaiShunResult.getError(1001, "can not find user data");
        }
        String appKey = actorData.getIntOs() == 0 ? androidAppKey : iosAppKey;
        if (invalidSignature(signature, signatureNonce, appKey, timestamp)) {
            return BaiShunResult.getError(1003, "signature error");
        }
        return BaiShunResult.getOk();
    }

    private BsGetGameInfoDTO buildBsGetGameInfoDTO(int os) {
        BsGetGameInfoDTO reqParam = new BsGetGameInfoDTO();
        long appId = os == 0 ? androidAppId : iosAppId;
        String appKey = os == 0 ? androidAppKey : iosAppKey;
        reqParam.setApp_id(appId);
        reqParam.setApp_channel(APP_CHANNEL);
        reqParam.setSignature_nonce(UUID.randomUUID().toString().replace("-", ""));
        reqParam.setTimestamp(DateHelper.getNowSeconds());
        reqParam.setSignature(getSignature(reqParam.getSignature_nonce(), appKey, reqParam.getTimestamp()));
        return reqParam;
    }

    private void sendGameWinBigDiamondsMsg(int gameId, String roomId, String uid, int change) {
        ActorData actorData = actorDao.getActorDataFromCache(uid);
        BaseTaskFactory.getFactory().addSlow(new Task() {
            @Override
            protected void execute() {
                BaiShunGameData gameInfo = GAME_INFO_MAP.get(gameId);
                if (gameInfo.getGameId() == 1017) {
                    logger.info("sendGameWinBigDiamondsMsg uid:{} roomId:{} change:{} gameId:{}", uid, roomId, change, gameId);
                    gameMsgService.pushCommonBigReward(actorData.getUid(), change, gameInfo.getGameName(), gameInfo.getGameName(),
                            gameInfo.getWebType(), gameInfo.getWebUrl());
                } else {
                    if (StringUtils.isEmpty(roomId) || change < BIG_DIAMONDS) {
                        return;
                    }
                    RoomNotificationMsg msg = new RoomNotificationMsg();
                    msg.setUid(actorData.getUid());
                    msg.setUser_name(actorData.getName());
                    msg.setUser_head(ImageUrlGenerator.generateRoomUserUrl(actorData.getHead(), vipInfoDao.getIntVipLevelFromCache(actorData.getUid())));
                    msg.setText(String.format(GAME_WIN_BIG_DIAMONDS_MSG, actorData.getName()));
                    msg.setText_ar(String.format(GAME_WIN_BIG_DIAMONDS_MSG_AR, actorData.getName()));
                    List<HighlightTextObject> list = new ArrayList<>();
                    HighlightTextObject object = new HighlightTextObject();
                    object.setText(actorData.getName());
                    object.setHighlightColor("#FFE200");
                    list.add(object);
                    msg.setHighlight_text(list);
                    msg.setHighlight_text_ar(list);

                    msg.setGame_type(gameInfo.getGameType());
                    msg.setWeb_url(gameInfo.getWebUrl());
                    msg.setWeb_type(gameInfo.getWebType());
                    msg.setBc_status(1);
                    roomWebSender.sendRoomWebMsg(roomId, "", msg, false, gameInfo.getVersion());
                }
            }
        });
    }

    private boolean invalidSignature(String signature, String signatureNonce, String appKey, long timestamp) {
        int nowTime = DateHelper.getNowSeconds();
        if (nowTime < timestamp || nowTime - timestamp > 15) {
            // 请求时间戳超过15秒会报错
            logger.error("timeStamp param error. timestamp={}, nowTime={}", timestamp, nowTime);
            return true;
        }
        // signatureNonce全局唯⼀,防重放攻击
        if (baiShunGameRedis.checkSignatureNonce(signatureNonce)) {
            return true;
        }
        baiShunGameRedis.saveSignatureNonce(signatureNonce);
        return !Objects.equals(signature, getSignature(signatureNonce, appKey, timestamp));
    }

    private void gameDiamondAlert(int gameId, String uid, int change) {
        try {
            // 过去24小时内账号钻石亏损10万钻时触发告警，后续每增加1万再触发。
            int userBeansChange = baiShunGameRedis.incUserBeansChange(gameId + "", uid, change);
            if (userBeansChange <= -PERSONAL_WARNING_LINE) {
                int warnLevel = (-userBeansChange - PERSONAL_WARNING_LINE) / 10000 + 1;
                int oldWarnLevel = baiShunGameRedis.getPersonalWarnLevel(uid);
                logger.info("userBeansChange={} warnLevel={} oldWarnLevel={}", userBeansChange, warnLevel, oldWarnLevel);
                if (warnLevel > oldWarnLevel) {
                    ActorData actorData = actorDao.getActorDataFromCache(uid);
                    String desc = String.format("ID：%s用户当天累积亏损%s万钻\n", actorData != null ? actorData.getRid() : uid, -userBeansChange / 10000);
                    noticeWarn("用户亏损告警", "用户钻石亏损", gameId, desc);
                    baiShunGameRedis.setPersonalWarnLevel(uid, warnLevel);
                }
            }
            // 单款游戏平台亏损25万钻时触发告警，每增加1万触发1次。
            int platformBeansChange = baiShunGameRedis.incPlatformBeansChange(gameId, change);
            if (platformBeansChange >= PLATFORM_WARNING_LINE) {
                int warnLevel = (platformBeansChange - PLATFORM_WARNING_LINE) / 10000 + 1;
                int oldWarnLevel = baiShunGameRedis.getPlatformWarnLevel(gameId + "");
                if (warnLevel > oldWarnLevel) {
                    StringBuilder desc = new StringBuilder(String.format("平台亏损钻石达%s万钻\n", platformBeansChange / 10000));
                    Map<String, Integer> userProfitRankingMap = baiShunGameRedis.getUserProfitRankingMap(gameId + "", 5);
                    for (Map.Entry<String, Integer> entry : userProfitRankingMap.entrySet()) {
                        ActorData actorData = actorDao.getActorDataFromCache(entry.getKey());
                        desc.append(String.format("ID：%s用户当天赢了%s钻;\n", actorData != null ? actorData.getRid() : entry.getKey(), entry.getValue()));
                    }
                    noticeWarn("平台亏损告警", "平台钻石亏损", gameId, desc.toString());
                    baiShunGameRedis.setPlatformWarnLevel(gameId + "", warnLevel);
                }
            }
        } catch (Exception e) {
            logger.error("gameDiamondAlert error. gameId={} uid={} change={} {}", gameId, uid, change, e.getMessage(), e);
        }
    }

    public void noticeWarn(String title, String name, int gameId, String desc) {
        String gameName = GAME_INFO_MAP.get(gameId).getGameName();
        String content = title + "\n"
                + ">告警名: " + name + "\n"
                + ">所属项目: Youstar \n"
                + ">游戏项目: " + gameName + "\n"
                + ">预警详情: " + desc
                + ">处理人: @王博";
        monitorSender.customMarkdown(MonitorWarnName.WARN_GAME_PLAY, content);
    }

    private void doEventReport(BsChangeBalanceDTO dto) {
        GameRecordLogEvent event = new GameRecordLogEvent();
        event.setUid(dto.getUser_id());
        event.setGame_id(dto.getGame_round_id());
        event.setRoom_id(dto.getRoom_id());
        event.setIs_creator(1);
        event.setGame_type(getEventGameType(dto.getGame_id()));
        event.setGet_diamonds(Math.max(dto.getCurrency_diff(), 0));
        event.setCost_diamonds(-Math.min(dto.getCurrency_diff(), 0));
        event.setCtime(DateHelper.getNowSeconds());
        eventReport.track(new EventDTO(event));
    }

    private int getEventGameType(int gameId) {
        if (gameId == 1016) {
            return EventGameTypeConstant.CRASH;
        } else if (gameId == 1018 || gameId == 1013) {
            return EventGameTypeConstant.HORSE_RACING;
        } else if (gameId == 1004) {
            return EventGameTypeConstant.SLOTS;
        } else if (gameId == 1014) {
            return EventGameTypeConstant.FAST3;
        } else {
            return EventGameTypeConstant.FISHING;
        }
    }

    private String getSignature(String signatureNonce, String appKey, long timestamp) {
        return DigestUtils.md5DigestAsHex((signatureNonce + appKey + timestamp).getBytes());
    }
}
