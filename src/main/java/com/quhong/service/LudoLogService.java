package com.quhong.service;

import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import com.quhong.mysql.data.LudoLogData;
import com.quhong.mysql.mapper.ustar_log.LudoLogMapper;
import org.springframework.stereotype.Service;

import java.util.Collection;

@Service
public class LudoLogService extends ServiceImpl<LudoLogMapper, LudoLogData> {

    @Override
    public boolean saveBatch(Collection<LudoLogData> entityList) {
        return saveBatch(entityList, 500);
    }
}
