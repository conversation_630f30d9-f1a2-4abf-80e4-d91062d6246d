package com.quhong.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Interner;
import com.google.common.collect.Interners;
import com.quhong.analysis.CreateGameLogEvent;
import com.quhong.analysis.EventDTO;
import com.quhong.analysis.EventReport;
import com.quhong.analysis.StarBeatGameLogEvent;
import com.quhong.config.AsyncConfig;
import com.quhong.core.config.ServerConfig;
import com.quhong.core.utils.DateHelper;
import com.quhong.dailyTask.CommonTaskService;
import com.quhong.data.*;
import com.quhong.data.dto.MoneyDetailReq;
import com.quhong.data.dto.ResourcesDTO;
import com.quhong.dto.StarBeatGameDTO;
import com.quhong.enums.*;
import com.quhong.exception.H5GameException;
import com.quhong.feign.DataCenterService;
import com.quhong.feign.DataResourcesService;
import com.quhong.image.ImageUrlGenerator;
import com.quhong.mongo.dao.ActorDao;
import com.quhong.mongo.dao.CarnivalGameDao;
import com.quhong.mongo.dao.ResourceConfigDao;
import com.quhong.mongo.data.CarnivalGameData;
import com.quhong.mongo.data.ResourceConfigData;
import com.quhong.mq.MqSenderService;
import com.quhong.msg.obj.HighlightTextObject;
import com.quhong.msg.room.RoomCommonScrollMsg;
import com.quhong.msg.room.RoomNotificationMsg;
import com.quhong.mysql.dao.RechargeDailyInfoDao;
import com.quhong.mysql.dao.UserResourceDao;
import com.quhong.mysql.data.QuestionData;
import com.quhong.redis.StarBeatGameRedis;
import com.quhong.redis.StarBeatPoolRedis;
import com.quhong.room.RoomWebSender;
import com.quhong.room.redis.RoomPlayerRedis;
import com.quhong.utils.ArithmeticUtils;
import com.quhong.utils.RoomUtils;
import com.quhong.vo.StarBeatGameVO;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.Executor;
import java.util.concurrent.ThreadLocalRandom;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
public class StarBeatGameService {
    private static final Logger logger = LoggerFactory.getLogger(StarBeatGameService.class);

    public static final String RES_DESC = "Carnival of Star";

    public static final String PLAY_DIAMOND_DESC = "Play Carnival of Star";
    public static final String PLAY_DIAMOND_TITLE = "Play Carnival of Star";
    public static final int PLAY_DIAMOND_TYPE = 970;


    public static final int ADD_DIAMOND_TYPE = 971;
    public static final String ADD_DIAMOND_TITLE = "Carnival of Star";

    private static final int AWARD_NORMAL_TYPE = 0; // 0 普通抽奖中出
    private static final int AWARD_LUCKY_TYPE = 1;//1幸运一击
    private static final int HISTORY_PAGE_SIZE = 30;
    private static final int RESOURCE_BOX_TYPE = -1;

    private final static String URL_STAR_BEAT = ServerConfig.isProduct() ? "https://static.youstar.live/star_beat/" : "https://test2.qmovies.tv/star_beat/";
    private final static String VIEW_EN = "View>";
    private final static String VIEW_AR = "عرض>";
    private final static String TEXT_EN = "Congratulations to #username# for getting #prize_name# (worth #prize_value#\uD83D\uDC8E) in Carnival of Star.View>";
    private final static String TEXT_AR = "تهانينا لـ #username# للحصول على #prize_name# (بقيمة #prize_value#\uD83D\uDC8E) في كرنفال النجوم.عرض>";

    private final static String TEXT_EN_M = "Congratulations to #username# for getting #prize_name# (worth #prize_value#\uD83D\uDC8E) in Carnival of Star.";
    private final static String TEXT_AR_M = "تهانينا لـ #username# للحصول على #prize_name# (بقيمة #prize_value#\uD83D\uDC8E) في كرنفال النجوم.";
    private final static String GAME_STAR_BEAT = "game_star_beat";

    /**
     * 奖品次序
     */
    private static final Comparator<StarBeatGameVO.PrizeConfigVO> SB_ORDER_ASC = Comparator.comparing(StarBeatGameVO.PrizeConfigVO::getOrderNum);

    /**
     * 选项价值升序
     */
    private static final Comparator<CarnivalGameData.PrizeFree> BT_ORDER_ASC = Comparator.comparing(CarnivalGameData.PrizeFree::getPrizePrice);

    public static final List<Integer> ALL_PRIZE_TYPE_LIST = Arrays.asList(
            BaseDataResourcesConstant.TYPE_MIC,
            BaseDataResourcesConstant.TYPE_BUDDLE,
            BaseDataResourcesConstant.TYPE_RIDE,
            BaseDataResourcesConstant.TYPE_RIPPLE,
            BaseDataResourcesConstant.TYPE_BADGE,
            BaseDataResourcesConstant.TYPE_FLOAT_SCREEN,
            BaseDataResourcesConstant.TYPE_MINE_BACKGROUND,
            BaseDataResourcesConstant.TYPE_ENTRY_EFFECT,
            BaseDataResourcesConstant.TYPE_HONOR_TITLE,
            BaseDataResourcesConstant.TYPE_RECHARGE_COUPON,
            BaseDataResourcesConstant.TYPE_TICKET
    );


    private static final Map<Integer, String> CATEGORY_DESC_MAP = new HashMap<Integer, String>() {
        {
            put(0, "Carnival of Star-primary");
            put(1, "Carnival of Star-middle");
            put(2, "Carnival of Star-senior");
        }
    };

    private static final Map<Integer, String> CATEGORY_PLAY_DESC_MAP = new HashMap<Integer, String>() {
        {
            put(0, "Play Carnival of Star-primary");
            put(1, "Play Carnival of Star-middle");
            put(2, "Play Carnival of Star-senior");
        }
    };

    private static final Map<Integer, String> CATEGORY_DESC_NEW_MAP = new HashMap<Integer, String>() {
        {
            put(101, "Carnival of Star-primary-A");
            put(102, "Carnival of Star-primary-B");
            put(103, "Carnival of Star-primary-C");
            put(201, "Carnival of Star-middle-A");
            put(202, "Carnival of Star-middle-B");
            put(203, "Carnival of Star-middle-C");
            put(301, "Carnival of Star-senior-A");
            put(302, "Carnival of Star-senior-B");
            put(303, "Carnival of Star-senior-C");
        }
    };

    private static final Map<Integer, String> CATEGORY_PLAY_DESC_NEW_MAP = new HashMap<Integer, String>() {
        {
            put(101, "Play Carnival of Star-primary-A");
            put(102, "Play Carnival of Star-primary-B");
            put(103, "Play Carnival of Star-primary-C");
            put(201, "Play Carnival of Star-middle-A");
            put(202, "Play Carnival of Star-middle-B");
            put(203, "Play Carnival of Star-middle-C");
            put(301, "Play Carnival of Star-senior-A");
            put(302, "Play Carnival of Star-senior-B");
            put(303, "Play Carnival of Star-senior-C");
        }
    };


    private Interner<String> stringPool = Interners.newWeakInterner();

    @Resource
    private DataCenterService dataCenterService;
    @Resource
    private StarBeatPoolRedis starBeatPoolRedis;
    @Resource
    private CarnivalGameDao carnivalGameDao;
    @Resource(name = AsyncConfig.ASYNC_TASK)
    private Executor executor;
    @Autowired
    private ActorDao actorDao;
    @Resource
    private DataResourcesService dataResourcesService;
    @Resource
    private MqSenderService mqSenderService;
    @Resource
    private StarBeatGameRedis starBeatGameRedis;
    @Resource
    private RoomPlayerRedis roomPlayerRedis;
    @Resource
    private RoomWebSender roomWebSender;
    @Resource
    private UserResourceDao userResourceDao;
    @Resource
    private ResourceConfigDao resourceConfigDao;
    @Resource
    private EventReport eventReport;
    @Resource
    private CommonTaskService commonTaskService;
    @Resource
    private RechargeDailyInfoDao rechargeDailyInfoDao;

    public StarBeatGameVO info(StarBeatGameDTO dto) {
        StarBeatGameVO vo = new StarBeatGameVO();
        String uid = dto.getUid();
        int slang = dto.getSlang();
        int category = dto.getCategory();
        if (category < 0) {
            logger.error("num or category type error. uid={} category={}", uid, category);
            throw new H5GameException(GameHttpCode.PARAM_ERROR);
        }
        CarnivalGameData carnivalGameData = carnivalGameDao.getCarnivalGameData(category);
        if (!carnivalGameDao.checkCarnivalGameData(carnivalGameData)) {
            logger.error(" CarnivalGameData is invalid error. uid={} category={}", uid, category);
            throw new H5GameException(GameHttpCode.PARAM_ERROR);
        }

        ActorData actorData = actorDao.getActorData(uid);
        if (actorData == null) {
            logger.error("user not exist. uid={} ", uid);
            throw new H5GameException(GameHttpCode.USER_NOT_EXISTING);
        }

        List<StarBeatGameVO.PrizeConfigVO> prizeConfigList = new ArrayList<>();
        StarBeatResultData resultData = new StarBeatResultData();
        fillSourceMapOnlyBox(resultData, carnivalGameData);
        Map<Integer, List<CarnivalGameData.PrizeConfig>> allBoxListMap = resultData.getAllBoxListMap();
        carnivalGameData.getPrizeConfigList().forEach(data -> {
            if (data.getPrizeStatus() == 1 && data.getParentId() == 0) {
                StarBeatGameVO.PrizeConfigVO one = new StarBeatGameVO.PrizeConfigVO();
                BeanUtils.copyProperties(data, one);
                if (data.getResourceType() == RESOURCE_BOX_TYPE) {
                    List<CarnivalGameData.PrizeConfig> boxList = allBoxListMap.get(data.getPrizeId());
                    List<StarBeatGameVO.PrizeConfigVO> boxPrizeConfigList = new ArrayList<>();
                    for (CarnivalGameData.PrizeConfig subConfig : boxList) {
                        if (subConfig.getPrizeStatus() == 1) {
                            StarBeatGameVO.PrizeConfigVO subOne = new StarBeatGameVO.PrizeConfigVO();
                            BeanUtils.copyProperties(subConfig, subOne);
                            boxPrizeConfigList.add(subOne);
                        }
                    }
                    one.setPrizeConfigBoxList(boxPrizeConfigList);
                    boxPrizeConfigList.sort(SB_ORDER_ASC);
                }
                prizeConfigList.add(one);
            }
        });
        prizeConfigList.sort(SB_ORDER_ASC);
        carnivalGameData.getPrizeFreeList().sort(BT_ORDER_ASC);

        if (carnivalGameData.getTicketId() > 0) {
            vo.setIsShowTicket(carnivalGameData.getTicketId());
            int myTicket = userResourceDao.selectTotalByUidEndTime(uid, BaseDataResourcesConstant.TYPE_TICKET,
                    carnivalGameData.getTicketId(), DateHelper.getNowSeconds());
            vo.setCurrencyTicket(myTicket);
            ResourceConfigData resourceData = resourceConfigDao.getResourceDataFromCache(carnivalGameData.getTicketId(), BaseDataResourcesConstant.TYPE_TICKET);
            if (resourceData != null) {
                vo.setTicketUrl(slang == SLangType.ENGLISH ? resourceData.getIcon() : resourceData.getIconAr());
            }
        } else {
            vo.setIsShowTicket(0);
            vo.setCurrencyTicket(0);
        }
        if (carnivalGameData.getPoolLuckyNum() > 0) {
            vo.setMaxLuckyStar(carnivalGameData.getPoolLuckyNum());
            vo.setCurrencyLuckyStar(starBeatGameRedis.getLuckyStar(uid, category));
        } else {
            vo.setMaxLuckyStar(0);
            vo.setCurrencyLuckyStar(0);
        }

        vo.setHead(ImageUrlGenerator.generateRoomUserUrl(actorData.getHead()));
        vo.setPrizeConfigList(prizeConfigList);
        vo.setPrizeFreeList(carnivalGameData.getPrizeFreeList());
        Map<Integer, CarnivalGameData.PrizeConfig> allPrizeConfig = carnivalGameDao.getAllPrizeConfig();
        fillRollVOList(vo, allPrizeConfig);
        return vo;
    }

    public StarBeatGameVO allBox(StarBeatGameDTO dto) {
        StarBeatGameVO vo = new StarBeatGameVO();
        List<CarnivalGameData> carnivalGameDataList = carnivalGameDao.getAllCarnivalGameDataCache();
        Map<Integer, List<CarnivalGameData.PrizeConfig>> allBoxListMap = new HashMap<>();
        List<StarBeatGameVO.PrizeConfigVO> prizeConfigList = new ArrayList<>();
        for (CarnivalGameData item : carnivalGameDataList) {
            item.getPrizeConfigList().forEach(data -> {
                if (data.getParentId() != 0) {
                    // 礼盒子奖励
                    int pid = data.getParentId();
                    List<CarnivalGameData.PrizeConfig> templateList = allBoxListMap.computeIfAbsent(pid, k -> new ArrayList<>());
                    templateList.add(data);
                }
            });
        }

        for (CarnivalGameData item : carnivalGameDataList) {
            item.getPrizeConfigList().forEach(data -> {
                if (data.getPrizeStatus() == 1 && data.getParentId() == 0 && data.getResourceType() == RESOURCE_BOX_TYPE) {
                    StarBeatGameVO.PrizeConfigVO one = new StarBeatGameVO.PrizeConfigVO();
                    BeanUtils.copyProperties(data, one);
                    List<CarnivalGameData.PrizeConfig> boxList = allBoxListMap.get(data.getPrizeId());
                    List<StarBeatGameVO.PrizeConfigVO> boxPrizeConfigList = new ArrayList<>();
                    for (CarnivalGameData.PrizeConfig subConfig : boxList) {
                        if (subConfig.getPrizeStatus() == 1) {
                            StarBeatGameVO.PrizeConfigVO subOne = new StarBeatGameVO.PrizeConfigVO();
                            BeanUtils.copyProperties(subConfig, subOne);
                            boxPrizeConfigList.add(subOne);
                        }
                    }
                    one.setPrizeConfigBoxList(boxPrizeConfigList);
                    boxPrizeConfigList.sort(SB_ORDER_ASC);
                    prizeConfigList.add(one);
                }
            });
            prizeConfigList.sort(SB_ORDER_ASC);
        }
        vo.setPrizeConfigList(prizeConfigList);
        return vo;
    }

    public StarBeatGameVO draw(StarBeatGameDTO dto) {
        StarBeatGameVO vo = new StarBeatGameVO();
        String uid = dto.getUid();
        int category = dto.getCategory();
        int drawType = dto.getDrawType();
        int num = dto.getNum();
        if (num <= 0 || category < 0) {
            logger.error("num or category type error. uid={} num={} category={}", uid, num, category);
            throw new H5GameException(GameHttpCode.PARAM_ERROR);
        }
        CarnivalGameData carnivalGameData = carnivalGameDao.getCarnivalGameData(category);
        if (!carnivalGameDao.checkCarnivalGameData(carnivalGameData)) {
            logger.error(" CarnivalGameData is invalid error. uid={} category={}", uid, category);
            throw new H5GameException(GameHttpCode.FAILURE);
        }
        ActorData actorData = actorDao.getActorData(uid);
        if (actorData == null) {
            logger.error("user not exist. uid={} ", uid);
            throw new H5GameException(GameHttpCode.USER_NOT_EXISTING);
        }
        List<CarnivalGameData.PrizeFree> prizeFreeList = carnivalGameData.getPrizeFreeList();
        Map<Integer, Integer> prizeFreeMap = prizeFreeList.stream().collect(Collectors.toMap(CarnivalGameData.PrizeFree::getPrizeTimes, CarnivalGameData.PrizeFree::getPrizePrice));
        int baseFeeBeans = prizeFreeMap.getOrDefault(num, 0);

        prizeFreeList.sort(BT_ORDER_ASC);
        int joinBeans = prizeFreeList.get(0).getPrizePrice();
        if (joinBeans <= 0) {
            logger.error(" CarnivalGameData is invalid error. uid={} category={} joinBeans={}", uid, category, joinBeans);
            throw new H5GameException(GameHttpCode.FAILURE);
        }
        float expand = carnivalGameData.getExpand();
        float expandJoinBeans = expand > 0 ? expand * joinBeans : joinBeans;
        if (expandJoinBeans <= 0) {
            logger.error(" CarnivalGameData is invalid error. uid={} category={} expandJoinBeans={}", uid, category, expandJoinBeans);
            throw new H5GameException(GameHttpCode.FAILURE);
        }
        int useTicket = 0;
        int leftTicket = 0;
        int userType = getUserType(uid);
        dto.setUserType(userType);
        synchronized (stringPool.intern(getLockKeyByUid(uid))) {
            int ticketEndTime = DateHelper.getNowSeconds();
            if (drawType == 1 && carnivalGameData.getTicketId() > 0) {
                // 检查券
                int myTicket = userResourceDao.selectTotalByUidEndTime(uid, BaseDataResourcesConstant.TYPE_TICKET,
                        carnivalGameData.getTicketId(), ticketEndTime);
                useTicket = Math.min(myTicket, num);
//                leftTicket = myTicket - useTicket;
//                leftTicket = Math.max(leftTicket, 0);
            }
            int useBeansNum = Math.max(num - useTicket, 0);
            int feeBeans;
            if (baseFeeBeans > 0) {
                feeBeans = baseFeeBeans - useTicket * joinBeans;
            } else {
                feeBeans = useBeansNum * joinBeans;
            }

            int beans = actorData.getBeans();
            if (beans < feeBeans) {
                logger.info("not enough diamonds. uid={} beans={} feeBeans={} useTicket={} joinBeans={} baseFeeBeans={} useBeansNum={}"
                        , uid, beans, feeBeans, useTicket, joinBeans, baseFeeBeans, useBeansNum);
                throw new H5GameException(GameHttpCode.NOT_ENOUGH_DIAMOND);
            }

            StarBeatResultData resultData = synDraw(uid, expandJoinBeans, category, num, carnivalGameData, userType);
            List<StarBeatResultData.AwardBean> awardBeanList = resultData.getAwardBeanList();
            Map<Integer, CarnivalGameData.PrizeConfig> allPrizeConfig = resultData.getAllPrizeConfig();
            Map<Integer, List<CarnivalGameData.PrizeConfig>> allBoxListMap = resultData.getAllBoxListMap();

            Map<Integer, StarBeatGameVO.PrizeConfigVO> prizeConfigVOMap = new HashMap<>();
            if (!CollectionUtils.isEmpty(awardBeanList)) {
                for (StarBeatResultData.AwardBean item : awardBeanList) {
                    int awardId = item.getAwardId();
                    int awardType = item.getAwardType();
                    CarnivalGameData.PrizeConfig config = allPrizeConfig.get(awardId);
                    if (config.getResourceType() == RESOURCE_BOX_TYPE) {
                        // 中了礼盒
                        List<CarnivalGameData.PrizeConfig> boxList = allBoxListMap.get(config.getPrizeId());
                        for (CarnivalGameData.PrizeConfig subConfig : boxList) {
                            if (subConfig.getPrizeStatus() == 1) {
                                fillPrizeConfigMap(prizeConfigVOMap, subConfig, awardType);
                            }
                        }
                    } else {
                        fillPrizeConfigMap(prizeConfigVOMap, config, awardType);
                    }
                }

                if (useTicket > 0) {
                    // 同步扣券
                    leftTicket = userResourceDao.reduceResourceChange(uid, BaseDataResourcesConstant.TYPE_TICKET,
                            carnivalGameData.getTicketId(), useTicket, ticketEndTime);
                    if (leftTicket < 0) {
                        logger.info("change ticket fail useTicket={} ticketId={} uid={} ",
                                useTicket, carnivalGameData.getTicketId(), uid);
                        throw new H5GameException(GameHttpCode.NOT_MONEY_OR_NUMBER);
                    }
                }
                if (feeBeans > 0) {
                    String playTitle = CATEGORY_PLAY_DESC_NEW_MAP.getOrDefault
                            (getEventUserType(category, userType), "Play Carnival of Star-unknown");
                    MoneyDetailReq moneyDetailReq = new MoneyDetailReq();
                    moneyDetailReq.setRandomId();
                    moneyDetailReq.setUid(uid);
                    moneyDetailReq.setAtype(PLAY_DIAMOND_TYPE);
                    moneyDetailReq.setChanged(-feeBeans);
                    moneyDetailReq.setTitle(playTitle);
                    moneyDetailReq.setDesc(playTitle);
                    ApiResult<String> reduceBeansResult = dataCenterService.reduceBeans(moneyDetailReq);
                    if (!reduceBeansResult.isOk()) {
                        logger.info("change beans fail cost={} uid={} ", feeBeans, uid);
                        throw new H5GameException(GameHttpCode.NOT_ENOUGH_DIAMOND);
                    }
                }

                List<StarBeatGameVO.PrizeConfigVO> prizeConfigVOList = new ArrayList<>(prizeConfigVOMap.values());
                vo.setPrizeConfigList(prizeConfigVOList);
                if (carnivalGameData.getTicketId() > 0) {
                    vo.setIsShowTicket(carnivalGameData.getTicketId());
                    vo.setCurrencyTicket(leftTicket + resultData.getAddTicket());
                } else {
                    vo.setIsShowTicket(0);
                    vo.setCurrencyTicket(0);
                }
                if (carnivalGameData.getPoolLuckyNum() > 0) {
                    vo.setMaxLuckyStar(carnivalGameData.getPoolLuckyNum());
                    vo.setCurrencyLuckyStar(resultData.getNowStar());
                } else {
                    vo.setMaxLuckyStar(0);
                    vo.setCurrencyLuckyStar(0);
                }
                if (resultData.getMaxPrizeConfig() != null) {
                    vo.setMaxValuePrizeId(resultData.getMaxPrizeConfig().getPrizeId());
                }
                asyncPushAndRecord(dto, allPrizeConfig, awardBeanList, prizeConfigVOList
                        , prizeConfigVOMap, feeBeans, useTicket, carnivalGameData.getTicketId());

                logger.info("success draw userType:{} useTicket:{} leftTicket:{} feeBeans:{} joinBeans:{} num:{}" +
                                " awardBeanList:{} maxValuePrizeId:{}",
                        userType, useTicket, vo.getCurrencyTicket(), -feeBeans, joinBeans, num,
                        awardBeanList, vo.getMaxValuePrizeId());
            }
        }
        return vo;
    }


    public StarBeatGameVO rank(StarBeatGameDTO dto) {
        String uid = dto.getUid();
        ActorData actorData = actorDao.getActorDataFromCache(dto.getUid());
        if (actorData == null) {
            logger.error("user not exist. uid={} ", dto.getUid());
            throw new H5GameException(GameHttpCode.USER_NOT_EXISTING);
        }
        StarBeatGameVO vo = new StarBeatGameVO();
        String weekStart;
        if (dto.getRankType() == 0) {
            weekStart = DateHelper.ARABIAN.getWeekStartDate();
            vo.setRankEndTime(DateHelper.ARABIAN.getWeekEndDateTime());
        } else {
            weekStart = DateHelper.ARABIAN.getPreWeekStartDate();
        }
        logger.info("weekStart:{}", weekStart);
        List<StarBeatGameVO.UserVO> rankingList = new ArrayList<>();
        StarBeatGameVO.UserVO myRank = new StarBeatGameVO.UserVO();
        Map<String, Integer> brainRankingMap = starBeatGameRedis.getCommonRankingMap(weekStart, 30);
        int rank = 1;
        for (Map.Entry<String, Integer> entry : brainRankingMap.entrySet()) {
            StarBeatGameVO.UserVO rankingVO = new StarBeatGameVO.UserVO();
            String aid = entry.getKey();
            ActorData rankActor = actorDao.getActorDataFromCache(aid);
            rankingVO.setHead(ImageUrlGenerator.generateRoomUserUrl(rankActor.getHead()));
            rankingVO.setName(rankActor.getName());
            rankingVO.setUid(aid);
            rankingVO.setScore(entry.getValue());
            rankingVO.setRank(rank);
            if (aid.equals(uid)) {
                BeanUtils.copyProperties(rankingVO, myRank);
            }
            rankingList.add(rankingVO);
            rank += 1;
        }

        if (myRank.getRank() == null || myRank.getRank() == 0) {
            myRank.setName(actorData.getName());
            myRank.setUid(uid);
            myRank.setHead(ImageUrlGenerator.generateRoomUserUrl(actorData.getHead()));
            myRank.setScore(starBeatGameRedis.getCommonZSetRankingScore(weekStart, uid));
            myRank.setRank(-1);
        }
        vo.setRankList(rankingList);
        vo.setMyRank(myRank);
        return vo;
    }


    public StarBeatGameVO history(StarBeatGameDTO dto) {
        String uid = dto.getUid();
        ActorData actorData = actorDao.getActorDataFromCache(uid);
        if (actorData == null) {
            logger.error("user not exist. uid={} ", dto.getUid());
            throw new H5GameException(GameHttpCode.USER_NOT_EXISTING);
        }
        Map<Integer, CarnivalGameData.PrizeConfig> allPrizeConfig = carnivalGameDao.getAllPrizeConfig();
        int page = dto.getPage() > 0 ? dto.getPage() : 1;
        int start = (page - 1) * HISTORY_PAGE_SIZE;
        int end = page * HISTORY_PAGE_SIZE;
        int slang = dto.getSlang();
        StarBeatGameVO vo = new StarBeatGameVO();
        List<StarBeatGameVO.UserVO> historyVOList = new ArrayList<>();
        List<StarBeatRedisData> historyList = starBeatGameRedis.getHistoryListPageRecord(uid, start, end);
        for (StarBeatRedisData item : historyList) {
            StarBeatGameVO.UserVO userVO = new StarBeatGameVO.UserVO();
            CarnivalGameData.PrizeConfig prizeConfig = allPrizeConfig.get(item.getPrizeId());
            userVO.setPrizeId(item.getPrizeId());
            userVO.setPrizeNameEn(prizeConfig.getResourceNameEn());
            userVO.setPrizeNameAr(prizeConfig.getResourceNameAr());
            userVO.setPrizeIcon(prizeConfig.getResourceIcon());
            userVO.setPrizePrice(prizeConfig.getPrizeValue());
            userVO.setCtime(item.getCtime());
            if (item.getDrawId() != null) {
                userVO.setDrawId(item.getDrawId());
                userVO.setUseTicket(item.getUseTicket());
                userVO.setUseBeans(item.getUseBeans());
                if (item.getUseTicket() > 0 && item.getTicketId() != null) {
                    ResourceConfigData resourceData = resourceConfigDao.getResourceDataFromCache(item.getTicketId(), BaseDataResourcesConstant.TYPE_TICKET);
                    if (resourceData != null) {
                        userVO.setTicketUrl(slang == SLangType.ENGLISH ? resourceData.getIcon() : resourceData.getIconAr());
                    }
                }
            }
            historyVOList.add(userVO);
        }
        vo.setHistoryList(historyVOList);
        return vo;
    }


    private StarBeatResultData synDraw(String uid, float joinBeans, int type, int num,
                                       CarnivalGameData carnivalGameData, int userType) {
        StarBeatResultData resultData = new StarBeatResultData();
        fillSourceMap(resultData, carnivalGameData, userType);
        synchronized (stringPool.intern(getLockKeyByType(type))) {
            StarBeatPoolRewardData poolRewardData = starBeatPoolRedis.getPoolReward(type);
            if (poolRewardData == null) {
                poolRewardData = new StarBeatPoolRewardData();
            }
            resultData.setStarBeatPoolRewardData(poolRewardData);
            if (carnivalGameData.getPoolLuckyNum() > 0) {
                resultData.setNowStar(starBeatGameRedis.getLuckyStar(uid, type));
            }
            resultData.setAwardBeanList(new ArrayList<>());
            for (int i = 0; i < num; i++) {
                drawOne(joinBeans, resultData, carnivalGameData, userType);
            }
            resultData.getStarBeatPoolRewardData().setmTime(DateHelper.getNowSeconds());
            starBeatPoolRedis.savePoolReward(resultData.getStarBeatPoolRewardData(), type);
            if (carnivalGameData.getPoolLuckyNum() > 0) {
                starBeatGameRedis.setLuckyStar(uid, type, resultData.getNowStar());
            }
        }
        return resultData;
    }

    private void drawOne(float joinBeans, StarBeatResultData resultData, CarnivalGameData carnivalGameData, int userType) {
        List<StarBeatResultData.AwardBean> awardBeanList = resultData.getAwardBeanList();
        StarBeatPoolRewardData poolRewardData = resultData.getStarBeatPoolRewardData();
        boolean isOpenLucky = carnivalGameData.getPoolLuckyNum() > 0;
        float oldGameAwardNum = poolRewardData.getRewardNum();
        float oldLuckyAwardNum = poolRewardData.getRewardLuckyNum();
        float awardGameRote = (float) carnivalGameData.getGameAllocation() / 100;
        float awardLuckyRote = (float) carnivalGameData.getLuckyAllocation() / 100;
        float nowGamePoolNumAdd = joinBeans * awardGameRote;
        float nowLuckyPoolNumAdd = joinBeans * awardLuckyRote;
        CarnivalGameData.PrizeConfig maxPrizeConfig = resultData.getMaxPrizeConfig();

        if (isOpenLucky && resultData.getNowStar() >= carnivalGameData.getPoolLuckyNum()) {
            Map<Integer, Integer> luckyNumMap = initLuckyNumMap(poolRewardData, carnivalGameData);
            Integer awardId = getAwardIdByProbability(luckyNumMap);
            awardBeanList.add(new StarBeatResultData.AwardBean(awardId, AWARD_LUCKY_TYPE));
            luckyNumMap.put(awardId, luckyNumMap.get(awardId) - 1);
            resultData.setNowStar(resultData.getNowStar() - carnivalGameData.getPoolLuckyNum());

//            float awardLuckyPoolNum = oldLuckyAwardNum + nowLuckyPoolNumAdd;
            float awardLuckyPoolNum = Float.parseFloat(ArithmeticUtils.add(String.valueOf(oldLuckyAwardNum)
                    , String.valueOf(nowLuckyPoolNumAdd), 2));

            CarnivalGameData.PrizeConfig awardPrizeConfig = resultData.getAllPrizeConfig().get(awardId);
            if (awardPrizeConfig.getResourceType() == BaseDataResourcesConstant.TYPE_TICKET &&
                    carnivalGameData.getTicketId() == awardPrizeConfig.getResourceId()) {
                resultData.setAddTicket(resultData.getAddTicket() + awardPrizeConfig.getResourceNum());
            }
            int reduceValue = getReduceValue(awardPrizeConfig);
            resultData.setMaxPrizeConfig(selectMaxPrizeConfig(maxPrizeConfig, awardPrizeConfig));

            poolRewardData.setRewardLuckyNum(Float.parseFloat(ArithmeticUtils.sub(String.valueOf(awardLuckyPoolNum)
                    , String.valueOf(reduceValue), 2)));
            poolRewardData.setRewardNum(Float.parseFloat(ArithmeticUtils.add(String.valueOf(oldGameAwardNum)
                    , String.valueOf(nowGamePoolNumAdd), 2)));
//            poolRewardData.setRewardLuckyNum(awardLuckyPoolNum-reduceValue);
//            poolRewardData.setRewardNum(oldGameAwardNum + nowGamePoolNumAdd);


            logger.info("lucky hit userType:{} joinBeans:{} awardId:{} nowRewardLuckyNum:{} nowStar:{} afterLuckyNumMap:{} " +
                            "awardBeanList.size:{} nowRewardNum:{}",
                    userType, joinBeans, awardId, poolRewardData.getRewardLuckyNum(), resultData.getNowStar(), luckyNumMap,
                    awardBeanList.size(), poolRewardData.getRewardNum());
        } else {
            int dealStatus = 0; // 0按概率中出 1 放水 2 回收

            int aNum = carnivalGameData.getPoolMax();
            int bNum = carnivalGameData.getPoolMin();
            int cRatio = carnivalGameData.getPoolMaxRate();
            int dRatio = carnivalGameData.getPoolMinRate();

            float fcRatio = cRatio >= 100 || cRatio <= 0 ? 1 : (float) cRatio / 100;
            float fdRatio = dRatio >= 100 || dRatio <= 0 ? 1 : (float) dRatio / 100;

//            float awardPoolNum = oldGameAwardNum + nowGamePoolNumAdd;
            float awardPoolNum = Float.parseFloat(ArithmeticUtils.add(String.valueOf(oldGameAwardNum)
                    , String.valueOf(nowGamePoolNumAdd), 2));
            float dealRatio = 1;
            if (oldGameAwardNum >= aNum) {
                dealStatus = 1;
                dealRatio = fcRatio;
            } else if (oldGameAwardNum <= bNum) {
                dealStatus = 2;
                dealRatio = fdRatio;
            }

//            else if (0 < awardPoolNum && awardPoolNum < aNum) {
//                dealStatus = 1;
//                dealRatio = (float) oldGameAwardNum / aNum * fcRatio; //  oldGameAwardNum可能为负值
//            } else if (bNum <= awardPoolNum && awardPoolNum < 0) {
//                dealStatus = 2;
//                dealRatio = (float) oldGameAwardNum / bNum * fdRatio;
//            }
            if (dealRatio != 1) {
                int dealRatioInt = (int) (dealRatio * 10000);
                int random = ThreadLocalRandom.current().nextInt(1, 10001);//1-10000
                if (random > dealRatioInt) {
                    // 概率不中，走随机
                    dealStatus = 0;
                }
            }

            Map<Integer, Integer> sourceMap = null;
            if (dealStatus == 1) {
                sourceMap = resultData.getHighSourceMap();
            } else if (dealStatus == 2) {
                sourceMap = resultData.getLowSourceMap();
            } else {
                sourceMap = resultData.getAllSourceMap();
            }
            Integer awardId = getAwardIdByProbability(sourceMap);
            awardBeanList.add(new StarBeatResultData.AwardBean(awardId, AWARD_NORMAL_TYPE));
            CarnivalGameData.PrizeConfig awardPrizeConfig = resultData.getAllPrizeConfig().get(awardId);
            if (awardPrizeConfig.getResourceType() == BaseDataResourcesConstant.TYPE_TICKET &&
                    carnivalGameData.getTicketId() == awardPrizeConfig.getResourceId()) {
                resultData.setAddTicket(resultData.getAddTicket() + awardPrizeConfig.getResourceNum());
            }
            int reduceValue = getReduceValue(awardPrizeConfig);
            resultData.setMaxPrizeConfig(selectMaxPrizeConfig(maxPrizeConfig, awardPrizeConfig));


//            poolRewardData.setRewardNum(awardPoolNum - reduceValue);
//            poolRewardData.setRewardLuckyNum(oldLuckyAwardNum + nowLuckyPoolNumAdd);

            poolRewardData.setRewardNum(Float.parseFloat(ArithmeticUtils.sub(String.valueOf(awardPoolNum)
                    , String.valueOf(reduceValue), 2)));
            poolRewardData.setRewardLuckyNum(Float.parseFloat(ArithmeticUtils.add(String.valueOf(oldLuckyAwardNum)
                    , String.valueOf(nowLuckyPoolNumAdd), 2)));
            if (isOpenLucky) {
                resultData.setNowStar(resultData.getNowStar() + 1);
            }
            logger.info("normal hit userType:{} joinBeans:{} awardId:{} nowRewardLuckyNum:{} nowStar:{} isOpenLucky:{}" +
                            " dealStatus:{} sourceMap:{} aNum:{} bNum:{} fcRatio:{} fdRatio:{}" +
                            " oldGameAwardNum:{} awardPoolNum:{} reduceValue:{} awardBeanList.size:{} nowRewardNum:{}",
                    userType, joinBeans, awardId, poolRewardData.getRewardLuckyNum(), resultData.getNowStar(), isOpenLucky,
                    dealStatus, sourceMap, aNum, bNum, fcRatio, fdRatio,
                    oldGameAwardNum, awardPoolNum, reduceValue, awardBeanList.size(), poolRewardData.getRewardNum());
        }

    }

    private void fillSourceMap(StarBeatResultData resultData, CarnivalGameData carnivalGameData, int userType) {
        Map<Integer, Integer> allSourceMap = new HashMap<>();
        Map<Integer, Integer> lowSourceMap = new HashMap<>();
        Map<Integer, Integer> highSourceMap = new HashMap<>();

//        Map<Integer, Integer> allSourceMapA = new HashMap<>();
//        Map<Integer, Integer> lowSourceMapA = new HashMap<>();
//        Map<Integer, Integer> highSourceMapA = new HashMap<>();
//
//        Map<Integer, Integer> allSourceMapB = new HashMap<>();
//        Map<Integer, Integer> lowSourceMapB = new HashMap<>();
//        Map<Integer, Integer> highSourceMapB = new HashMap<>();
//
//        Map<Integer, Integer> allSourceMapC = new HashMap<>();
//        Map<Integer, Integer> lowSourceMapC = new HashMap<>();
//        Map<Integer, Integer> highSourceMapC = new HashMap<>();

        Map<Integer, CarnivalGameData.PrizeConfig> allPrizeConfig = new HashMap<>();
        Map<Integer, List<CarnivalGameData.PrizeConfig>> allBoxListMap = new HashMap<>();
        carnivalGameData.getPrizeConfigList().forEach(data -> {
            if (data.getParentId() == 0) {
                if (data.getPrizeStatus() == 1) {
                    if (data.getValueType() == 0) {
                        if (userType == 1) {
                            lowSourceMap.put(data.getPrizeId(), (int) (data.getRateA() * 1000));
                        } else if (userType == 2) {
                            lowSourceMap.put(data.getPrizeId(), (int) (data.getRateB() * 1000));
                        } else if (userType == 3) {
                            lowSourceMap.put(data.getPrizeId(), (int) (data.getRateC() * 1000));
                        } else {
                            lowSourceMap.put(data.getPrizeId(), (int) (data.getRate() * 1000));
                        }
//                        lowSourceMapA.put(data.getPrizeId(), (int) (data.getRateA() * 1000));
//                        lowSourceMapB.put(data.getPrizeId(), (int) (data.getRateB() * 1000));
//                        lowSourceMapC.put(data.getPrizeId(), (int) (data.getRateC() * 1000));
                    } else {
                        if (userType == 1) {
                            highSourceMap.put(data.getPrizeId(), (int) (data.getRateA() * 1000));
                        } else if (userType == 2) {
                            highSourceMap.put(data.getPrizeId(), (int) (data.getRateB() * 1000));
                        } else if (userType == 3) {
                            highSourceMap.put(data.getPrizeId(), (int) (data.getRateC() * 1000));
                        } else {
                            highSourceMap.put(data.getPrizeId(), (int) (data.getRate() * 1000));
                        }
//                        highSourceMapA.put(data.getPrizeId(), (int) (data.getRateA() * 1000));
//                        highSourceMapB.put(data.getPrizeId(), (int) (data.getRateB() * 1000));
//                        highSourceMapC.put(data.getPrizeId(), (int) (data.getRateC() * 1000));
                    }
                }
            } else {
                // 礼盒子奖励
                int pid = data.getParentId();
                List<CarnivalGameData.PrizeConfig> templateList = allBoxListMap.computeIfAbsent(pid, k -> new ArrayList<>());
                templateList.add(data);
            }
            allPrizeConfig.put(data.getPrizeId(), data);
        });
        allSourceMap.putAll(lowSourceMap);
        allSourceMap.putAll(highSourceMap);
//        allSourceMapA.putAll(lowSourceMapA);
//        allSourceMapA.putAll(highSourceMapA);
//        allSourceMapB.putAll(lowSourceMapB);
//        allSourceMapB.putAll(highSourceMapB);
//        allSourceMapC.putAll(lowSourceMapC);
//        allSourceMapC.putAll(highSourceMapC);

        resultData.setLowSourceMap(lowSourceMap);
        resultData.setHighSourceMap(highSourceMap);
        resultData.setAllSourceMap(allSourceMap);

//        resultData.setLowSourceMapA(lowSourceMapA);
//        resultData.setHighSourceMapA(highSourceMapA);
//        resultData.setAllSourceMapA(allSourceMapA);
//        resultData.setLowSourceMapB(lowSourceMapB);
//        resultData.setHighSourceMapB(highSourceMapB);
//        resultData.setAllSourceMapB(allSourceMapB);
//        resultData.setLowSourceMapC(lowSourceMapC);
//        resultData.setHighSourceMapC(highSourceMapC);
//        resultData.setAllSourceMapC(allSourceMapC);

        resultData.setAllPrizeConfig(allPrizeConfig);
        resultData.setAllBoxListMap(allBoxListMap);
    }

    private void fillSourceMapOnlyBox(StarBeatResultData resultData, CarnivalGameData carnivalGameData) {

        Map<Integer, CarnivalGameData.PrizeConfig> allPrizeConfig = new HashMap<>();
        Map<Integer, List<CarnivalGameData.PrizeConfig>> allBoxListMap = new HashMap<>();
        carnivalGameData.getPrizeConfigList().forEach(data -> {
            if (data.getParentId() != 0) {
                // 礼盒子奖励
                int pid = data.getParentId();
                List<CarnivalGameData.PrizeConfig> templateList = allBoxListMap.computeIfAbsent(pid, k -> new ArrayList<>());
                templateList.add(data);
            }
            allPrizeConfig.put(data.getPrizeId(), data);
        });
        resultData.setAllPrizeConfig(allPrizeConfig);
        resultData.setAllBoxListMap(allBoxListMap);
    }

    private Map<Integer, Integer> initLuckyNumMap(StarBeatPoolRewardData poolRewardData, CarnivalGameData carnivalGameData) {
        int totalRemain = 0;
        if (CollectionUtils.isEmpty(poolRewardData.getLuckyNumMap()) ||
                (totalRemain = poolRewardData.getLuckyNumMap().values().stream().mapToInt(Integer::intValue).sum()) <= 0) {
            Map<Integer, Integer> luckyNumMap = new HashMap<>();
            carnivalGameData.getPrizeConfigList().forEach(data -> {
                if (data.getParentId() == 0 && data.getPrizeStatus() == 1 && data.getLuckyPoolNum() > 0) {
                    luckyNumMap.put(data.getPrizeId(), data.getLuckyPoolNum());
                }
            });
            poolRewardData.setLuckyNumMap(luckyNumMap);
            logger.info("totalRemain:{} initLuckyNumMap:{}", totalRemain, luckyNumMap);
            return luckyNumMap;
        } else {
            Map<Integer, Integer> luckyNumMap = poolRewardData.getLuckyNumMap();
            return luckyNumMap;
        }

    }

    private void fillPrizeConfigMap(Map<Integer, StarBeatGameVO.PrizeConfigVO> prizeConfigMap, CarnivalGameData.PrizeConfig source, int awardType) {
        int awardId = source.getPrizeId();
        if (prizeConfigMap.containsKey(awardId)) {
            StarBeatGameVO.PrizeConfigVO old = prizeConfigMap.get(awardId);
            old.setAwardNum(old.getAwardNum() + 1);
            if (awardType == AWARD_LUCKY_TYPE) {
                old.setAwardType(awardType);
            }
        } else {
            StarBeatGameVO.PrizeConfigVO one = new StarBeatGameVO.PrizeConfigVO();
            BeanUtils.copyProperties(source, one);
            one.setAwardNum(1);
            one.setAwardType(awardType);
            prizeConfigMap.put(awardId, one);
        }
    }

    private void fillRollVOList(StarBeatGameVO vo, Map<Integer, CarnivalGameData.PrizeConfig> allPrizeMap) {
        List<StarBeatGameVO.UserVO> rollVOList = new ArrayList<>();
        List<StarBeatRedisData> rollList = starBeatGameRedis.getShowRollListRecord(30);
        for (StarBeatRedisData item : rollList) {
            String aid = item.getUid();
            Integer prizeId = item.getPrizeId();
            ActorData aidData = actorDao.getActorDataFromCache(aid);
            StarBeatGameVO.UserVO itemVO = new StarBeatGameVO.UserVO();
            itemVO.setName(aidData.getName());
            itemVO.setPrizeNameEn(allPrizeMap.get(prizeId).getResourceNameEn());
            itemVO.setPrizeNameAr(allPrizeMap.get(prizeId).getResourceNameAr());
            itemVO.setPrizePrice(allPrizeMap.get(prizeId).getPrizeValue());
            rollVOList.add(itemVO);
        }
        vo.setRollList(rollVOList);
    }


    public Integer getAwardIdByProbability(Map<Integer, Integer> sourceMap) {
        try {
            if (CollectionUtils.isEmpty(sourceMap)) {
                logger.info("getAwardIdByProbability sourceMap is empty");
                return null;
            }
            int total = 0;
            Map<Integer, Integer> mapRatio = new HashMap<>();
            List<Integer> ratioList = new ArrayList<>(); // 这样大于等于0，小于1000取第一个（key为1000） 、大于等于1000，小于2000取第二个 （key为2000）
            for (Map.Entry<Integer, Integer> entry : sourceMap.entrySet()) {
                int awardId = entry.getKey();
                int value = entry.getValue();
                if (value <= 0) {
                    continue;
                }
                total += value;
                mapRatio.put(total, awardId);
                ratioList.add(total);
            }
            if (total == 0) {
                logger.info("getAwardIdByProbability total is zero sourceMap={} ", sourceMap);
                return null;
            }
            int ratio = ThreadLocalRandom.current().nextInt(0, total);//0-(total-1)
            if (!ratioList.contains(ratio)) {
                ratioList.add(ratio);
                Collections.sort(ratioList);
            }

            int index = ratioList.indexOf(ratio);
            int destNum = ratioList.get(index + 1);
            int hitType = mapRatio.get(destNum);
//            logger.info("getAwardIdByProbability-->ratioList:{} mapRatio:{} total:{} ratio:{} index:{} destNum:{} hitType:{}"
//                    , ratioList, mapRatio, total, ratio, index, destNum, hitType);
            return hitType;
        } catch (Exception e) {
            logger.error("getAwardIdByProbability error sourceMap:{} msg:{}", sourceMap, e.getMessage(), e);
            return null;
        }
    }


    private void asyncPushAndRecord(StarBeatGameDTO dto, Map<Integer, CarnivalGameData.PrizeConfig> allPrizeConfig, List<StarBeatResultData.AwardBean> awardBeanList,
                                    List<StarBeatGameVO.PrizeConfigVO> prizeConfigVOList
            , Map<Integer, StarBeatGameVO.PrizeConfigVO> prizeConfigVOMap, int feeBeans, int useTicket, int ticketId) {
        executor.execute(() -> {
            String uid = dto.getUid();
            int total = 0;
            int maxScreenValue = 0;
            int maxRoomCommonValue = 0;
            CarnivalGameData.PrizeConfig maxScreenAwardPrizeConfig = null;
            CarnivalGameData.PrizeConfig maxRoomCommonAwardPrizeConfig = null;
            List<StarBeatRedisData> allAwardList = new ArrayList<>();
            int now = DateHelper.getNowSeconds();
            String drawId = new ObjectId().toString();
            for (StarBeatResultData.AwardBean item : awardBeanList) {
                int awardId = item.getAwardId();
                CarnivalGameData.PrizeConfig prizeConfig = allPrizeConfig.get(awardId);
                int pushType = prizeConfig.getPushType();
                if (pushType == 1) {
                    if (maxScreenValue < prizeConfig.getPrizeValue()) {
                        maxScreenValue = prizeConfig.getPrizeValue();
                        maxScreenAwardPrizeConfig = prizeConfig;
                    }
                } else if (pushType == 2) {
                    if (maxScreenValue < prizeConfig.getPrizeValue()) {
                        maxScreenValue = prizeConfig.getPrizeValue();
                        maxScreenAwardPrizeConfig = prizeConfig;
                    }
                    if (maxRoomCommonValue < prizeConfig.getPrizeValue()) {
                        maxRoomCommonValue = prizeConfig.getPrizeValue();
                        maxRoomCommonAwardPrizeConfig = prizeConfig;
                    }
                }
                StarBeatRedisData redisData = new StarBeatRedisData(prizeConfig.getPrizeId(), now);
                redisData.setDrawId(drawId);
                redisData.setUseTicket(useTicket);
                redisData.setUseBeans(feeBeans);
                redisData.setTicketId(ticketId);
                allAwardList.add(redisData);
                total += prizeConfig.getPrizeValue();
            }
            // 排行榜数据
            String weekStartStr = DateHelper.ARABIAN.getWeekStartDate();
            starBeatGameRedis.incrCommonZSetRankingScoreSimple(weekStartStr, uid, total);
            //历史记录-不拆礼盒
            starBeatGameRedis.leftPushAllHistoryList(uid, allAwardList);
            // 滚动消息-不拆礼盒
            if (maxScreenAwardPrizeConfig != null) {
                starBeatGameRedis.addRollListRecord(new StarBeatRedisData(uid, maxScreenAwardPrizeConfig.getPrizeId()));
            }
            // 下发奖励-礼盒拆开
            int category = dto.getCategory();
            for (StarBeatGameVO.PrizeConfigVO itemVO : prizeConfigVOList) {
                int count = itemVO.getAwardNum();
                if (itemVO.getResourceType() == BaseDataResourcesConstant.TYPE_BAG_GIFT) {
                    for (int i = 0; i < count; i++) {
                        handleResources(uid, category, dto.getUserType(), itemVO);

                        CommonMqTopicData.StarBeatGameInfo starBeatGameInfo = new CommonMqTopicData.StarBeatGameInfo();
                        starBeatGameInfo.setResourceType(itemVO.getResourceType());
                        starBeatGameInfo.setResourceId(itemVO.getResourceId());
                        starBeatGameInfo.setResourceNum(itemVO.getResourceNum());
                        starBeatGameInfo.setResourceTime(itemVO.getResourceTime());
                        CommonMqTopicData topicData = new CommonMqTopicData(uid, "", "", "", CommonMqTaskConstant.PLAY_CARNIVAL_REWARD, 0);
                        topicData.setJsonData(JSONObject.toJSONString(starBeatGameInfo));
                        commonTaskService.sendCommonTaskMq(topicData);
                    }
                } else {
                    handleResources(uid, category, dto.getUserType(), itemVO);
                }
            }
            //push-不拆礼盒
            pushBigReward(dto, maxScreenAwardPrizeConfig, maxRoomCommonAwardPrizeConfig);

            doReportEvent(dto, feeBeans, useTicket, prizeConfigVOMap);
            if (feeBeans > 0) {
                commonTaskService.sendCommonTaskMq(new CommonMqTopicData(dto.getUid(), "", "", "", CommonMqTaskConstant.PLAY_CARNIVAL_GAME, feeBeans));
            }
        });
    }

    private ResourcesDTO prizeConfigToResDTO(String uid, StarBeatGameVO.PrizeConfigVO vo, String title) {
        if (vo == null) {
            return null;
        } else {
            int resourceType = vo.getResourceType();
            int sourceId = vo.getResourceId();
            int day = vo.getResourceTime();
            int num = vo.getResourceNum();
            int count = (vo.getAwardNum() == null || vo.getAwardNum() <= 0) ? 1 : vo.getAwardNum();
            if (BaseDataResourcesConstant.TYPE_BAG_GIFT == resourceType) {
                if (num > 0) {
                    ResourcesDTO dto = new ResourcesDTO();
                    dto.setUid(uid);
                    dto.setResId(String.valueOf(sourceId));
                    dto.setResType(resourceType);
                    dto.setActionType(BaseDataResourcesConstant.ACTION_GET);
                    dto.setGetWay(4);
                    dto.setmTime(DateHelper.getNowSeconds());
                    dto.setGainType(BaseDataResourcesConstant.GAIN_TYPE_DAY);
                    dto.setDays(day > 0 ? day : -1);
                    dto.setNum(num);
                    dto.setDesc(title);
                    dto.setItemsSourceDetail(title);
                    return dto;
                } else {
                    return null;
                }
            } else if (BaseDataResourcesConstant.TYPE_TICKET == resourceType) {
                ResourcesDTO dto = new ResourcesDTO();
                dto.setUid(uid);
                dto.setResId(String.valueOf(sourceId));
                dto.setResType(resourceType);
                dto.setActionType(BaseDataResourcesConstant.ACTION_GET);
                dto.setmTime(DateHelper.getNowSeconds());
                dto.setGainType(BaseDataResourcesConstant.GAIN_TYPE_DAY);
                dto.setDays(day > 0 ? day : -1);
                dto.setNum(num != 0 ? num * count : 1);
                dto.setDesc(title);
                dto.setItemsSourceDetail(title);
                return dto;
            } else if (ALL_PRIZE_TYPE_LIST.contains(resourceType)) {
                ResourcesDTO dto = new ResourcesDTO();
                dto.setUid(uid);
                dto.setResId(String.valueOf(sourceId));
                if (resourceType == BaseDataResourcesConstant.TYPE_MINE_BACKGROUND) {
                    dto.setRoomId(RoomUtils.formatRoomId(uid));
                }
                dto.setResType(resourceType);
                if (resourceType == BaseDataResourcesConstant.TYPE_HONOR_TITLE) {
                    dto.setActionType(BaseDataResourcesConstant.ACTION_GET_WEAR);
                } else {
                    dto.setActionType(BaseDataResourcesConstant.ACTION_GET);
                }
                dto.setmTime(DateHelper.getNowSeconds());
                dto.setGainType(BaseDataResourcesConstant.GAIN_TYPE_DAY);
                dto.setDays(day > 0 ? day * count : -1);
                dto.setNum(num != 0 ? num : 1);
                dto.setDesc(title);
                dto.setItemsSourceDetail(title);
                return dto;
            } else {
                return null;
            }
        }
    }

    private void handleResources(String uid, int category, int userType, StarBeatGameVO.PrizeConfigVO vo) {
        String title = CATEGORY_DESC_NEW_MAP.getOrDefault
                (getEventUserType(category, userType), "Carnival of Star-unknown");
        ResourcesDTO dto = prizeConfigToResDTO(uid, vo, title);
        if (dto != null) {
            mqSenderService.asyncHandleResources(dto);
        } else {
            if (-2 == vo.getResourceType()) {
                int beansNum = vo.getResourceNum() == null ? 0 : vo.getResourceNum();
                if (beansNum > 0) {
                    MoneyDetailReq moneyDetailReq = new MoneyDetailReq();
                    moneyDetailReq.setRandomId();
                    moneyDetailReq.setUid(uid);
                    moneyDetailReq.setAtype(ADD_DIAMOND_TYPE);
                    moneyDetailReq.setChanged(beansNum);
                    moneyDetailReq.setTitle(title);
                    moneyDetailReq.setDesc(title);
                    ApiResult<String> addBeansResult = dataCenterService.chargeBeans(moneyDetailReq);
                    if (!addBeansResult.isOk()) {
                        logger.info("change beans fail add={} uid={} ", beansNum, uid);
                    }
                }
            } else {
                logger.info("ResourcesDTO is null vo:{}", vo);
            }
            logger.info("ResourcesDTO is null vo:{}", vo);
        }
    }

    private void pushBigReward(StarBeatGameDTO dto, CarnivalGameData.PrizeConfig screenConfig, CarnivalGameData.PrizeConfig scrollConfig) {
        String uid = dto.getUid();
        String roomId = dto.getRoomId();
        String toRoomId = StringUtils.isEmpty(roomId) ? roomPlayerRedis.getActorRoomStatus(uid) : roomId;
        if (screenConfig != null) {
            if (!StringUtils.isEmpty(toRoomId)) {
                int prizeValue = screenConfig.getPrizeValue();
                String prizeNameEn = screenConfig.getResourceNameEn();
                String prizeNameAr = screenConfig.getResourceNameAr();
                ActorData actorData = actorDao.getActorDataFromCache(uid);
                String name = actorData.getName();
                RoomNotificationMsg msg = new RoomNotificationMsg();
                msg.setUid(uid);
                msg.setUser_name(name);
                msg.setUser_head(ImageUrlGenerator.generateMiniUrl(actorData.getHead()));
//                msg.setHide_head(1);
                msg.setText(TEXT_EN.replace("#username#", name).replace("#prize_name#", prizeNameEn).replace("#prize_value#", String.valueOf(prizeValue)));
                msg.setText_ar(TEXT_AR.replace("#username#", name).replace("#prize_name#", prizeNameAr).replace("#prize_value#", String.valueOf(prizeValue)));
                List<HighlightTextObject> list = new ArrayList<>();

                HighlightTextObject object = new HighlightTextObject();
                object.setText(name);
                object.setHighlightColor("#FFE200");//
                list.add(object);

                HighlightTextObject beanObject = new HighlightTextObject();
                beanObject.setText(String.valueOf(prizeValue));
                beanObject.setHighlightColor("#FFE200");//
                list.add(beanObject);

                HighlightTextObject prizeNameEnObject = new HighlightTextObject();
                beanObject.setText(prizeNameEn);
                beanObject.setHighlightColor("#FFE200");//
                list.add(prizeNameEnObject);

                HighlightTextObject prizeNameArObject = new HighlightTextObject();
                beanObject.setText(prizeNameAr);
                beanObject.setHighlightColor("#FFE200");//
                list.add(prizeNameArObject);

                HighlightTextObject viewObject = new HighlightTextObject();
                viewObject.setText(VIEW_EN);
                viewObject.setHighlightColor("#37B5FF");//
                list.add(viewObject);

                HighlightTextObject viewArObject = new HighlightTextObject();
                viewArObject.setText(VIEW_AR);
                viewArObject.setHighlightColor("#37B5FF");//
                list.add(viewArObject);

                msg.setHighlight_text(list);
                msg.setHighlight_text_ar(list);
                msg.setWeb_type(2);
                msg.setGame_type(GAME_STAR_BEAT);
                msg.setWeb_url(URL_STAR_BEAT);
                roomWebSender.sendRoomWebMsg(toRoomId, null, msg, false);
                logger.info("screenMsg success uid:{} toRoomId:{} prizeId:{} prizeNameEn:{}",
                        uid, toRoomId, screenConfig.getPrizeId(), screenConfig.getResourceNameEn());
            }
        }
        if (scrollConfig != null) {
            toRoomId = StringUtils.isEmpty(toRoomId) ? RoomWebSender.ALL_ROOM : RoomWebSender.ALL_ROOM + "_" + toRoomId;
            int prizeValue = scrollConfig.getPrizeValue();
            String prizeNameEn = scrollConfig.getResourceNameEn();
            String prizeNameAr = scrollConfig.getResourceNameAr();
            ActorData actorData = actorDao.getActorDataFromCache(uid);
            String name = actorData.getName();
            RoomCommonScrollMsg msg = new RoomCommonScrollMsg();
            msg.setUid(uid);
            msg.setPrizeIcon(scrollConfig.getResourceIcon());
            msg.setActionType(19);
            msg.setActionValue(URL_STAR_BEAT);
            msg.setPrizeTextEn(TEXT_EN_M.replace("#username#", name).replace("#prize_name#", prizeNameEn).replace("#prize_value#", String.valueOf(prizeValue)));
            msg.setPrizeTextAr(TEXT_AR_M.replace("#username#", name).replace("#prize_name#", prizeNameAr).replace("#prize_value#", String.valueOf(prizeValue)));
            List<HighlightTextObject> list = new ArrayList<>();

            HighlightTextObject object = new HighlightTextObject();
            object.setText(name);
            object.setHighlightColor("#FFE200");//
            list.add(object);

            HighlightTextObject beanObject = new HighlightTextObject();
            beanObject.setText(String.valueOf(prizeValue));
            beanObject.setHighlightColor("#FFE200");//
            list.add(beanObject);

            HighlightTextObject prizeNameEnObject = new HighlightTextObject();
            beanObject.setText(prizeNameEn);
            beanObject.setHighlightColor("#FFE200");//
            list.add(prizeNameEnObject);

            HighlightTextObject prizeNameArObject = new HighlightTextObject();
            beanObject.setText(prizeNameAr);
            beanObject.setHighlightColor("#FFE200");//
            list.add(prizeNameArObject);

//            HighlightTextObject viewObject = new HighlightTextObject();
//            viewObject.setText(VIEW_EN);
//            viewObject.setHighlightColor("#37B5FF");//
//            list.add(viewObject);

//            HighlightTextObject viewArObject = new HighlightTextObject();
//            viewArObject.setText(VIEW_AR);
//            viewArObject.setHighlightColor("#37B5FF");//
//            list.add(viewArObject);

            msg.setHighlightTextEn(list);
            msg.setHighlightTextAr(list);
            roomWebSender.sendRoomWebMsg(toRoomId, null, msg, false);
            logger.info("scrollMsg success uid:{} toRoomId:{} prizeId:{} prizeNameEn:{}",
                    uid, toRoomId, scrollConfig.getPrizeId(), scrollConfig.getResourceNameEn());
        }


    }

    private int getReduceValue(CarnivalGameData.PrizeConfig config) {
        if (config.getPrizeRealValue() > 0) {
            return config.getPrizeRealValue();
        } else {
            return config.getPrizeValue();
        }
    }

    private CarnivalGameData.PrizeConfig selectMaxPrizeConfig(CarnivalGameData.PrizeConfig maxConfig, CarnivalGameData.PrizeConfig config) {
        if (maxConfig == null) {
            maxConfig = config;
        } else {
            if (config.getPrizeValue() > maxConfig.getPrizeValue()) {
                maxConfig = config;
            }
        }
        return maxConfig;
    }

    private int getUserType(String uid) {
        int charge = rechargeDailyInfoDao.getUserLastRechargeCache(uid, 30);
        int userType;
        if (charge <= 10) {
            userType = 1;
        } else if (charge <= 50) {
            userType = 2;
        } else {
            userType = 3;
        }
        return userType;
    }

    private String getLockKeyByType(int type) {
        return "star:beat:game:type:" + type;
    }

    private String getLockKeyByUid(String uid) {
        return "star:beat:game:draw:" + uid;
    }

    private void doReportEvent(StarBeatGameDTO dto, int feeBeans, int useTicket, Map<Integer, StarBeatGameVO.PrizeConfigVO> prizeConfigVOMap) {
        StarBeatGameLogEvent event = new StarBeatGameLogEvent();
        event.setUid(dto.getUid());
        event.setCtime(DateHelper.getNowSeconds());
        event.setSence(ADD_DIAMOND_TITLE);
        event.setSence_detail(getEventUserType(dto.getCategory(), dto.getUserType()));
        event.setRoom_id(dto.getRoomId());
        event.setCost_diamonds(Math.max(feeBeans, 0));
        event.setCost_ticket(Math.max(useTicket, 0));
        event.setDraw_nums(dto.getNum());
        Map<Integer, JSONObject> prizeConfigMap = new HashMap<>();
        final String[] desc = {""};
        prizeConfigVOMap.forEach((k, v) -> {
            JSONObject jsonObject = new JSONObject();
            jsonObject.put("resourceName", v.getResourceNameEn());
            jsonObject.put("resourceNum", v.getResourceNum());
            jsonObject.put("resourceTime", v.getResourceTime());
            jsonObject.put("awardNum", v.getAwardNum());
            if (v.getAwardType() == 1) {
                desc[0] = "lucky_hit";
            }
            prizeConfigMap.put(k, jsonObject);
        });
        event.setDraw_result(JSON.toJSONString(prizeConfigMap));
        event.setDesc(desc[0]);
        eventReport.track(new EventDTO(event));
    }

    private int getEventUserType(int category, int userType) {
        int base = 10;
        if (category == 0) {
            base = 100;
        } else if (category == 1) {
            base = 200;
        } else if (category == 2) {
            base = 300;
        }
        return base + userType;
    }
}
