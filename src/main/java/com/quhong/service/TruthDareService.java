package com.quhong.service;

import com.quhong.core.concurrency.BaseTaskFactory;
import com.quhong.core.concurrency.queues.SlowTaskQueue;
import com.quhong.core.concurrency.tasks.Task;
import com.quhong.core.config.ServerConfig;
import com.quhong.core.timers.LoopTask;
import com.quhong.core.timers.TimerService;
import com.quhong.core.utils.DateHelper;
import com.quhong.dailyTask.CommonTaskService;
import com.quhong.data.*;
import com.quhong.data.dto.MoneyDetailReq;
import com.quhong.dto.TruthDareDTO;
import com.quhong.enums.*;
import com.quhong.exception.GameException;
import com.quhong.feign.DataCenterService;
import com.quhong.image.ImageUrlGenerator;
import com.quhong.mongo.dao.ActorDao;
import com.quhong.mongo.dao.RoomMemberDao;
import com.quhong.mq.MqSenderService;
import com.quhong.msg.room.TruthDareGamePushMsg;
import com.quhong.mysql.dao.VipInfoDao;
import com.quhong.redis.TruthOrDareRedis;
import com.quhong.redis.TurntableGameRedis;
import com.quhong.room.RoomWebSender;
import com.quhong.utils.RoomUtils;
import com.quhong.vo.TruthDareGameVO;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.ThreadLocalRandom;
import java.util.stream.Collectors;

/**
 * 真心话大冒险转盘
 *
 * <AUTHOR>
 * @date 2023/3/20
 */
@Service
public class TruthDareService extends SlowTaskQueue {

    private static final Logger logger = LoggerFactory.getLogger(TruthDareService.class);

    private static final String JOIN_GAME_TITLE = "join smart wheel game";

    private static final String TAKE_A_PERCENTAGE_TITLE = "take a percentage in smart wheel game";
    private static final String RETURN_TITLE = "return in smart wheel game";
    private static final int A_TYPE = 41;

    /**
     * 最大等待时间 30 分钟
     */
    private static final int MAX_WAIT_TIME = 30 * 60;

    @Resource
    private TruthOrDareRedis truthOrDareRedis;
    @Resource
    private TurntableGameRedis turntableGameRedis;
    @Resource
    private ActorDao actorDao;
    @Resource
    private RoomMemberDao memberDao;
    @Resource
    private DataCenterService dataCenterService;
    @Resource
    private RoomWebSender roomWebSender;
    @Resource
    private VipInfoDao vipInfoDao;
    @Resource
    private MqSenderService mqSenderService;
    @Resource
    private CommonTaskService commonTaskService ;

    @PostConstruct
    public void postInit() {
        TimerService.getService().addDelay(new LoopTask(this, 1000) {
            @Override
            protected void execute() {
                Set<String> runningEndGameIds = truthOrDareRedis.getRunningEndGameIds(DateHelper.getNowSeconds());
                if (!CollectionUtils.isEmpty(runningEndGameIds)) {
                    logger.info("truth or dare turntable game settle accounts. runningEndGameIds.size={} runningEndGameIds={}", runningEndGameIds.size(), Arrays.toString(runningEndGameIds.toArray()));
                    BaseTaskFactory.getFactory().addSlow(new Task() {
                        @Override
                        protected void execute() {
                            for (String gameId : runningEndGameIds) {
                                truthOrDareRedis.removeGameTimerRunning(gameId);
                                // 游戏结算
                                gameSettleAccounts(gameId);
                            }
                        }
                    });
                }
                Set<String> waitingEndGameIds = truthOrDareRedis.getWaitingEndGameIds(DateHelper.getNowSeconds());
                if (!CollectionUtils.isEmpty(waitingEndGameIds)) {
                    logger.info("dismiss truth or dare turntable game. waitingEndGameIds.size={} waitingEndGameIds={}", waitingEndGameIds.size(), Arrays.toString(waitingEndGameIds.toArray()));
                    BaseTaskFactory.getFactory().addSlow(new Task() {
                        @Override
                        protected void execute() {
                            for (String gameId : waitingEndGameIds) {
                                truthOrDareRedis.removeGameTimerWaiting(gameId);
                                // 结束等待超时游戏
                                dismissTurntableGame(gameId);
                            }
                        }
                    });
                }
            }
        });
    }

    /**
     * 创建游戏
     */
    public TruthDareGameVO createGame(TruthDareDTO reqDTO) {
        ActorData actorData = actorDao.getActorDataFromCache(reqDTO.getUid());
        if (actorData == null) {
            logger.error("can not find actor data. uid={}", reqDTO.getUid());
            throw new GameException(GameHttpCode.USER_NOT_EXISTING);
        }
        RoomRoleData roleData = memberDao.getRoleData(reqDTO.getRoom_id(), reqDTO.getUid());
        if (!roleData.isAdmin()) {
            logger.info("Only room owner and admin can create the game. uid={}, roomId={}", reqDTO.getUid(), reqDTO.getRoom_id());
            throw new GameException(GameHttpCode.OWNER_AND_ADMIN_CAN_CREATE);
        }
        String truthGameId = truthOrDareRedis.getGameIdFromRedis(reqDTO.getRoom_id());
        String turntableGameId = turntableGameRedis.getGameIdFromRedis(reqDTO.getRoom_id());
        if (!StringUtils.isEmpty(truthGameId) || !StringUtils.isEmpty(turntableGameId)) {
            logger.info("your room exist game. uid={} roomId={} truthGameId={} turntableGameId={}", reqDTO.getUid(), reqDTO.getRoom_id(), truthGameId, turntableGameId);
            throw new GameException(GameHttpCode.GAME_HAS_ALREADY_EXISTED);
        }
        if (reqDTO.getJoin_beans() != 0) {
            // 扣除参加转盘游戏的费用
            deductCost(reqDTO.getUid(), reqDTO.getRoom_id(), reqDTO.getJoin_beans());
        }
        TruthOrDareInfo gameInfo = buildTruthOrDareInfo(reqDTO);
        truthOrDareRedis.saveGameInfo(gameInfo);
        truthOrDareRedis.setGameIdInRedis(reqDTO.getRoom_id(), gameInfo.get_id());
        int endTime = DateHelper.getNowSeconds() + MAX_WAIT_TIME;
        truthOrDareRedis.setGameTimerWaiting(gameInfo.get_id(), endTime);
        // 发送创建游戏im
        sendTruthDareGamePushMsg(gameInfo.get_id(), gameInfo.getRoom_id(), TruthDareGameState.CREATE);
        TruthDareGameVO vo = new TruthDareGameVO();
        BeanUtils.copyProperties(gameInfo, vo);
        return vo;
    }

    /**
     * 加入转盘游戏
     */
    public TruthDareGameVO joinGame(TruthDareDTO reqDTO) {
        ActorData actorData = actorDao.getActorDataFromCache(reqDTO.getUid());
        if (actorData == null) {
            logger.error("The actor not found. uid={}", reqDTO.getUid());
            throw new GameException(GameHttpCode.THE_ACTOR_NOT_FOUND);
        }
        TruthOrDareInfo gameInfo = truthOrDareRedis.getGameInfo(reqDTO.getGame_id());
        if (gameInfo == null) {
            logger.error("The game is not found. uid={} gameId={}", reqDTO.getUid(), reqDTO.getGame_id());
            throw new GameException(GameHttpCode.THE_GAME_IS_NOT_FOUND);
        }
        // 校验游戏是否正在进行
        if (gameInfo.getSstatus() == TruthDareConstant.GAME_RUNNING) {
            logger.info("The game is running. uid={} roomId={} gameId={}", reqDTO.getUid(), gameInfo.getRoom_id(), reqDTO.getGame_id());
            throw new GameException(GameHttpCode.THE_GAME_IS_RUNNING);
        }
        // 校验游戏是否已经结束
        if (gameInfo.getSstatus() == TruthDareConstant.GAME_END) {
            logger.info("The game has been cancelled. uid={} roomId={}  gameId={}", reqDTO.getUid(), gameInfo.getRoom_id(), reqDTO.getGame_id());
            throw new GameException(GameHttpCode.GAME_HAS_BEEN_CANCELLED);
        }
        List<TruthOrDareInfo.Actor> players = gameInfo.getPlayers();
        if (CollectionUtils.isEmpty(players)) {
            players = new ArrayList<>();
        }
        // 校验人数是否大于最大玩家数
        if (players.size() >= TruthDareConstant.MAX_PLAYERS_NUM) {
            logger.info("The number of players reach max. uid={} roomId={}  gameId={}", reqDTO.getUid(), gameInfo.getRoom_id(), reqDTO.getGame_id());
            throw new GameException(GameHttpCode.PLAYERS_NUM_REACH_MAX);
        }
        // 校验是否已经加入游戏
        if (players.stream().anyMatch(a -> a.getUid().equals(reqDTO.getUid()))) {
            logger.info("You have joined the game. uid={} roomId={}  gameId={}", reqDTO.getUid(), gameInfo.getRoom_id(), reqDTO.getGame_id());
            throw new GameException(GameHttpCode.YOU_HAVE_JOINED_THE_GAME);
        }
        players.add(getActorInfo(reqDTO.getUid()));
        gameInfo.setPlayers(players);
        // 扣入场费用
        if (gameInfo.getBeans() != 0) {
            deductCost(reqDTO.getUid(), gameInfo.getRoom_id(), gameInfo.getBeans());
        }
        truthOrDareRedis.saveGameInfo(gameInfo);
        // 发送创建游戏im
        sendTruthDareGamePushMsg(gameInfo.get_id(), gameInfo.getRoom_id(), TruthDareGameState.JOIN);
        TruthDareGameVO vo = new TruthDareGameVO();
        BeanUtils.copyProperties(gameInfo, vo);
        return vo;
    }

    public TruthDareGameVO startGame(TruthDareDTO reqDTO) {
        ActorData actorData = actorDao.getActorDataFromCache(reqDTO.getUid());
        if (actorData == null) {
            logger.error("can not find actor data. uid={}", reqDTO.getUid());
            throw new GameException(GameHttpCode.PARAM_ERROR);
        }
        TruthOrDareInfo gameInfo = truthOrDareRedis.getGameInfo(reqDTO.getGame_id());
        if (gameInfo == null) {
            logger.info("can not find game info. gameId={}", reqDTO.getGame_id());
            throw new GameException(GameHttpCode.PARAM_ERROR);
        }
        if (gameInfo.getSstatus() == TruthDareConstant.GAME_END) {
            logger.info("The game has been cancelled. uid={} roomId={}  gameId={}", reqDTO.getUid(), gameInfo.getRoom_id(), reqDTO.getGame_id());
            throw new GameException(GameHttpCode.GAME_HAS_BEEN_CANCELLED);
        }
        List<TruthOrDareInfo.Actor> players = gameInfo.getPlayers();
        // 在等待游戏开始时判断人数
        if (gameInfo.getSstatus() == TruthDareConstant.GAME_WAITING && players.size() < (ServerConfig.isProduct() ? TruthDareConstant.MIN_PLAYERS_NUM : 3)) {
            logger.info("Need at least 4 players,please wait other players joining first. gameId={} players.size={}", reqDTO.getGame_id(), players.size());
            throw new GameException(GameHttpCode.PLEASE_WAIT_OTHER_PLAYERS_JOINING);
        }
        TruthOrDareInfo.Actor thisRoundLoser = new TruthOrDareInfo.Actor();
        int nowTime = DateHelper.getNowSeconds();
        // 点击开始时判断是否第一次点击
        if (gameInfo.getFirst_start_flag() == 1) {
            gameInfo.setSstatus(TruthDareConstant.GAME_RUNNING);
            gameInfo.setLast_start_at(nowTime);
            gameInfo.setFirst_start_flag(0);
            gameInfo.setPlayerNum(players.size());
            // 本轮输家
            int index = ThreadLocalRandom.current().nextInt(players.size());
            players.get(index).setIsLoser(1);
            thisRoundLoser.copyFrom(players.get(index));
        } else {
            List<TruthOrDareInfo.Actor> newPlayers = players.stream().filter(k -> k.getIsLoser() == 0).collect(Collectors.toList());
            // 本轮输家
            int index = ThreadLocalRandom.current().nextInt(newPlayers.size());
            newPlayers.get(index).setIsLoser(1);
            thisRoundLoser.copyFrom(newPlayers.get(index));
            gameInfo.setPlayers(newPlayers);
            if (newPlayers.size() <= 2) {
                // 最后一轮 结束了立马结算
                gameInfo.setSstatus(TruthDareConstant.GAME_END);
                truthOrDareRedis.removeGameId(gameInfo.getRoom_id());
                truthOrDareRedis.setGameTimerRunning(reqDTO.getGame_id(), nowTime);
                truthOrDareRedis.removeGameTimerWaiting(reqDTO.getGame_id());
            } else {
                truthOrDareRedis.setGameTimerRunning(reqDTO.getGame_id(), nowTime + MAX_WAIT_TIME);
            }
        }
        // 本轮真心话
        TruthDareGameVO vo = new TruthDareGameVO();
        gameInfo.setTruth(gameInfo.getTruths().get(ThreadLocalRandom.current().nextInt(gameInfo.getTruths().size())));
        gameInfo.setLosers(thisRoundLoser);
        truthOrDareRedis.saveGameInfo(gameInfo);
        BeanUtils.copyProperties(gameInfo, vo);
        // 发送关闭游戏消息
        sendTruthDareGamePushMsg(gameInfo.get_id(), gameInfo.getRoom_id(), TruthDareGameState.START);

        for (TruthOrDareInfo.Actor player : gameInfo.getPlayers()) {
            // 玩真心话转盘游戏的每日任务
            commonTaskService.sendCommonTaskMq(new CommonMqTopicData(player.getUid(), gameInfo.getRoom_id(), "", gameInfo.get_id(), CommonMqTaskConstant.PLAY_TRUTH_DARE_WHEEL, 1));
        }
        return vo;
    }


    public void closeAtWaiting(TruthDareDTO reqDTO) {
        ActorData actorData = actorDao.getActorDataFromCache(reqDTO.getUid());
        if (actorData == null) {
            logger.error("can not find actor data. uid={}", reqDTO.getUid());
            throw new GameException(GameHttpCode.USER_NOT_EXISTING);
        }
        TruthOrDareInfo gameInfo = truthOrDareRedis.getGameInfo(reqDTO.getGame_id());
        if (gameInfo == null) {
            logger.error("The game is not found. uid={} gameId={}", reqDTO.getUid(), reqDTO.getGame_id());
            return;
            // throw new GameException(GameHttpCode.THE_GAME_IS_NOT_FOUND);
        }
        if (gameInfo.getSstatus() == TruthDareConstant.GAME_END) {
            logger.info("The game is end. uid={} gameId={}", reqDTO.getUid(), reqDTO.getGame_id());
            return;
            // throw new GameException(GameHttpCode.GAME_HAS_BEEN_CANCELLED);
        }
        if (!reqDTO.getUid().equals(gameInfo.getCreate_uid())) {
            logger.info("Yor are not the creator. uid={} gameId={}", reqDTO.getUid(), reqDTO.getGame_id());
            throw new GameException(GameHttpCode.YOU_ARE_NOT_THE_CREATOR);
        }
        truthOrDareRedis.setGameTimerWaiting(gameInfo.get_id(), DateHelper.getNowSeconds());
        truthOrDareRedis.removeGameId(gameInfo.getRoom_id());
        // 发送关闭游戏消息
        sendTruthDareGamePushMsg(gameInfo.get_id(), gameInfo.getRoom_id(), TruthDareGameState.CLOSE);
    }

    /**
     * 游戏过程中刷新游戏的状态
     */
    public TruthDareGameVO refreshGame(TruthDareDTO reqDTO) {
        TruthOrDareInfo gameInfo = truthOrDareRedis.getGameInfo(reqDTO.getGame_id());
        TruthDareGameVO vo = new TruthDareGameVO();
        BeanUtils.copyProperties(gameInfo, vo);
        return vo;
    }

    /**
     * 游戏正常结束，结算处理
     */
    private void gameSettleAccounts(String gameId) {
        TruthOrDareInfo gameInfo = truthOrDareRedis.getGameInfo(gameId);
        if (gameInfo == null) {
            logger.error("can not find game info. gameId={}", gameId);
            return;
        }
        gameInfo.setSstatus(TruthDareConstant.GAME_END);
        truthOrDareRedis.saveGameInfo(gameInfo);
        truthOrDareRedis.removeGameId(gameInfo.getRoom_id());
        if (gameInfo.getBeans() < 1) {
            logger.info("game join beans is less than 1. gameId={} joinBean={}", gameId, gameInfo.getBeans());
            return;
        }
        // todo 保存转盘游戏记录
        int totalBeans = gameInfo.getBeans() * gameInfo.getPlayerNum();
        // 房主抽成
        hostGetBeans(totalBeans, gameInfo.getRoom_id());
    }

    /**
     * 游戏等待超时，结算游戏
     */
    private void dismissTurntableGame(String gameId) {
        TruthOrDareInfo gameInfo = truthOrDareRedis.getGameInfo(gameId);
        if (gameInfo == null) {
            logger.error("can not find game info. gameId={}", gameId);
            return;
        }
        if (gameInfo.getSstatus() != TruthDareConstant.GAME_END) {
            // 未结束的游戏关闭才会退还费用
            if (gameInfo.getBeans() < 1) {
                logger.info("game join beans is less than 1. gameId={} joinBean={}", gameId, gameInfo.getBeans());
                return;
            }
            List<TruthOrDareInfo.Actor> players = gameInfo.getPlayers();
            int remainingPlayerNum = 0;
            if (!CollectionUtils.isEmpty(players)) {
                for (TruthOrDareInfo.Actor actor : players) {
                    if (actor.getIsLoser() == 1) {
                        continue;
                    }
                    remainingPlayerNum ++;
                    chargeBeans(actor.getUid(), gameInfo.getRoom_id(), gameInfo.getBeans(), RETURN_TITLE);
                }
            }
            if (gameInfo.getSstatus() == TruthDareConstant.GAME_RUNNING) {
                // 房主抽成
                int totalBeans = gameInfo.getBeans() * (gameInfo.getPlayerNum() - remainingPlayerNum);
                hostGetBeans(totalBeans, gameInfo.getRoom_id());
            }
        }
        gameInfo.setSstatus(TruthDareConstant.GAME_END);
        truthOrDareRedis.saveGameInfo(gameInfo);
        truthOrDareRedis.removeGameId(gameInfo.getRoom_id());
    }

    private void hostGetBeans(int totalBeans, String roomId) {
        int hostGetBeans = (int) (totalBeans * 0.5);
        String hostId = RoomUtils.getRoomHostId(roomId);
        if (hostGetBeans >= 1) {
            // 改变房主钻石
            chargeBeans(hostId, roomId, hostGetBeans, TAKE_A_PERCENTAGE_TITLE);
        }
    }

    private void sendTruthDareGamePushMsg(String gameId, String roomId, int status) {
        TruthDareGamePushMsg msg = new TruthDareGamePushMsg();
        msg.setGame_id(gameId);
        msg.setGame_status(status);
        roomWebSender.sendRoomWebMsg(roomId, "", msg, true);
    }

    private TruthOrDareInfo buildTruthOrDareInfo(TruthDareDTO reqDTO) {
        TruthOrDareInfo info = new TruthOrDareInfo();
        info.set_id(UUID.randomUUID().toString().replace("-", ""));
        info.setRoom_id(reqDTO.getRoom_id());
        info.setSstatus(TruthDareConstant.GAME_WAITING);
        info.setBeans(reqDTO.getJoin_beans());
        info.setCreate_uid(reqDTO.getUid());
        info.setFirst_start_flag(1);
        // opt_msg 映射 2条变4条，3条变6条，4条以上不用管
        if (reqDTO.getOpt_msg().size() <= 3) {
            ArrayList<String> truths = new ArrayList<>(reqDTO.getOpt_msg());
            truths.addAll(reqDTO.getOpt_msg());
            info.setTruths(truths);
        } else {
            info.setTruths(reqDTO.getOpt_msg());
        }
        info.setPlayers(Collections.singletonList(getActorInfo(reqDTO.getUid())));
        info.setCtime(DateHelper.getNowSeconds());
        return info;
    }

    /**
     * 获取用户信息
     */
    private TruthOrDareInfo.Actor getActorInfo(String uid) {
        if (StringUtils.isEmpty(uid)) {
            return null;
        }
        ActorData actorData = actorDao.getActorDataFromCache(uid);
        if (actorData == null) {
            return null;
        }
        TruthOrDareInfo.Actor actor = new TruthOrDareInfo.Actor();
        actor.setUid(uid);
        actor.setName(actorData.getName());
        actor.setHead(ImageUrlGenerator.generateRoomUserUrl(actorData.getHead(), vipInfoDao.getIntVipLevelFromCache(actor.getUid())));
        return actor;
    }

    /**
     * 扣除入场费用
     */
    private void deductCost(String uid, String roomId, int joinBeans) {
        ApiResult<String> result = dataCenterService.reduceBeans(buildMoneyDetailReq(uid, roomId, -joinBeans, JOIN_GAME_TITLE));
        if (result.isError()) {
            if (1 == result.getCode().getCode()) {
                logger.info("diamonds are not enough. uid={} joinBeans={}", uid, joinBeans);
                throw new GameException(GameHttpCode.DIAMOND_NOT_ENOUGH);
            }
            logger.error("reduce beans error, msg={}", result.getCode().getMsg());
            throw new GameException(GameHttpCode.SERVER_ERROR);
        }
    }

    /**
     * 加钻石
     */
    private void chargeBeans(String uid, String roomId, int changed, String title) {
        mqSenderService.asyncChargeDiamonds(buildMoneyDetailReq(uid, roomId, changed, title));
    }

    private MoneyDetailReq buildMoneyDetailReq(String uid, String roomId, int changed, String title) {
        MoneyDetailReq moneyDetailReq = new MoneyDetailReq();
        moneyDetailReq.setRandomId();
        moneyDetailReq.setRoomId(roomId);
        moneyDetailReq.setUid(uid);
        moneyDetailReq.setAtype(A_TYPE);
        moneyDetailReq.setChanged(changed);
        moneyDetailReq.setTitle(title);
        moneyDetailReq.setDesc("");
        return moneyDetailReq;
    }
}
