package com.quhong.service;

import com.alibaba.fastjson.JSON;
import com.quhong.config.CityDiggerPrizeConfig;
import com.quhong.core.utils.DateHelper;
import com.quhong.dailyTask.CommonTaskService;
import com.quhong.data.ActorData;
import com.quhong.data.CommonMqTopicData;
import com.quhong.enums.CityDiggerConstant;
import com.quhong.enums.CommonMqTaskConstant;
import com.quhong.image.ImageUrlGenerator;
import com.quhong.mysql.config.DBLogMysqlBean;
import com.quhong.mysql.dao.HeartRecordDao;
import com.quhong.mysql.dao.VipInfoDao;
import com.quhong.mysql.data.CityDiggerData;
import com.quhong.mysql.data.CityDiggerPrizeData;
import com.quhong.mysql.data.HeartRecordData;
import com.quhong.mysql.mapper.ustar_log.CityDiggerMapper;
import com.quhong.mysql.mapper.ustar_log.CityDiggerPrizeMapper;
import com.quhong.redis.CityDiggerRedis;
import com.quhong.vo.CityDiggerVo;
import com.quhong.vo.GiftsMqVo;
import com.quhong.vo.PrizeVo;
import com.quhong.vo.RecentPrizeVo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.*;

@Service
public class CityDiggerService {
    private static final Logger logger = LoggerFactory.getLogger(CityDiggerService.class);

    private static final String MIC = "mic";
    private static final String DIAMOND = "diamond";
    private static final String HEART_RECORD_TITLE = "Play city treasure hunt";
    private static final String HEART_RECORD_REMARK = "city treasure hunt";

    @Resource
    private CityDiggerMapper cityDiggerMapper;
    @Resource
    private CityDiggerPrizeMapper cityDiggerPrizeMapper;
    @Resource
    private CityDiggerRedis cityDiggerRedis;
    @Resource
    private ActorService actorService;
    @Resource
    private CityDiggerPrizeMapper prizeMapper;
    @Resource
    private HeartRecordDao heartRecordDao;
    @Resource
    private CityDiggerPrizeConfig config;
    @Resource
    private GiftsService giftsService;
    @Resource
    private VipInfoDao vipInfoDao;
    @Resource
    private CommonTaskService commonTaskService;


    @Transactional(value = DBLogMysqlBean.USTAR_LOG_TRANSACTION)
    public void throwDice(CityDiggerVo vo, int heartConsume, int heart, int heartReceived) {
        CityDiggerData data = getCityDiggerData(vo.getUid());
        int curStep = data.getStep();
        int nextStep = curStep + (int) (Math.random() * 6) + 1;
        vo.setCurStep(curStep);
        vo.setDiceStep(nextStep);
        vo.setReceived(stringToIntegerSet(data.getReceived()));
        executeStep(curStep, nextStep, vo);

        // 保存步数及心心消耗
        data.setConsume(data.getConsume() + heartConsume);
        data.setStep(vo.getNextStep() == config.getFinalStep() ? 1 : vo.getNextStep());
        data.setReceived(vo.getNextStep() == config.getFinalStep() ? "[]" : vo.getReceived().toString());
        data.setMtime(DateHelper.getNowSeconds());
        updateCityDiggerData(data);
        // 记录心心流水
        heartRecordDao.insert(new HeartRecordData(vo.getUid(), -heartConsume, heart, heartReceived, HEART_RECORD_TITLE, HEART_RECORD_REMARK));
        // 保存心心数, mongodb不支持事务，放到最后处理
        actorService.updateHeart(vo.getUid(), heart);
        // 记录日榜消费
        cityDiggerRedis.logRanking(vo.getUid(), CityDiggerConstant.HEART_CONSUME);
        // 发送任务消息
        commonTaskService.sendCommonTaskMq(new CommonMqTopicData(vo.getUid(), "", vo.getUid(), vo.getUid(), CommonMqTaskConstant.PLAY_CITY_TREASURE, 1));
    }

    /**
     * @param curStep  当前步数
     * @param nextStep 下一步
     * @param vo       结果
     */
    private void executeStep(int curStep, int nextStep, CityDiggerVo vo) {
        if (isBananaPeelings(nextStep)) {
            // 香蕉皮返回上一步
            nextStep = curStep;
        } else if (isBomb(nextStep)) {
            // 炸弹返回起点
            nextStep = 1;
            // 重置已领取的奖励
            vo.setReceived(new HashSet<>());
        } else if (isGiftBox(nextStep)) {
            // 礼盒，可随机爆钻石、礼物、麦位框、座驾
            List<PrizeVo> prizeVos = getGiftBoxRewards(vo);
            if (CollectionUtils.isEmpty(vo.getPrizes())) {
                vo.setPrizes(prizeVos);
            } else {
                vo.getPrizes().addAll(prizeVos);
            }
            vo.getReceived().add(nextStep);
        } else if (isMicFrame(nextStep)) {
            //麦位框，有效期1天，重复获得可累加，数量：7
            List<PrizeVo> prizeVos = getMicFrameRewards(vo, nextStep);
            if (CollectionUtils.isEmpty(vo.getPrizes())) {
                vo.setPrizes(prizeVos);
            } else {
                vo.getPrizes().addAll(prizeVos);
            }
            vo.getReceived().add(nextStep);
        } else if (isRocket(nextStep)) {
            // 火箭，向前移动5步
            executeStep(nextStep, nextStep += 5, vo);
        } else if (nextStep == config.getFinalStep()) {
            // 终极奖励：随机爆座驾1天、200钻石、99钻礼物
            List<PrizeVo> finalRewards = getFinalReward(vo);
            if (CollectionUtils.isEmpty(vo.getPrizes())) {
                vo.setPrizes(finalRewards);
            } else {
                vo.getPrizes().addAll(finalRewards);
            }
        } else if (nextStep > config.getFinalStep()) {
            // 骰子数大于终点数时保持原位
            nextStep = curStep;
        }
        vo.setNextStep(nextStep);
    }

    public List<PrizeVo> getGiftBoxRewards(CityDiggerVo vo) {
        List<PrizeVo> prizeVos = new ArrayList<>();
        PrizeVo prizeVo = config.getGiftBoxRewards().get((int) (Math.random() * config.getGiftBoxRewards().size()));
        if (prizeVo.getType().equals(MIC)) {
            return getMicFrameRewards(vo, null);
        } else {
            prizeVos.add(prizeVo);
            logger.info("receive gift box uid={} gift name={} sourceId={} diamond={}",
                    vo.getUid(), prizeVo.getName(), prizeVo.getSourceId(), prizeVo.getDiamond());
            // 发放奖励
            if (prizeVo.getDiamond() > 0) {
                giftsService.sendGiftToMq(new GiftsMqVo(vo.getUid(), prizeVo.getDiamond()));
            } else {
                giftsService.sendGiftToMq(new GiftsMqVo(vo.getUid(), prizeVo));
            }
            // 记录中奖信息
            prizeMapper.insert(new CityDiggerPrizeData(vo.getUid(), vo.getName(), prizeVo));
            cityDiggerRedis.deleteRecentPrize();
        }
        return prizeVos;
    }

    public List<PrizeVo> getMicFrameRewards(CityDiggerVo vo, Integer step) {
        List<PrizeVo> prizeVos = new ArrayList<>();
        PrizeVo prizeVo;
        if (null == step) {
            prizeVo = config.getMicFrameRewards().get((int) (Math.random() * config.getMicFrameRewards().size()));
        } else {
            prizeVo = config.getMicFrameRewards().get(Arrays.asList(config.getMicFrame()).indexOf(step));
        }
        prizeVos.add(prizeVo);
        logger.info("receive frame rewards uid={} gift name={} sourceId={} diamond={}",
                vo.getUid(), prizeVo.getName(), prizeVo.getSourceId(), prizeVo.getDiamond());
        // 发放奖励
        giftsService.sendGiftToMq(new GiftsMqVo(vo.getUid(), prizeVo));
        // 记录中奖信息
        prizeMapper.insert(new CityDiggerPrizeData(vo.getUid(), vo.getName(), prizeVo));
        cityDiggerRedis.deleteRecentPrize();
        return prizeVos;
    }

    public List<PrizeVo> getFinalReward(CityDiggerVo vo) {
        List<PrizeVo> prizeVos = new ArrayList<>();
        // PrizeVo finalReward = config.getFinalRewards().get((int) (Math.random() * config.getFinalRewards().size()));
        PrizeVo finalReward = config.getMicFrameRewards().get((int) (Math.random() * config.getMicFrameRewards().size()));
        prizeVos.add(finalReward);
        logger.info("receive final rewards uid={} rewards={}", vo.getUid(), finalReward);
        // 发放奖励
        if (DIAMOND.equals(finalReward.getType())) {
            giftsService.sendGiftToMq(new GiftsMqVo(vo.getUid(), finalReward.getDiamond()));
        } else {
            giftsService.sendGiftToMq(new GiftsMqVo(vo.getUid(), finalReward));
        }
        // 记录中奖信息
        prizeMapper.insert(new CityDiggerPrizeData(vo.getUid(), vo.getName(), finalReward));
        cityDiggerRedis.deleteRecentPrize();
        return prizeVos;
    }

    public CityDiggerData getCityDiggerData(String uid) {
        CityDiggerData data = cityDiggerMapper.getByUid(uid);
        if (null == data) {
            CityDiggerData newData = new CityDiggerData();
            int nowSeconds = DateHelper.getNowSeconds();
            newData.setUid(uid);
            newData.setStep(1);//从第一步开始
            newData.setConsume(0);
            newData.setReceived("[]");
            newData.setCtime(nowSeconds);
            newData.setMtime(nowSeconds);
            cityDiggerMapper.insert(newData);
            return newData;
        }
        return data;
    }

    public void updateCityDiggerData(CityDiggerData data) {
        cityDiggerMapper.update(data);
    }

    public List<CityDiggerPrizeData> getPrizeList(String uid, Integer curPage, Integer pageSize) {
        if (null == curPage) {
            curPage = 1;
        }
        if (null == pageSize) {
            pageSize = 100;
        }
        int offset = (curPage - 1) * pageSize;
        return cityDiggerPrizeMapper.getByUid(uid, offset, pageSize);
    }

    public List<CityDiggerData> getRanking() {
        List<CityDiggerData> ranking = new ArrayList<>();
        Map<String, Long> linkedRankMap = cityDiggerRedis.getRanking();
        for (Map.Entry<String, Long> entry : linkedRankMap.entrySet()) {
            CityDiggerData data = new CityDiggerData();
            ActorData actor = actorService.getActorDataFromCache(entry.getKey());
            if (null == actor) {
                logger.error("can not find actor. uid={}", data.getUid());
                continue;
            }
            data.setConsume(entry.getValue());
            data.setName(actor.getName());
            data.setRid(actor.getRid() + "");
            data.setHead(ImageUrlGenerator.generateRoomUserUrl(actor.getHead(), vipInfoDao.getIntVipLevelFromCache(entry.getKey())));
            ranking.add(data);
        }
        return ranking;
    }

    public List<RecentPrizeVo> getRecentPrize() {
        List<RecentPrizeVo> recentPrizeCache = cityDiggerRedis.getRecentPrizeCache();
        if (recentPrizeCache != null) {
            logger.info("get recent prize list from cache. list.size={}", recentPrizeCache.size());
            return recentPrizeCache;
        }
        List<CityDiggerPrizeData> recentPrize = cityDiggerPrizeMapper.getRecentPrize();
        recentPrizeCache = new ArrayList<>();
        for (CityDiggerPrizeData data : recentPrize) {
            recentPrizeCache.add(new RecentPrizeVo(data.getName(), data.getPrizeName(), data.getPrizeNameAr()));
        }
        cityDiggerRedis.cacheRecentPrize(recentPrizeCache);
        return recentPrizeCache;
    }

    private boolean isBananaPeelings(int step) {
        return Arrays.stream(config.getBananaPeelings()).anyMatch(integer -> integer == step);
    }

    private boolean isBomb(int step) {
        return Arrays.stream(config.getBomb()).anyMatch(integer -> integer == step);
    }

    private boolean isGiftBox(int step) {
        return Arrays.stream(config.getGiftBox()).anyMatch(integer -> integer == step);
    }

    private boolean isMicFrame(int step) {
        return Arrays.stream(config.getMicFrame()).anyMatch(integer -> integer == step);
    }

    private boolean isRocket(int step) {
        return Arrays.stream(config.getRocket()).anyMatch(integer -> integer == step);
    }

    @SuppressWarnings("unchecked")
    public HashSet<Integer> stringToIntegerSet(String received) {
        try {
            if (StringUtils.isEmpty(received)) {
                return new HashSet<>();
            }
            return JSON.parseObject(received, HashSet.class);
        } catch (Exception e) {
            logger.error("string to integer set error. error msg={}", e.getMessage());
            return new HashSet<>();
        }
    }
}
