package com.quhong.service;

import com.alibaba.fastjson.JSONObject;
import com.quhong.analysis.EventDTO;
import com.quhong.analysis.EventReport;
import com.quhong.analysis.PkGameLogEvent;
import com.quhong.config.CaffeineCacheConfig;
import com.quhong.core.concurrency.BaseTaskFactory;
import com.quhong.core.concurrency.queues.SlowTaskQueue;
import com.quhong.core.concurrency.tasks.Task;
import com.quhong.core.config.ServerConfig;
import com.quhong.core.timers.LoopTask;
import com.quhong.core.timers.TimerService;
import com.quhong.core.utils.DateHelper;
import com.quhong.dailyTask.CommonTaskService;
import com.quhong.dailyTask.DailyTaskService;
import com.quhong.data.*;
import com.quhong.data.dto.MoneyDetailReq;
import com.quhong.dto.PkGameDTO;
import com.quhong.enums.*;
import com.quhong.exception.GameException;
import com.quhong.feign.DataCenterService;
import com.quhong.image.ImageUrlGenerator;
import com.quhong.mongo.dao.*;
import com.quhong.mongo.data.*;
import com.quhong.mq.MqSenderService;
import com.quhong.msg.obj.PKExtraInfoObject;
import com.quhong.msg.obj.PKInfoObject;
import com.quhong.msg.obj.PKUserInfoObject;
import com.quhong.msg.obj.RidInfoObject;
import com.quhong.msg.room.*;
import com.quhong.mysql.dao.GiftDao;
import com.quhong.mysql.dao.VipInfoDao;
import com.quhong.mysql.data.GiftData;
import com.quhong.redis.*;
import com.quhong.room.RoomWebSender;
import com.quhong.utils.RoomUtils;
import com.quhong.vo.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.BeanUtils;
import org.springframework.cache.annotation.Cacheable;
import org.springframework.data.redis.core.StringRedisTemplate;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.math.BigDecimal;
import java.util.*;

/**
 * <AUTHOR>
 * @date 2022/11/9
 */
@Service
public class PkGameService extends SlowTaskQueue {

    private static final Logger logger = LoggerFactory.getLogger(PkGameService.class);

    private static final int CREATE_PK_BEANS = 20; // 创建pk和房间PK的钻石费用
    private static final int PK_MAX_WAIT_TIME_PRO = 10 * 60; // pk最大等待时间(正式服)
    private static final int PK_MAX_WAIT_TIME_DEBUG = 60; // pk最大等待时间(测试服)

    private static final int CREATE_PK_ACT_TYPE = 61;
    private static final String CREATE_PK_TITLE = "create pk game";
    private static final String CREATE_PK_DESC = "create pk game";

    private static final int CREATE_ROOM_PK_ACT_TYPE = 62;
    private static final String CREATE_ROOM_PK_TITLE = "create room pk game";
    private static final String CREATE_ROOM_PK_DESC = "create room pk game";

    private static final int RETURN_PK_ACT_TYPE = 61;
    private static final String RETURN_CREATE_PK_TITLE = "return create pk game";
    private static final String RETURN_CREATE_ROOM_PK_TITLE = "return create room pk game";

    private static final int PK_DAILY_RANK = 24 * 3600;  // pk日榜
    private static final int PK_WEEKLY_RANK = 7 * 24 * 3600;  // pk周榜
    private static final int PK_MONTHLY_RANK = 30 * 24 * 3600;  // pk月榜

    private static final int LOOP_TIME = 60 * 60 * 1000; // 每小时刷新一下pk排行榜

    @Resource
    private ActorDao actorDao;
    @Resource
    private CommonConfig commonConfig;
    @Resource
    private PkGameDao pkGameDao;
    @Resource
    private RoomPkGameDao roomPkGameDao;
    @Resource
    private TurntableGameRedis turntableGameRedis;
    @Resource
    private TruthOrDareRedis truthOrDareRedis;
    @Resource
    private FingerGuessRedis fingerGuessRedis;
    @Resource(name = DataRedisBean.MAIN_CLUSTER)
    private StringRedisTemplate redisTemplate;
    @Resource
    private PkGameRedis pkGameRedis;
    @Resource
    private RoomPkGameRedis roomPkGameRedis;
    @Resource
    private MongoRoomDao roomDao;
    @Resource
    private DataCenterService dataCenterService;
    @Resource
    private GiftDao giftDao;
    @Resource
    private VipInfoDao vipInfoDao;
    @Resource
    private RoomWebSender roomWebSender;
    @Resource
    private PkGiftDao pkGiftDao;
    @Resource
    private RoomAdminRedis roomAdminRedis;
    @Resource
    private UserLevelDao userLevelDao;
    @Resource
    private SysConfigDao sysConfigDao;
    @Resource
    private FriendsListRedis friendsListRedis;
    @Resource
    private EventReport eventReport;
    @Resource
    private DailyTaskService dailyTaskService;
    @Resource
    private RoomMemberRedis roomMemberRedis;
    @Resource
    private MqSenderService mqSenderService;
    @Resource
    private UserOnlineRedis userOnlineRedis;
    @Resource
    private CommonTaskService commonTaskService;

    private final Map<Integer, List<PkRankUserInfo>> pkCacheMap = new HashMap<>(4);
    private final Map<Integer, List<RoomPkRankInfo>> roomPkCacheMap = new HashMap<>(4);

    @PostConstruct
    public void postInit() {
        // 结束pk和房间pk
        TimerService.getService().addDelay(new LoopTask(this, 1000) {
            @Override
            protected void execute() {
                onTick();
            }
        });
        // 刷新排行榜
        refreshPkRanking();
        TimerService.getService().addDelay(new LoopTask(this, LOOP_TIME) {
            @Override
            protected void execute() {
                refreshPkRanking();
            }
        });
    }

    public void onTick() {
        // 结束等待超时的pk和房间pk
        endWaitTimeoutPk();
        endWaitTimeoutRoomPk();
        // 结束正常结束的pk和房间pk
        endCompletedPk();
        endCompletedRoomPk();
    }

    /**
     * 检查房间内所有游戏状态，红包，转盘，pk，真心话
     */
    public RoomGameStatusVO checkRoomGameStatus(PkGameDTO reqDTO) {
        String roomId = reqDTO.getRoom_id();
        if (StringUtils.isEmpty(roomId)) {
            logger.error("room id can not be empty.");
            throw new GameException(HttpCode.PARAM_ERROR);
        }
        RoomGameStatusVO vo = new RoomGameStatusVO();
        PkGame pkGame = pkGameDao.getByRoomId(roomId);
        RoomPkGame roomPkGame = roomPkGameDao.findDataByRoomId(roomId);
        TurntableGameInfo turntable = turntableGameRedis.getGameInfoByRoomId(roomId);
        TruthOrDareInfo truthOrDare = truthOrDareRedis.getGameInfoByRoomId(roomId);
        int guessNum = fingerGuessRedis.getAllGuessNum();
        vo.setPk(pkGame != null ? 1 : 0);
        vo.setRoom_pk(roomPkGame != null ? 1 : 0);
        vo.setTurntable(turntable != null ? 1 : 0);
        vo.setTruth_dare(truthOrDare != null ? 1 : 0);
        vo.setLucky_box(0);
        vo.setLudo(getLudoStatus(roomId));
        vo.setBumper(0);
        vo.setFinger_guess(guessNum > 0 ? 1 : 0);
        vo.setGift_version(commonConfig.getConfigIntValue("gift_version"));
        vo.setGift_bag_version(commonConfig.getConfigIntValue("gift_bag_version"));
        if (reqDTO.getOs() == ClientOS.IOS) {
            int iosExamine = commonConfig.getConfigIntValue("ios_examine");
            if (iosExamine != 0 && reqDTO.getVersioncode() >= iosExamine) {
                vo.setLudo(0);
            }
        }
        return vo;
    }

    /**
     * 创建pk游戏
     */
    public CreatePkVO createPk(PkGameDTO reqDTO) {
        ActorData actorData = actorDao.getActorDataFromCache(reqDTO.getUid());
        if (actorData == null) {
            logger.error("the actor not found. uid={}", reqDTO.getUid());
            throw new GameException(GameHttpCode.THE_ACTOR_NOT_FOUND);
        }
        if (pkGameRedis.isInPkGame(reqDTO.getUid())) {
            logger.info("user have been in PK. uid={}", reqDTO.getUid());
            throw new GameException(GameHttpCode.HAVE_BEEN_IN_PK);
        }
        PkGame pkGame = pkGameDao.getByRoomId(reqDTO.getRoom_id());
        RoomPkGame roomPkGame = roomPkGameDao.findDataByRoomId(reqDTO.getRoom_id());
        if (pkGame != null || roomPkGame != null) {
            logger.info("There is PK ongoing now, please try later. roomId={}", reqDTO.getRoom_id());
            throw new GameException(GameHttpCode.PK_IS_ONGOING_NOW);
        }
        MongoRoomData roomData = roomDao.findData(reqDTO.getRoom_id());
        if (roomData == null) {
            logger.error("can not find room data. roomId={}", reqDTO.getRoom_id());
            throw new GameException(GameHttpCode.PARAM_ERROR);
        }
        if (roomData.getRoom_pk() == 2) {
            logger.info("the PK function has been disabled. roomId={}", reqDTO.getRoom_id());
            throw new GameException(GameHttpCode.PK_FUNCTION_DISABLED);
        }
        // 扣除创建pk费用
        deductCost(reqDTO.getUid(), reqDTO.getRoom_id(), CREATE_PK_ACT_TYPE, CREATE_PK_TITLE, CREATE_PK_DESC);
        int nowTime = DateHelper.getNowSeconds();
        pkGame = buildPkGameData(reqDTO, nowTime);
        pkGameDao.save(pkGame);
        PkGameInfo pkGameInfo = buildPkGameInfo(pkGame);
        pkGameRedis.savePkGameInfo(pkGame.get_id().toString(), pkGameInfo);
        if (pkGameInfo.getPk_type() == 0) {
            savePkHallInfo(pkGameInfo);
        }
        // 保存在pk的用户id
        pkGameRedis.saveUserInPkGame(reqDTO.getUid());
        int waitEndTime = nowTime + (ServerConfig.isNotProduct() ? PK_MAX_WAIT_TIME_DEBUG : PK_MAX_WAIT_TIME_PRO);
        pkGameRedis.addPkGameWaitingTime(pkGame.get_id().toString(), waitEndTime);
        sendRoomMicPkMessage(pkGameInfo, "create_pk");
        CreatePkVO vo = new CreatePkVO();
        BeanUtils.copyProperties(pkGameInfo, vo);
        return vo;
    }

    /**
     * 接受pk
     */
    public ReceivePkVO receivePk(PkGameDTO reqDTO) {
        PkGame pkGame = pkGameDao.getByGameIdAndStatus(reqDTO.getPk_game_id(), PkGameConstant.CREATE_STATUS);
        if (pkGame == null) {
            logger.error("game not exist. gameId={}", reqDTO.getPk_game_id());
            throw new GameException(GameHttpCode.THE_GAME_IS_NOT_FOUND);
        }
        pkGame.setReceiver(reqDTO.getUid());
        pkGameDao.save(pkGame);
        return new ReceivePkVO(reqDTO.getUid(), pkGame.getRoom_id());
    }

    /**
     * pk列表
     */
    public PageVO pkList() {
        Map<String, PkHallInfo> pkHallInfoMap = pkGameRedis.getPkHallInfoMap();
        if (CollectionUtils.isEmpty(pkHallInfoMap)) {
            return new PageVO(Collections.emptyList(), "");
        }
        List<PkHallInfo> list = new ArrayList<>();
        for (Map.Entry<String, PkHallInfo> entry : pkHallInfoMap.entrySet()) {
            list.add(entry.getValue());
        }
        return new PageVO(list, "");
    }

    /**
     * pk准备
     */
    public PkPrepareVO pkPrepare() {
        PkPrepareVO vo = new PkPrepareVO();
        vo.setStart_game_cost(CREATE_PK_BEANS);
        List<PkGiftVO> list = new ArrayList<>();
        PkGiftData pkGiftData = pkGiftDao.findValidData();
        if (pkGiftData == null) {
            vo.setList(list);
            return vo;
        }
        List<Integer> giftIdList = pkGiftData.getGift_id();
        if (!CollectionUtils.isEmpty(giftIdList)) {
            for (Integer giftId : giftIdList) {
                GiftData gift = giftDao.getGiftFromCache(giftId);
                if (gift == null) {
                    continue;
                }
                PkGiftVO giftVO = new PkGiftVO();
                giftVO.setIcon(gift.getGicon());
                giftVO.setPrice(gift.getPrice());
                giftVO.setGid(gift.getRid());
                list.add(giftVO);
            }
        }
        vo.setList(list);
        return vo;
    }

    /**
     * 邀请好友pk
     */
    public InviteFriendPkVO inviteFriendPk(PkGameDTO reqDTO) {
        if (StringUtils.isEmpty(reqDTO.getAid()) || StringUtils.isEmpty(reqDTO.getPk_game_id())) {
            logger.info("invite friend pk param error. uid={} aid={} pkGameId={} ", reqDTO.getUid(), reqDTO.getAid(), reqDTO.getPk_game_id());
            throw new GameException(GameHttpCode.PARAM_ERROR);
        }
        ActorData actorData = actorDao.getActorDataFromCache(reqDTO.getUid());
        if (actorData == null) {
            logger.error("can not find actor. uid={}", reqDTO.getUid());
            throw new GameException(GameHttpCode.PARAM_ERROR);
        }
        PkGame pkGame = pkGameDao.findData(reqDTO.getPk_game_id());
        if (pkGame == null) {
            logger.error("pk game not exist. pkGameId={}", reqDTO.getPk_game_id());
            throw new GameException(GameHttpCode.GAME_NOT_EXIST);
        }
        // 被邀请的好友不能是已经在玩pk的
        if (pkGameRedis.isInPkGame(reqDTO.getAid())) {
            logger.info("The invited is in PK now, please try later. pkGameId={} aid={}", reqDTO.getPk_game_id(), reqDTO.getAid());
            throw new GameException(GameHttpCode.THE_INVITED_IS_IN_PK);
        }
        // 添加同一局游戏不能重复邀请好友
        if (pkGameRedis.hasBeenInvited(reqDTO.getPk_game_id(), reqDTO.getAid())) {
            logger.info("You have invited him. uid={} aid={} pkGameId={} ", reqDTO.getUid(), reqDTO.getAid(), reqDTO.getPk_game_id());
            throw new GameException(GameHttpCode.HAVE_INVITED_HIM);
        }
        pkGameRedis.addInvitedUser(reqDTO.getPk_game_id(), reqDTO.getAid());
        PkGameInfo pkGameInfo = pkGameRedis.getPkGameInfo(reqDTO.getPk_game_id());
        if (CollectionUtils.isEmpty(pkGameInfo.getInvite_uid())) {
            pkGameInfo.setInvite_uid(Collections.singletonList(reqDTO.getAid()));
        } else {
            pkGameInfo.getInvite_uid().add(reqDTO.getAid());
        }
        pkGameRedis.savePkGameInfo(reqDTO.getPk_game_id(), pkGameInfo);
        int vipLevel = vipInfoDao.getIntVipLevelFromCache(reqDTO.getUid());
        sendRoomMicPkMessage(pkGameInfo, "check_pk");
        sendRoomMicPkInviteMsg(actorData, pkGameInfo, reqDTO.getAid(), vipLevel);
        InviteFriendPkVO vo = new InviteFriendPkVO();
        vo.setIcon(pkGameInfo.getGicon());
        vo.setPk_time(pkGame.getTime());
        vo.setName(actorData.getName());
        vo.setHead(ImageUrlGenerator.generateRoomUserUrl(actorData.getHead(), vipLevel));
        vo.setRoom_id(pkGame.getRoom_id());
        vo.setPk_game_id(reqDTO.getPk_game_id());
        return vo;
    }

    /**
     * 取消pk游戏
     */
    public void cancelPk(PkGameDTO reqDTO) {
        ActorData actorData = actorDao.getActorDataFromCache(reqDTO.getUid());
        if (actorData == null) {
            logger.error("can not find actor. uid={}", reqDTO.getUid());
            throw new GameException(GameHttpCode.PARAM_ERROR);
        }
        PkGame pkGame = pkGameDao.findData(reqDTO.getPk_game_id());
        if (pkGame == null || pkGame.getStatus() == PkGameConstant.CANCEL_STATUS || pkGame.getStatus() == PkGameConstant.END_STATUS) {
            logger.info("can not find pk game data. uid={} roomId={} pkGameId={}", reqDTO.getUid(), reqDTO.getRoom_id(), reqDTO.getPk_game_id());
            return;
        }
        if (pkGame.getStatus() == PkGameConstant.START_STATUS) {
            logger.info("this pk game is running. uid={} roomId={} pkGameId={}", reqDTO.getUid(), reqDTO.getRoom_id(), reqDTO.getPk_game_id());
            throw new GameException(GameHttpCode.PK_GAME_IS_RUNNING);
        }
        // 判断是否是房主或者是房间管理员或者是游戏创建者
        boolean isCreator = reqDTO.getUid().equals(pkGame.getCreator());
        boolean isRoomAdmin = roomAdminRedis.isRoomAdmin(reqDTO.getRoom_id(), reqDTO.getUid());
        if (!isCreator && !isRoomAdmin && !RoomUtils.isHomeowner(reqDTO.getUid(), reqDTO.getRoom_id())) {
            logger.info("only room owner or room admin or game creator can cancel pk game. roomId={} uid={} pkGameId={}", reqDTO.getRoomId(), reqDTO.getUid(), reqDTO.getPk_game_id());
            throw new GameException(GameHttpCode.CAN_NOT_CANCEL_PK_GAME);
        }
        // 退还创建pk游戏费用
        changeBeans(reqDTO.getUid(), reqDTO.getRoom_id(), RETURN_PK_ACT_TYPE, RETURN_CREATE_PK_TITLE);
        pkGame.setStatus(PkGameConstant.CANCEL_STATUS);
        pkGameDao.save(pkGame);
        PkGameInfo pkGameInfo = pkGameRedis.getPkGameInfo(reqDTO.getPk_game_id());
        pkGameInfo.setStatus(PkGameConstant.CANCEL_STATUS);
        pkGameRedis.savePkGameInfo(pkGame.get_id().toString(), pkGameInfo);
        if (pkGame.getType() == 0) {
            pkGameRedis.deletePkHallInfo(reqDTO.getPk_game_id());
        }
        pkGameRedis.removeUserInPkGame(pkGameInfo.getCreator_uid());
        sendRoomMicPkMessage(pkGameInfo, "cancel_pk");
        pkGameRedis.deletePkGameWaitingTime(reqDTO.getPk_game_id());
        pkGameRedis.deleteInvitedUser(reqDTO.getPk_game_id());
        // 上报数数
        doReportEvent(pkGame);
    }

    /**
     * 结束pk游戏
     */
    public void endPk(PkGameDTO reqDTO) {
        PkGame data = pkGameDao.findData(reqDTO.getPid());
        if (data == null) {
            logger.error("pk game not exist. pid={}", reqDTO.getPid());
            throw new GameException(GameHttpCode.PK_GAME_NOT_EXIST);
        }
        data.setStatus(PkGameConstant.END_STATUS);
        pkGameDao.save(data);
        // 上报数数
        doReportEvent(data);
    }

    /**
     * 加入pk游戏
     */
    public JoinPkVO joinPk(PkGameDTO reqDTO) {
        ActorData actorData = actorDao.getActorDataFromCache(reqDTO.getUid());
        if (actorData == null) {
            logger.error("can not find actor. uid={}", reqDTO.getUid());
            throw new GameException(GameHttpCode.PARAM_ERROR);
        }
        if (pkGameRedis.isInPkGame(reqDTO.getUid())) {
            logger.info("You have been in PK, you can't start or join new PK now. uid={} pkGameId={}", reqDTO.getUid(), reqDTO.getPk_game_id());
            throw new GameException(GameHttpCode.HAVE_BEEN_IN_PK);
        }
        PkGame pkGame = pkGameDao.findData(reqDTO.getPk_game_id());
        if (pkGame == null) {
            logger.error("pk game not exist. uid={} pkGameId={}", reqDTO.getUid(), reqDTO.getPk_game_id());
            throw new GameException(GameHttpCode.PK_GAME_NOT_EXIST);
        }
        if (pkGame.getStatus() == PkGameConstant.START_STATUS) {
            logger.info("the PK has been started, you cannot join now. uid={} pkGameId={}", reqDTO.getUid(), reqDTO.getPk_game_id());
            throw new GameException(GameHttpCode.PK_GAME_HAS_BEEN_STARTED);
        }
        if (pkGame.getStatus() != PkGameConstant.CREATE_STATUS) {
            logger.error("pk game not exist. uid={} pkGameId={}", reqDTO.getUid(), reqDTO.getPk_game_id());
            throw new GameException(GameHttpCode.PK_GAME_NOT_EXIST);
        }
        int nowTime = DateHelper.getNowSeconds();
        int endTime = nowTime + pkGame.getTime();
        pkGame.setStatus(PkGameConstant.START_STATUS);
        pkGame.setReceiver(reqDTO.getUid());
        pkGame.setStart_time(nowTime);
        pkGame.setEnd_time(endTime);
        pkGameDao.save(pkGame);
        PkGameInfo pkGameInfo = pkGameRedis.getPkGameInfo(reqDTO.getPk_game_id());
        pkGameInfo.setStatus(PkGameConstant.START_STATUS);
        pkGameInfo.setBlue_info(getPkInfoObject(reqDTO.getUid()));
        pkGameRedis.savePkGameInfo(pkGame.get_id().toString(), pkGameInfo);
        pkGameRedis.deletePkGameWaitingTime(reqDTO.getPk_game_id());
        pkGameRedis.addPkGameRunningTime(reqDTO.getPk_game_id(), endTime);
        // 保存在pk的用户id
        pkGameRedis.saveUserInPkGame(reqDTO.getUid());
        savePkHallInfo(pkGameInfo);
        sendRoomMicPkMessage(pkGameInfo, "start_pk");
        // 发送房间pk开始消息
        notifyFriendAndMember(pkGame.getCreator(), pkGame.getRoom_id(), 1);
        notifyFriendAndMember(pkGame.getReceiver(), pkGame.getRoom_id(), 1);
        dailyTaskService.sendToMq(new DailyTaskMqData(pkGame.getCreator(), 11, DateHelper.ARABIAN.formatDateInDay2(), 1));
        dailyTaskService.sendToMq(new DailyTaskMqData(pkGame.getReceiver(), 11, DateHelper.ARABIAN.formatDateInDay2(), 1));
        JoinPkVO vo = new JoinPkVO();
        BeanUtils.copyProperties(pkGameInfo, vo);
        return vo;
    }

    /**
     * 加入房间时检查是否存在pk游戏
     */
    public CheckPkVO checkPk(PkGameDTO reqDTO) {
        PkGame pkGame = null;
        if (!StringUtils.isEmpty(reqDTO.getRoom_id())) {
            pkGame = pkGameDao.getByRoomId(reqDTO.getRoom_id());
        } else if (!StringUtils.isEmpty(reqDTO.getPid())) {
            pkGame = pkGameDao.findData(reqDTO.getPid());
        }
        if (pkGame == null) {
            logger.info("pk game not exist. uid={} pid={}", reqDTO.getUid(), reqDTO.getPid());
            throw new GameException(new HttpCode(1, ""));
        }
        PkGameInfo pkGameInfo = pkGameRedis.getPkGameInfo(pkGame.get_id().toString());
        if (pkGameInfo == null) {
            logger.info("pk game not exist. uid={} pid={}", reqDTO.getUid(), reqDTO.getPid());
            throw new GameException(new HttpCode(1, ""));
        }
        CheckPkVO vo = new CheckPkVO();
        BeanUtils.copyProperties(pkGameInfo, vo);
        if (pkGame.getStatus() == PkGameConstant.START_STATUS) {
            vo.setLeft_time(Math.max(pkGame.getEnd_time() - DateHelper.getNowSeconds(), 0));
        }
        return vo;
    }

    /**
     * 获取pk排行榜
     */
    public PageVO pkRanking(PkGameDTO reqDTO) {
        List<PkRankUserInfo> list;
        switch (reqDTO.getOpt_type()) {
            case 1:
                list = pkCacheMap.get(PK_DAILY_RANK);
                break;
            case 2:
                list = pkCacheMap.get(PK_WEEKLY_RANK);
                break;
            case 3:
                list = pkCacheMap.get(PK_MONTHLY_RANK);
                break;
            default:
                logger.error("get pk ranking param error. optType={}", reqDTO.getOpt_type());
                throw new GameException(GameHttpCode.PARAM_ERROR);
        }
        logger.info("list.size={}", CollectionUtils.isEmpty(list) ? 0 : list.size());
        return new PageVO(CollectionUtils.isEmpty(list) ? Collections.emptyList() : list);
    }

    /**
     * 刷新pk排行榜
     */
    public void refreshPkRanking() {
        long timeMillis = System.currentTimeMillis();
        logger.info("refreshPkRanking start.");
        // 刷新pk和房间pk日榜
        makePkRankList(PK_DAILY_RANK);
        makeRoomPkRankList(PK_DAILY_RANK);
        // 刷新pk和房间pk周榜
        makePkRankList(PK_WEEKLY_RANK);
        makeRoomPkRankList(PK_WEEKLY_RANK);
        // 刷新pk和房间pk月榜
        makePkRankList(PK_MONTHLY_RANK);
        makeRoomPkRankList(PK_MONTHLY_RANK);
        logger.info("refreshPkRanking start. cost={}", System.currentTimeMillis() - timeMillis);
    }

    public RoomPkVO createRoomPk(PkGameDTO reqDTO) {
        ActorData actorData = actorDao.getActorDataFromCache(reqDTO.getUid());
        if (actorData == null) {
            logger.error("the actor not found. uid={}", reqDTO.getUid());
            throw new GameException(GameHttpCode.THE_ACTOR_NOT_FOUND);
        }
        if (pkGameRedis.isInPkGame(reqDTO.getUid())) {
            logger.info("You have been in PK, you can't start or join new PK now. uid={} pkGameId={}", reqDTO.getUid(), reqDTO.getPk_game_id());
            throw new GameException(GameHttpCode.HAVE_BEEN_IN_PK);
        }
        RoomPkGame roomPkGame = roomPkGameDao.findDataByRoomId(reqDTO.getRoom_id());
        PkGame pkGame = pkGameDao.getByRoomId(reqDTO.getRoom_id());
        if (pkGame != null || roomPkGame != null) {
            logger.info("There is PK ongoing now, please try later. roomId={}", reqDTO.getRoom_id());
            throw new GameException(GameHttpCode.PK_IS_ONGOING_NOW);
        }
        MongoRoomData roomData = roomDao.findData(reqDTO.getRoom_id());
        if (roomData == null) {
            logger.error("can not find room data. roomId={}", reqDTO.getRoom_id());
            throw new GameException(GameHttpCode.PARAM_ERROR);
        }
        if (roomData.getRoom_pk() == 2) {
            logger.error("the PK function has been disabled. roomId={}", reqDTO.getRoom_id());
            throw new GameException(GameHttpCode.PK_FUNCTION_DISABLED);
        }
        // 扣除创建房间pk费用
        deductCost(reqDTO.getUid(), reqDTO.getRoom_id(), CREATE_ROOM_PK_ACT_TYPE, CREATE_ROOM_PK_TITLE, CREATE_ROOM_PK_DESC);
        int nowTime = DateHelper.getNowSeconds();
        roomPkGame = buildRoomPkGameData(reqDTO, nowTime);
        roomPkGameDao.save(roomPkGame);
        String gameId = roomPkGame.get_id().toString();
        int waitEndTime = nowTime + (ServerConfig.isNotProduct() ? PK_MAX_WAIT_TIME_DEBUG : PK_MAX_WAIT_TIME_PRO);
        roomPkGameRedis.addRoomPkWaitEndTime(gameId, waitEndTime);
        RoomPkGameInfo gameInfo = saveRoomPkGameInfo(roomPkGame);
        if (gameInfo.getPk_type() == 0) {
            // 去pk大厅展示
            saveRoomPkHallInfo(gameInfo);
            sendRoomPkNotifyFriendsMsg(gameId, roomData.getName());
        }
        pkGameRedis.saveUserInPkGame(reqDTO.getUid());
        sendRoomRoomPkMsg(gameInfo, null, "room_create_pk");
        RoomPkVO vo = new RoomPkVO();
        BeanUtils.copyProperties(gameInfo, vo);
        return vo;
    }

    /**
     * 房间pk大厅(展示所有竞赛，除了邀请好友在等待好友回应的pk游戏)
     */
    public PageVO roomPkList() {
        Map<String, RoomPkHallInfo> pkHallInfoMap = roomPkGameRedis.getPkHallInfoMap();
        if (CollectionUtils.isEmpty(pkHallInfoMap)) {
            return new PageVO(Collections.emptyList(), "");
        }
        List<RoomPkHallInfo> list = new ArrayList<>();
        for (Map.Entry<String, RoomPkHallInfo> entry : pkHallInfoMap.entrySet()) {
            list.add(entry.getValue());
        }
        return new PageVO(list, "");
    }

    /**
     * 邀请好友房间pk
     */
    public InviteFriendRoomPkVO inviteFriendRoomPk(PkGameDTO reqDTO) {
        ActorData actorData = actorDao.getActorDataFromCache(reqDTO.getUid());
        if (actorData == null) {
            logger.error("the actor not found. uid={}", reqDTO.getUid());
            throw new GameException(GameHttpCode.THE_ACTOR_NOT_FOUND);
        }
        RoomPkGame roomPkGame = roomPkGameDao.findDataByGameId(reqDTO.getPid());
        if (roomPkGame == null) {
            logger.info("game not exist. pid={}", reqDTO.getPid());
            throw new GameException(GameHttpCode.PK_GAME_NOT_EXIST);
        }
        // 校验对方是否已经在PK游戏
        if (pkGameRedis.isInPkGame(reqDTO.getAid())) {
            logger.info("The invited is in PK now, please try later. uid={} aid={}", reqDTO.getUid(), reqDTO.getAid());
            throw new GameException(GameHttpCode.THE_INVITED_IS_IN_PK);
        }
        String inviteRoomId = RoomUtils.formatRoomId(reqDTO.getAid());
        // 添加同一局游戏不能重复邀请房主进行pk
        if (roomPkGameRedis.hasBeenInvited(reqDTO.getPid(), inviteRoomId)) {
            logger.info("You have invited him. uid={} aid={} ", reqDTO.getUid(), reqDTO.getAid());
            throw new GameException(GameHttpCode.HAVE_INVITED_HIM);
        }
        roomPkGameRedis.addInvitedUser(reqDTO.getPid(), inviteRoomId);
        RoomPkGameInfo gameInfo = roomPkGameRedis.getPkGameInfo(reqDTO.getPid());
        sendRoomRoomPkMsg(gameInfo, null, "room_check_pk");
        int vipLevel = vipInfoDao.getIntVipLevelFromCache(actorData.getUid());
        // 发送房间PK邀请消息
        sendRoomPkInviteMsg(reqDTO, actorData, roomPkGame.getTime(), vipLevel);
        return new InviteFriendRoomPkVO(
                reqDTO.getRoom_id(),
                reqDTO.getPid(),
                actorData.getName(),
                ImageUrlGenerator.generateRoomUserUrl(actorData.getHead(), vipLevel),
                roomPkGame.getTime());
    }

    /**
     * 取消房间pk
     */
    public void cancelRoomPk(PkGameDTO reqDTO) {
        ActorData actorData = actorDao.getActorDataFromCache(reqDTO.getUid());
        if (actorData == null) {
            logger.error("the actor not found. uid={}", reqDTO.getUid());
            throw new GameException(GameHttpCode.THE_ACTOR_NOT_FOUND);
        }
        RoomPkGame roomPkGame = roomPkGameDao.findDataByGameId(reqDTO.getPid());
        if (roomPkGame == null || roomPkGame.getStatus() == PkGameConstant.CANCEL_STATUS || roomPkGame.getStatus() == PkGameConstant.END_STATUS) {
            logger.info("game not exist. pid={}", reqDTO.getPid());
            return;
        }
        if (roomPkGame.getStatus() == PkGameConstant.START_STATUS) {
            logger.info("this pk game is running. pid={}", reqDTO.getPid());
            throw new GameException(GameHttpCode.PK_GAME_IS_RUNNING);
        }
        // 房主才可以取消房间pk
        if (!RoomUtils.isHomeowner(reqDTO.getUid(), reqDTO.getRoom_id())) {
            logger.info("only room owner can cancel pk game. uid={} roomId={}", reqDTO.getPid(), reqDTO.getRoom_id());
            throw new GameException(GameHttpCode.ONLY_ROOM_OWNER_CAN_CANCEL_PK_GAME);
        }
        changeBeans(reqDTO.getUid(), reqDTO.getRoom_id(), CREATE_ROOM_PK_ACT_TYPE, RETURN_CREATE_ROOM_PK_TITLE);
        roomPkGame.setStatus(PkGameConstant.CANCEL_STATUS);
        roomPkGameDao.save(roomPkGame);
        if (roomPkGame.getType() == 0) {
            roomPkGameRedis.deleteRoomPkHallInfo(reqDTO.getPid());
        }
        RoomPkGameInfo gameInfo = roomPkGameRedis.getPkGameInfo(reqDTO.getPid());
        gameInfo.setStatus(PkGameConstant.CANCEL_STATUS);
        roomPkGameRedis.savePkGameInfo(reqDTO.getPid(), gameInfo);
        roomPkGameRedis.removeRoomPkWaitEndTime(reqDTO.getPid());
        pkGameRedis.deleteInvitedUser(reqDTO.getPid());
        pkGameRedis.removeUserInPkGame(RoomUtils.getRoomHostId(roomPkGame.getCreator()));
        sendRoomRoomPkMsg(gameInfo, actorData, "room_cancel_pk");
        // 上报数数
        doReportEvent(roomPkGame);
    }

    /**
     * 搜索房间好友
     */
    public PageVO searchRoomFriend(PkGameDTO reqDTO) {
        if (reqDTO.getKey() == null || StringUtils.isEmpty(reqDTO.getKey().trim())) {
            logger.info("Please enter user ID. uid={} key={}", reqDTO.getUid(), reqDTO.getKey());
            throw new GameException(GameHttpCode.PLEASE_ENTER_USER_ID);
        }
        ActorData actorData;
        try {
            int intKey = Integer.parseInt(reqDTO.getKey());
            actorData = actorDao.getActorByRid(intKey);
        } catch (NumberFormatException e) {
            // 字符串搜索
            actorData = actorDao.getActorByStrRid(reqDTO.getKey());
        }
        if (null == actorData || actorData.getValid() != 1) {
            logger.info("User not existing. uid={} key={}", reqDTO.getUid(), reqDTO.getKey());
            throw new GameException(GameHttpCode.USER_NOT_EXISTING);
        }
        // 校验是否是好友
        if (!friendsListRedis.isFriend(reqDTO.getUid(), actorData.getUid())) {
            logger.info("This is not your friend. uid={} aid={}", reqDTO.getUid(), actorData.getUid());
            throw new GameException(GameHttpCode.THIS_IS_NOT_YOUR_FRIEND);
        }
        // 校验该用户是否已创建房间
        MongoRoomData roomData = roomDao.getDataFromCache(RoomUtils.formatRoomId(actorData.getUid()));
        if (roomData == null) {
            logger.info("Your friend hasn't created room yet. aid={}", actorData.getUid());
            throw new GameException(GameHttpCode.YOUR_FRIEND_HAS_NOT_CREATED_ROOM);
        }
        RoomFriendVO vo = new RoomFriendVO();
        vo.setAid(actorData.getUid());
        vo.setName(roomData.getName());
        vo.setHead(roomData.getHead());
        vo.setAnnounce(roomData.getAnnounce());
        vo.setCountry(roomData.getCountry());
        vo.setPwd(!StringUtils.isEmpty(roomData.getPwd()) ? 1 : 0);
        vo.setVip_level(vipInfoDao.getIntVipLevelFromCache(actorData.getUid()));
        return new PageVO(Collections.singletonList(vo));
    }

    /**
     * 展示在线好友房间
     */
    public PageVO onlineFriendRoomList(PkGameDTO reqDTO) {
        Set<String> friendSet = friendsListRedis.getFriendList(reqDTO.getUid());
        List<OnlineFriendRoomVO> list = new ArrayList<>();
        if (!CollectionUtils.isEmpty(friendSet)) {
            Set<String> allOnlineSet = getAllOnlineSet();
            for (String aid : friendSet) {
                if (!allOnlineSet.contains(aid)) {
                    continue;
                }
                String roomId = RoomUtils.formatRoomId(aid);
                MongoRoomData roomData = roomDao.getDataFromCache(roomId);
                if (roomData == null) {
                    continue;
                }
                OnlineFriendRoomVO vo = new OnlineFriendRoomVO();
                vo.setAid(aid);
                vo.setCountry(roomData.getCountry());
                vo.setName(roomData.getName());
                vo.setAnnounce(roomData.getAnnounce());
                vo.setVip_level(vipInfoDao.getIntVipLevelFromCache(aid));
                vo.setHead(ImageUrlGenerator.generateRoomUserUrl(roomData.getHead(), vo.getVip_level()));
                vo.setPwd(!StringUtils.isEmpty(roomData.getPwd()) ? 1 : 0);
                list.add(vo);
            }
        }
        return new PageVO(list, "");
    }

    @Cacheable(value = "getAllOnlineSet", cacheManager = CaffeineCacheConfig.EXPIRE_15S_AFTER_WRITE)
    public Set<String> getAllOnlineSet() {
        return userOnlineRedis.getAllUserOnline();
    }

    /**
     * 加入房间时检查是否存在房间pk游戏
     */
    public RoomPkVO checkRoomPk(PkGameDTO reqDTO) {
        RoomPkGame roomPkGame = null;
        if (!StringUtils.isEmpty(reqDTO.getRoom_id())) {
            roomPkGame = roomPkGameDao.findDataByRoomId(reqDTO.getRoom_id());
        } else if (!StringUtils.isEmpty(reqDTO.getPid())) {
            roomPkGame = roomPkGameDao.findDataByGameId(reqDTO.getPid());
        }
        if (roomPkGame == null) {
            logger.info("room pk game not exist. roomId={} pid={}", reqDTO.getRoom_id(), reqDTO.getPid());
            throw new GameException(new HttpCode(1, ""));
        }
        String pkGameId = roomPkGame.get_id().toString();
        RoomPkGameInfo pkGameInfo = roomPkGameRedis.getPkGameInfo(pkGameId);
        if (pkGameInfo == null) {
            logger.info("room pk game not exist. roomId={} pid={}", reqDTO.getRoom_id(), reqDTO.getPid());
            throw new GameException(new HttpCode(1, ""));
        }
        RoomPkVO vo = new RoomPkVO();
        BeanUtils.copyProperties(pkGameInfo, vo);
        if (roomPkGame.getStatus() == PkGameConstant.START_STATUS) {
            vo.setLeft_time(Math.max(roomPkGame.getEnd_time() - DateHelper.getNowSeconds(), 0));
        }
        return vo;
    }

    /**
     * 接受房间pk
     */
    public RoomPkVO acceptRoomPk(PkGameDTO reqDTO) {
        String roomId = RoomUtils.formatRoomId(reqDTO.getUid());
        MongoRoomData roomData = roomDao.getDataFromCache(roomId);
        if (roomData == null) {
            logger.info("You need to create room first. roomId={}", roomId);
            throw new GameException(GameHttpCode.YOU_NEED_TO_CREATE_ROOM_FIRST);
        }
        // 校验创建房间和接受pk的房间是否已经在PK中
        if (pkGameRedis.isInPkGame(reqDTO.getUid()) || roomPkGameRedis.roomInPk(roomId)) {
            logger.info("You have been in PK, you can't start or join new PK now. uid={} pkGameId={}", reqDTO.getUid(), reqDTO.getPid());
            throw new GameException(GameHttpCode.HAVE_BEEN_IN_PK);
        }
        RoomPkGame roomPkGame = roomPkGameDao.findDataByGameId(reqDTO.getPid());
        // 校验pk是否在等待中
        if (roomPkGame == null || roomPkGame.getStatus() != 0) {
            logger.info("This PK has been started or cancelled. pid={}", reqDTO.getPid());
            throw new GameException(GameHttpCode.PK_HAS_BEEN_STARTED_OR_CANCELLED);
        }
        int nowTime = DateHelper.getNowSeconds();
        // 更新mongo房间pk数据
        roomPkGame.setStatus(PkGameConstant.START_STATUS);
        roomPkGame.setReceiver(roomId);
        roomPkGame.setStart_time(nowTime);
        roomPkGame.setEnd_time(nowTime + roomPkGame.getTime());
        roomPkGameDao.save(roomPkGame);
        // 保存房间pk结束时间
        roomPkGameRedis.addRoomPkEndTime(reqDTO.getPid(), roomPkGame.getEnd_time());
        // 保存在房间pk的房间id
        roomPkGameRedis.addInPkRoom(roomPkGame.getCreator());
        roomPkGameRedis.addInPkRoom(roomPkGame.getReceiver());
        // 保存在pk的用户id
        pkGameRedis.saveUserInPkGame(reqDTO.getUid());
        // 更新RoomPkInfo
        RoomPkGameInfo gameInfo = roomPkGameRedis.getPkGameInfo(reqDTO.getPid());
        gameInfo.setStatus(PkGameConstant.START_STATUS);
        gameInfo.setChallenger_room_id(roomId);
        gameInfo.setChallenger_info(getRoomPkInfoObject(roomId));
        gameInfo.setLeft_time(roomPkGame.getTime());
        roomPkGameRedis.savePkGameInfo(reqDTO.getPid(), gameInfo);
        saveRoomPkHallInfo(gameInfo);
        // 删除房间pk的等待时间
        roomPkGameRedis.removeRoomPkWaitEndTime(reqDTO.getPid());
        // 发送房间pk开始消息
        notifyFriendAndMember(RoomUtils.getRoomHostId(roomPkGame.getCreator()), roomPkGame.getCreator(), 2);
        notifyFriendAndMember(RoomUtils.getRoomHostId(roomPkGame.getReceiver()), roomPkGame.getReceiver(), 2);
        sendRoomRoomPkMsg(gameInfo, null, "room_start_pk");
        // 创建者和接受pk者完成每日任务
        String creator = RoomUtils.getRoomHostId(roomPkGame.getCreator());
        dailyTaskService.sendToMq(new DailyTaskMqData(creator, 11, DateHelper.ARABIAN.formatDateInDay2(), 1));
        dailyTaskService.sendToMq(new DailyTaskMqData(reqDTO.getUid(), 11, DateHelper.ARABIAN.formatDateInDay2(), 1));
        RoomPkVO vo = new RoomPkVO();
        BeanUtils.copyProperties(gameInfo, vo);
        return vo;
    }

    public PageVO roomPkRanking(PkGameDTO reqDTO) {
        List<RoomPkRankInfo> list;
        switch (reqDTO.getOpt_type()) {
            case 1:
                list = roomPkCacheMap.get(PK_DAILY_RANK);
                break;
            case 2:
                list = roomPkCacheMap.get(PK_WEEKLY_RANK);
                break;
            case 3:
                list = roomPkCacheMap.get(PK_MONTHLY_RANK);
                break;
            default:
                logger.error("get room pk ranking param error. optType={}", reqDTO.getOpt_type());
                throw new GameException(GameHttpCode.PARAM_ERROR);
        }
        return new PageVO(CollectionUtils.isEmpty(list) ? Collections.emptyList() : list);
    }

    private int getLudoStatus(String roomId) {
        String key = "str:room_sud_game_" + roomId;
        // 值为gameId-gameType
        String gameId = redisTemplate.opsForValue().get(key);
        logger.info("gameId={}", gameId);
        if (StringUtils.isEmpty(gameId)) {
            return 0;
        }
        String[] split = gameId.split("-");
        if (split.length == 2 && Integer.parseInt(split[1]) == SudGameConstant.LUDO_GAME) {
            return 1;
        }
        return 0;
    }

    private PkGame buildPkGameData(PkGameDTO reqDTO, int nowTime) {
        PkGame pkGame = new PkGame();
        pkGame.setCreator(reqDTO.getUid());
        pkGame.setRoom_id(reqDTO.getRoom_id());
        pkGame.setGift_id(reqDTO.getPk_gid());
        pkGame.setTime(reqDTO.getPk_time() * 60);
        pkGame.setType(reqDTO.getPk_type());
        pkGame.setCreate_time(nowTime);
        pkGame.setStatus(PkGameConstant.CREATE_STATUS);
        return pkGame;
    }

    private void sendRoomMicPkMessage(PkGameInfo pkGameInfo, String topic) {
        RoomMicPKMessage msg = new RoomMicPKMessage();
        BeanUtils.copyProperties(pkGameInfo, msg);
        msg.setRoomId(pkGameInfo.getRoom_id());
        msg.setTopic(topic);
        roomWebSender.sendRoomWebMsg(pkGameInfo.getRoom_id(), "", msg, true);
    }

    private PKInfoObject getPkInfoObject(String uid) {
        PKInfoObject infoObject = new PKInfoObject();
        infoObject.setUser_info(getPkUserInfoObject(uid));
        infoObject.setTop_list_info(Collections.emptyList());
        return infoObject;
    }

    private PKUserInfoObject getPkUserInfoObject(String uid) {
        ActorData actorData = actorDao.getActorDataFromCache(uid);
        PKUserInfoObject userInfoObject = new PKUserInfoObject();
        userInfoObject.setRid(actorData != null ? actorData.getRid() + "" : "");
        userInfoObject.setRidInfo(actorData != null ? new RidInfoObject(actorData.getRidData()) : new RidInfoObject());
        userInfoObject.setUid(actorData != null ? actorData.getUid() : "");
        userInfoObject.setName(actorData != null ? actorData.getName() : "");
        userInfoObject.setHead(actorData != null ? ImageUrlGenerator.generateRoomUserUrl(actorData.getHead()) : "");
        userInfoObject.setVip(actorData != null ? vipInfoDao.getIntVipLevelFromCache(actorData.getUid()) : 0);
        return userInfoObject;
    }

    private PKInfoObject getRoomPkInfoObject(String roomId) {
        PKInfoObject infoObject = new PKInfoObject();
        infoObject.setTop_list_info(Collections.emptyList());
        infoObject.setUser_info(getRoomPkUserInfoObject(roomId));
        return infoObject;
    }


    /**
     * 发送房间PK邀请消息
     */
    private void sendRoomPkInviteMsg(PkGameDTO reqDTO, ActorData actorData, int time, int vipLevel) {
        BaseTaskFactory.getFactory().addSlow(new Task() {
            @Override
            protected void execute() {
                RoomPKInviteMsg msg = new RoomPKInviteMsg();
                msg.setRoom_id(reqDTO.getRoom_id());
                msg.setPid(reqDTO.getPid());
                msg.setName(actorData.getName());
                msg.setHead(ImageUrlGenerator.generateRoomUserUrl(actorData.getHead(), vipLevel));
                msg.setTotal_time(time);
                roomWebSender.sendPlayerWebMsg("", actorData.getUid(), reqDTO.getAid(), msg, true);
            }
        });
    }

    private PKUserInfoObject getRoomPkUserInfoObject(String roomId) {
        MongoRoomData roomData = roomDao.findData(roomId);
        String uid = !StringUtils.isEmpty(roomId) ? RoomUtils.getRoomHostId(roomId) : "";
        PKUserInfoObject userInfoObject = new PKUserInfoObject();
        userInfoObject.setRid(roomData != null ? getOwnerId(roomData, roomId) : "");
        userInfoObject.setRidInfo(getOwnerRidInfo(roomId));
        userInfoObject.setUid(roomData != null ? uid : "");
        userInfoObject.setName(roomData != null ? roomData.getName() : "");
        userInfoObject.setHead(roomData != null ? roomData.getHead() : "");
        userInfoObject.setVip(roomData != null ? vipInfoDao.getIntVipLevelFromCache(uid) : 0);
        return userInfoObject;
    }

    private String getOwnerId(MongoRoomData roomData, String roomId) {
        if (StringUtils.isEmpty(roomId)) {
            return "";
        }
        if (roomData.getOwnerRid() != 0) {
            return roomData.getOwnerRid() + "";
        }
        ActorData actorData = actorDao.getActorDataFromCache(RoomUtils.getRoomHostId(roomId));
        return actorData != null ? actorData.getRid() + "" : "";
    }

    private RidInfoObject getOwnerRidInfo(String roomId) {
        if (StringUtils.isEmpty(roomId)) {
            return new RidInfoObject();
        }
        ActorData actorData = actorDao.getActorDataFromCache(RoomUtils.getRoomHostId(roomId));
        return actorData != null ? new RidInfoObject(actorData.getRidData()) : new RidInfoObject();
    }

    /**
     * 保存PkHallInfo到redis中
     */
    private void savePkHallInfo(PkGameInfo pkGameInfo) {
        PkHallInfo pkHallInfo = new PkHallInfo();
        pkHallInfo.setPkGameId(pkGameInfo.getPid());
        pkHallInfo.setRoomId(pkGameInfo.getRoom_id());
        pkHallInfo.setStatus(pkGameInfo.getStatus());
        pkHallInfo.setPkTime(pkGameInfo.getTotal_time() < 600 ? String.format("0%d:00", pkGameInfo.getTotal_time() / 60) : String.format("%d:00", pkGameInfo.getTotal_time() / 60));
        pkHallInfo.setPkGiftIcon(pkGameInfo.getGicon());
        PKUserInfoObject creator = pkGameInfo.getRed_info().getUser_info();
        pkHallInfo.setPkCreatorInfo(new PkHallInfo.PkUserInfo(
                ImageUrlGenerator.generateRoomUserUrl(creator.getHead(), creator.getVip()),
                creator.getName(),
                creator.getVip()));
        PKUserInfoObject challenger = pkGameInfo.getBlue_info().getUser_info();
        if (!StringUtils.isEmpty(challenger.getUid())) {
            pkHallInfo.setPkChallengerInfo(new PkHallInfo.PkUserInfo(
                    ImageUrlGenerator.generateRoomUserUrl(challenger.getHead(), challenger.getVip()),
                    challenger.getName(),
                    challenger.getVip()));
        }
        pkGameRedis.savePkHallInfo(pkHallInfo);
    }

    private PkGameInfo buildPkGameInfo(PkGame pkGame) {
        PkGameInfo pkGameInfo = new PkGameInfo();
        pkGameInfo.setPid(pkGame.get_id().toString());
        pkGameInfo.setRoom_id(pkGame.getRoom_id());
        pkGameInfo.setPk_type(pkGame.getType());
        pkGameInfo.setTotal_time(pkGame.getTime());
        pkGameInfo.setLeft_time(pkGame.getTime());
        pkGameInfo.setStatus(pkGame.getStatus());
        pkGameInfo.setCreator_uid(pkGame.getCreator());
        pkGameInfo.setRed_info(getPkInfoObject(pkGame.getCreator()));
        pkGameInfo.setInvite_uid(Collections.emptyList());
        pkGameInfo.setBlue_info(getPkInfoObject(pkGame.getReceiver()));
        pkGameInfo.setWinner_info(getPkUserInfoObject(pkGame.getWinner()));
        GiftData giftData = giftDao.getGiftFromCache(pkGame.getGift_id());
        pkGameInfo.setGid(pkGame.getGift_id() + "");
        pkGameInfo.setGicon(giftData.getGicon());
        pkGameInfo.setGift_price(giftData.getPrice());
        pkGameInfo.setExtra_msg(new PKExtraInfoObject());
        return pkGameInfo;
    }

    /**
     * 退还游戏费用
     */
    private void changeBeans(String uid, String roomId, int actType, String title) {
        MoneyDetailReq moneyDetailReq = new MoneyDetailReq();
        moneyDetailReq.setRandomId();
        moneyDetailReq.setRoomId(roomId);
        moneyDetailReq.setUid(uid);
        moneyDetailReq.setAtype(actType);
        moneyDetailReq.setChanged(PkGameService.CREATE_PK_BEANS);
        moneyDetailReq.setTitle(title);
        moneyDetailReq.setDesc("");
        mqSenderService.asyncChargeDiamonds(moneyDetailReq);
    }

    /**
     * 扣除创建pk费用
     */
    private void deductCost(String uid, String roomId, int actType, String title, String desc) {
        MoneyDetailReq moneyDetailReq = new MoneyDetailReq();
        moneyDetailReq.setRandomId();
        moneyDetailReq.setRoomId(roomId);
        moneyDetailReq.setUid(uid);
        moneyDetailReq.setAtype(actType);
        moneyDetailReq.setChanged(-PkGameService.CREATE_PK_BEANS);
        moneyDetailReq.setTitle(title);
        moneyDetailReq.setDesc(desc);
        ApiResult<String> result = dataCenterService.reduceBeans(moneyDetailReq);
        if (result.isError()) {
            if (1 == result.getCode().getCode()) {
                throw new GameException(GameHttpCode.DIAMOND_NOT_ENOUGH);
            }
            logger.error("reduce beans error, msg={}", result.getCode().getMsg());
            throw new GameException(GameHttpCode.CREATE_PK_FAILED);
        }
    }

    private void sendRoomMicPkInviteMsg(ActorData actorData, PkGameInfo pkGameInfo, String aid, int vipLevel) {
        BaseTaskFactory.getFactory().addSlow(new Task() {
            @Override
            protected void execute() {
                RoomMicPKInviteMsg msg = new RoomMicPKInviteMsg();
                msg.setPid(pkGameInfo.getPid());
                msg.setGicon(pkGameInfo.getGicon());
                msg.setCreator_uid(pkGameInfo.getCreator_uid());
                msg.setRoomId(pkGameInfo.getRoom_id());
                msg.setRoom_id(pkGameInfo.getRoom_id());
                msg.setName(actorData.getName());
                msg.setHead(ImageUrlGenerator.generateRoomUserUrl(actorData.getHead(), vipLevel));
                msg.setVip(vipLevel);
                msg.setTotal_time(pkGameInfo.getTotal_time());
                msg.setMsg_id(UUID.randomUUID().toString().replace("-", ""));
                msg.setIdentify(vipLevel > 0 ? 1 : 0);
                roomWebSender.sendPlayerWebMsg("", actorData.getUid(), aid, msg, true);
            }
        });
    }

    private PkRankUserInfo buildPkRankUserInfo(ActorData actorData, long totalCostBeans) {
        PkRankUserInfo userInfo = new PkRankUserInfo();
        userInfo.setName(actorData.getName());
        userInfo.setDesc(actorData.getDesc());
        userInfo.setGender(actorData.getFb_gender());
        userInfo.setAge(actorData.getAge());
        userInfo.setCountry(actorData.getCountry());
        userInfo.setRid(actorData.getRid());
        userInfo.setOs(actorData.getIntOs());
        userInfo.setValid(actorData.getValid());
        userInfo.setUid(actorData.getUid());
        userInfo.setViplevel(vipInfoDao.getIntVipLevel(actorData.getUid()));
        userInfo.setHead(ImageUrlGenerator.generateRoomUserUrl(actorData.getHead(), userInfo.getViplevel()));
        userInfo.setIdentify(userInfo.getViplevel() > 0 ? 1 : 0);
        userInfo.setUlvl(userLevelDao.getUserLevel(actorData.getUid()));
        userInfo.setTotal(formatDevotes(totalCostBeans));
        return userInfo;
    }

    /**
     * 格式化贡献数值
     */
    private String formatDevotes(long devotes) {
        if (devotes >= 1000000) {
            return new BigDecimal(devotes / 1000000f + "").setScale(1, BigDecimal.ROUND_HALF_UP) + "M";
        } else if (devotes >= 1000) {
            return new BigDecimal(devotes / 1000f + "").setScale(1, BigDecimal.ROUND_HALF_UP) + "K";
        } else {
            return devotes + "";
        }
    }

    private RoomPkGameInfo saveRoomPkGameInfo(RoomPkGame roomPkGame) {
        RoomPkGameInfo gameInfo = new RoomPkGameInfo();
        gameInfo.setPid(roomPkGame.get_id().toString());
        gameInfo.setCreator_room_id(roomPkGame.getCreator());
        gameInfo.setChallenger_room_id(roomPkGame.getReceiver());
        gameInfo.setOpt_time(DateHelper.getNowSeconds());
        gameInfo.setPk_type(roomPkGame.getType());
        gameInfo.setStatus(roomPkGame.getStatus());
        gameInfo.setCreator_num(roomPkGame.getCreator_count());
        gameInfo.setChallenger_num(roomPkGame.getReceiver_count());
        gameInfo.setTotal_time(roomPkGame.getTime());
        gameInfo.setLeft_time(roomPkGame.getTime());
        gameInfo.setCreator_info(getRoomPkInfoObject(roomPkGame.getCreator()));
        gameInfo.setChallenger_info(getRoomPkInfoObject(roomPkGame.getReceiver()));
        gameInfo.setWinner_info(getRoomPkUserInfoObject(roomPkGame.getWinner()));
        roomPkGameRedis.savePkGameInfo(gameInfo.getPid(), gameInfo);
        return gameInfo;
    }

    private RoomPkGame buildRoomPkGameData(PkGameDTO reqDTO, int nowTime) {
        RoomPkGame roomPkGame = new RoomPkGame();
        roomPkGame.setCreator(reqDTO.getRoom_id());
        roomPkGame.setTime(reqDTO.getPk_time() * 60);
        roomPkGame.setType(reqDTO.getPk_type());
        roomPkGame.setCreate_time(nowTime);
        roomPkGame.setStatus(PkGameConstant.CREATE_STATUS);
        return roomPkGame;
    }

    private void sendRoomRoomPkMsg(RoomPkGameInfo roomPkGameInfo, ActorData actorData, String topic) {
        RoomRoomPKMsg msg = new RoomRoomPKMsg();
        BeanUtils.copyProperties(roomPkGameInfo, msg);
        msg.setOpt_time(DateHelper.getNowSeconds());
        msg.setTopic(topic);
        PKExtraInfoObject extraMsg = new PKExtraInfoObject();
        if (actorData != null) {
            extraMsg.setOperator(actorData.getName());
            extraMsg.setCancel_type(0);
        }
        extraMsg.setOperator("");
        extraMsg.setCancel_type(1);
        msg.setExtra_msg(extraMsg);
        logger.info("msg={}", JSONObject.toJSONString(msg));
        roomWebSender.sendRoomWebMsg(roomPkGameInfo.getCreator_room_id(), "", msg, true);
        roomWebSender.sendRoomWebMsg(roomPkGameInfo.getChallenger_room_id(), "", msg, true);
    }

    private void sendRoomPkNotifyFriendsMsg(String gameId, String name) {
        RoomRoomPKNotifyFriendsMsg msg = new RoomRoomPKNotifyFriendsMsg();
        msg.setName(name);
        msg.setPid(gameId);
        msg.setGift_token(System.currentTimeMillis() + "");
        roomWebSender.sendRoomWebMsg(RoomWebSender.ALL_ROOM, null, msg, false);
    }

    private void saveRoomPkHallInfo(RoomPkGameInfo gameInfo) {
        RoomPkHallInfo info = new RoomPkHallInfo();
        info.setRoom_id(gameInfo.getCreator_room_id());
        info.setPid(gameInfo.getPid());
        info.setPk_status(gameInfo.getStatus());
        info.setPk_time(gameInfo.getTotal_time() < 600 ? String.format("0%d:00", gameInfo.getTotal_time() / 60) : String.format("%d:00", gameInfo.getTotal_time() / 60));
        PKUserInfoObject creator = gameInfo.getCreator_info().getUser_info();
        info.setPk_creator_info(new RoomPkHallInfo.PkUserInfo(
                creator.getHead(),
                creator.getName(),
                gameInfo.getCreator_room_id(),
                creator.getVip()));
        if (!StringUtils.isEmpty(gameInfo.getChallenger_room_id())) {
            PKUserInfoObject challenger = gameInfo.getChallenger_info().getUser_info();
            info.setPk_challenger_info(new RoomPkHallInfo.PkUserInfo(
                    challenger.getHead(),
                    challenger.getName(),
                    gameInfo.getChallenger_room_id(),
                    challenger.getVip()));
        }
        roomPkGameRedis.saveRoomPkHallInfo(info);
    }

    private void makePkRankList(int period) {
        int nowTime = DateHelper.getNowSeconds();
        List<PkGame> recentList = pkGameDao.findRecentList(nowTime - period);
        Map<String, Long> costBeanMap = new HashMap<>(32);
        if (!CollectionUtils.isEmpty(recentList)) {
            for (PkGame pkGame : recentList) {
                if (pkGame.getCreator_count() > 0 || pkGame.getReceiver_count() > 0) {
                    GiftData giftData = giftDao.getGiftFromCache(pkGame.getGift_id());
                    int giftPrice = giftData.getPrice();
                    if (costBeanMap.containsKey(pkGame.getCreator())) {
                        long totalCostBeans = costBeanMap.get(pkGame.getCreator()) + (long) pkGame.getCreator_count() * giftPrice;
                        costBeanMap.put(pkGame.getCreator(), totalCostBeans);
                    } else {
                        costBeanMap.put(pkGame.getCreator(), (long) pkGame.getCreator_count() * giftPrice);
                    }
                    if (costBeanMap.containsKey(pkGame.getReceiver())) {
                        long totalCostBeans = costBeanMap.get(pkGame.getReceiver()) + (long) pkGame.getReceiver_count() * giftPrice;
                        costBeanMap.put(pkGame.getReceiver(), totalCostBeans);
                    } else {
                        costBeanMap.put(pkGame.getReceiver(), (long) pkGame.getReceiver_count() * giftPrice);
                    }
                }
            }
            List<Map.Entry<String, Long>> entryList = new ArrayList<>(costBeanMap.entrySet());
            entryList.sort(Comparator.comparingLong(o -> -o.getValue()));
            List<PkRankUserInfo> list = new ArrayList<>();
            int count = 0;
            List<String> systemDeleteList = sysConfigDao.getSystemDeleteList();
            for (Map.Entry<String, Long> entry : entryList) {
                if (count >= 20) {
                    continue;
                }
                if (entry.getValue() == 0) {
                    continue;
                }
                String uid = entry.getKey();
                if (!CollectionUtils.isEmpty(systemDeleteList) && systemDeleteList.contains(uid)) {
                    continue;
                }
                ActorData actorData = actorDao.getActorDataFromCache(uid);
                if (actorData == null || actorData.getValid() == 0) {
                    continue;
                }
                list.add(buildPkRankUserInfo(actorData, entry.getValue()));
                count++;
            }
            pkCacheMap.put(period, list);
        }
    }

    private void makeRoomPkRankList(int period) {
        int nowTime = DateHelper.getNowSeconds();
        List<RoomPkGame> recentList = roomPkGameDao.findRecentList(nowTime - period);
        Map<String, Integer> costBeanMap = new HashMap<>(32);
        if (!CollectionUtils.isEmpty(recentList)) {
            for (RoomPkGame roomPkGame : recentList) {
                if (roomPkGame.getCreator_count() > 0 || roomPkGame.getReceiver_count() > 0) {
                    if (costBeanMap.containsKey(roomPkGame.getCreator())) {
                        int totalCostBeans = costBeanMap.get(roomPkGame.getCreator()) + roomPkGame.getCreator_count();
                        costBeanMap.put(roomPkGame.getCreator(), totalCostBeans);
                    } else {
                        costBeanMap.put(roomPkGame.getCreator(), roomPkGame.getCreator_count());
                    }
                    if (costBeanMap.containsKey(roomPkGame.getReceiver())) {
                        int totalCostBeans = costBeanMap.get(roomPkGame.getReceiver()) + roomPkGame.getReceiver_count();
                        costBeanMap.put(roomPkGame.getReceiver(), totalCostBeans);
                    } else {
                        costBeanMap.put(roomPkGame.getReceiver(), roomPkGame.getReceiver_count());
                    }
                }
            }
            List<Map.Entry<String, Integer>> entryList = new ArrayList<>(costBeanMap.entrySet());
            entryList.sort(Comparator.comparingLong(o -> -o.getValue()));
            List<RoomPkRankInfo> list = new ArrayList<>();
            int count = 0;
            List<String> systemDeleteList = sysConfigDao.getSystemDeleteList();
            for (Map.Entry<String, Integer> entry : entryList) {
                if (count >= 20) {
                    continue;
                }
                if (entry.getValue() == 0) {
                    continue;
                }
                String roomId = entry.getKey();
                String aid = RoomUtils.getRoomHostId(roomId);
                if (!CollectionUtils.isEmpty(systemDeleteList) && systemDeleteList.contains(aid)) {
                    continue;
                }
                MongoRoomData roomData = roomDao.getDataFromCache(roomId);
                if (roomData == null) {
                    continue;
                }
                list.add(buildRoomPkRankInfo(aid, roomData, entry.getValue()));
                count++;
            }
            roomPkCacheMap.put(period, list);
        }
    }

    private void endCompletedPk() {
        int nowSeconds = DateHelper.getNowSeconds();
        Set<String> hasEndedPkGameIds = pkGameRedis.getHasEndedPkGameIds(nowSeconds);
        for (String pkGameId : hasEndedPkGameIds) {
            pkGameRedis.deletePkGameRunningTime(pkGameId);
            PkGame pkGame = pkGameDao.findData(pkGameId);
            if (pkGame == null) {
                continue;
            }
            PkGameInfo pkGameInfo = pkGameRedis.getPkGameInfo(pkGameId);
            pkGame.setStatus(PkGameConstant.END_STATUS);
            pkGame.setCreator_count(pkGameInfo.getRed_gift_num());
            pkGame.setReceiver_count(pkGameInfo.getBlue_gift_num());
            pkGameInfo.setStatus(PkGameConstant.END_STATUS);
            pkGameInfo.setLeft_time(0);
            String winner = "";
            if (pkGameInfo.getRed_gift_num() > pkGameInfo.getBlue_gift_num()) {
                winner = pkGameInfo.getCreator_uid();
            } else if (pkGameInfo.getRed_gift_num() < pkGameInfo.getBlue_gift_num()) {
                winner = pkGameInfo.getBlue_info().getUser_info().getUid();
            }
            pkGame.setWinner(winner);
            pkGameInfo.setWinner_info(getPkUserInfoObject(winner));
            pkGameDao.save(pkGame);
            pkGameRedis.savePkGameInfo(pkGame.get_id().toString(), pkGameInfo);
            pkGameRedis.deletePkHallInfo(pkGameId);
            pkGameRedis.deleteInvitedUser(pkGameId);
            pkGameRedis.removeUserInPkGame(pkGame.getCreator());
            pkGameRedis.removeUserInPkGame(pkGame.getReceiver());
            sendRoomMicPkMessage(pkGameInfo, "end_pk");
            // 上报数数
            doReportEvent(pkGame);
            commonTaskService.sendCommonTaskMq(new CommonMqTopicData(pkGame.getCreator(), pkGame.getRoom_id(), pkGame.getReceiver(), pkGame.getWinner(), CommonMqTaskConstant.PLAY_PK_GAME, 1));
        }
    }

    private void endCompletedRoomPk() {
        int nowSeconds = DateHelper.getNowSeconds();
        Set<String> waitEndPkGameIds = roomPkGameRedis.getHasEndedPkGameIds(nowSeconds);
        for (String pkGameId : waitEndPkGameIds) {
            roomPkGameRedis.removeRoomPkEndTime(pkGameId);
            RoomPkGame pkGame = roomPkGameDao.findDataByGameId(pkGameId);
            if (pkGame == null) {
                continue;
            }
            logger.info("end room pk. roomPkGame={}", JSONObject.toJSONString(pkGame));
            roomPkGameRedis.deleteRoomPkHallInfo(pkGameId);
            String creator = RoomUtils.getRoomHostId(pkGame.getCreator());
            pkGameRedis.removeUserInPkGame(RoomUtils.getRoomHostId(pkGame.getCreator()));
            pkGameRedis.removeUserInPkGame(RoomUtils.getRoomHostId(pkGame.getReceiver()));
            roomPkGameRedis.deleteInvitedUser(pkGameId);
            roomPkGameRedis.deleteInPkRoom(pkGame.getCreator());
            roomPkGameRedis.deleteInPkRoom(pkGame.getReceiver());
            RoomPkGameInfo pkGameInfo = roomPkGameRedis.getPkGameInfo(pkGameId);
            pkGame.setStatus(PkGameConstant.END_STATUS);
            pkGameInfo.setStatus(PkGameConstant.END_STATUS);
            String winner = "";
            if (pkGameInfo.getCreator_num() > pkGameInfo.getChallenger_num()) {
                winner = pkGameInfo.getCreator_room_id();
            } else if (pkGameInfo.getCreator_num() < pkGameInfo.getChallenger_num()) {
                winner = pkGameInfo.getChallenger_room_id();
            }
            pkGame.setWinner(winner);
            pkGame.setCreator_count(pkGameInfo.getCreator_num());
            pkGame.setReceiver_count(pkGameInfo.getChallenger_num());
            pkGameInfo.setWinner_info(getRoomPkUserInfoObject(winner));
            pkGameInfo.setLeft_time(0);
            roomPkGameDao.save(pkGame);
            roomPkGameRedis.savePkGameInfo(pkGameId, pkGameInfo);
            sendRoomRoomPkMsg(pkGameInfo, actorDao.getActorDataFromCache(creator), "room_end_pk");
            // 上报数数
            doReportEvent(pkGame);
            String receiverHostId = StringUtils.isEmpty(pkGame.getReceiver()) ? pkGame.getReceiver() : RoomUtils.getRoomHostId(pkGame.getReceiver());
            String winnerHostId = StringUtils.isEmpty(winner) ? winner : RoomUtils.getRoomHostId(winner);
            commonTaskService.sendCommonTaskMq(new CommonMqTopicData(creator, "", receiverHostId, winnerHostId, CommonMqTaskConstant.PLAY_PK_GAME, 1));
        }
    }

    private void endWaitTimeoutPk() {
        int nowSeconds = DateHelper.getNowSeconds();
        Set<String> waitEndPkGameIds = pkGameRedis.getWaitEndPkGameIds(nowSeconds);
        for (String pkGameId : waitEndPkGameIds) {
            pkGameRedis.deletePkGameWaitingTime(pkGameId);
            PkGame pkGame = pkGameDao.findData(pkGameId);
            if (pkGame == null) {
                continue;
            }
            pkGame.setStatus(PkGameConstant.CANCEL_STATUS);
            pkGameDao.save(pkGame);
            PkGameInfo pkGameInfo = pkGameRedis.getPkGameInfo(pkGameId);
            pkGameInfo.setStatus(PkGameConstant.CANCEL_STATUS);
            pkGameRedis.savePkGameInfo(pkGame.get_id().toString(), pkGameInfo);
            if (pkGame.getType() == 0) {
                pkGameRedis.deletePkHallInfo(pkGameId);
            }
            asyncChargeDiamonds(pkGame.getCreator(), pkGame.getRoom_id(), RETURN_CREATE_PK_TITLE, "return create pk game overtime");
            pkGameRedis.removeUserInPkGame(pkGame.getCreator());
            sendRoomMicPkMessage(pkGameInfo, "cancel_pk");
            // 上报数数
            doReportEvent(pkGame);
        }
    }

    private void endWaitTimeoutRoomPk() {
        int nowSeconds = DateHelper.getNowSeconds();
        Set<String> waitEndPkGameIds = roomPkGameRedis.getWaitEndPkGameIds(nowSeconds);
        for (String pkGameId : waitEndPkGameIds) {
            roomPkGameRedis.removeRoomPkWaitEndTime(pkGameId);
            RoomPkGame pkGame = roomPkGameDao.findDataByGameId(pkGameId);
            if (pkGame == null) {
                continue;
            }
            pkGame.setStatus(PkGameConstant.CANCEL_STATUS);
            roomPkGameDao.save(pkGame);
            if (pkGame.getType() == 0) {
                roomPkGameRedis.deleteRoomPkHallInfo(pkGameId);
            }
            String creator = RoomUtils.getRoomHostId(pkGame.getCreator());
            asyncChargeDiamonds(creator, pkGame.getCreator(), RETURN_CREATE_ROOM_PK_TITLE, "return create room pk game overtime");
            pkGameRedis.removeUserInPkGame(RoomUtils.getRoomHostId(pkGame.getCreator()));
            RoomPkGameInfo gameInfo = roomPkGameRedis.getPkGameInfo(pkGameId);
            gameInfo.setStatus(PkGameConstant.CANCEL_STATUS);
            roomPkGameRedis.savePkGameInfo(pkGameId, gameInfo);
            sendRoomRoomPkMsg(gameInfo, actorDao.getActorDataFromCache(creator), "room_cancel_pk");
            // 上报数数
            doReportEvent(pkGame);
        }
    }

    private void asyncChargeDiamonds(String uid, String roomId, String title, String desc) {
        logger.info("asyncChargeDiamonds. uid={}, changed={}, title={}", uid, CREATE_PK_BEANS, title);
        MoneyDetailReq moneyDetailReq = new MoneyDetailReq();
        moneyDetailReq.setRandomId();
        moneyDetailReq.setUid(uid);
        moneyDetailReq.setRoomId(roomId);
        moneyDetailReq.setAtype(PkGameService.RETURN_PK_ACT_TYPE);
        moneyDetailReq.setChanged(CREATE_PK_BEANS);
        moneyDetailReq.setTitle(title);
        moneyDetailReq.setDesc(desc);
        moneyDetailReq.setMtime(DateHelper.getNowSeconds());
        mqSenderService.asyncChargeDiamonds(moneyDetailReq);
    }

    private RoomPkRankInfo buildRoomPkRankInfo(String aid, MongoRoomData roomData, int totalCostBeans) {
        int vipLevel = vipInfoDao.getIntVipLevelFromCache(aid);
        return new RoomPkRankInfo(roomData.getRid(),
                roomData.getHead(),
                roomData.getName(),
                vipLevel,
                vipLevel > 0 ? 1 : 0,
                formatDevotes(totalCostBeans));
    }

    /**
     * 房间pk开始通知好友和房间会员
     */
    private void notifyFriendAndMember(String uid, String roomId, int pkType) {
        BaseTaskFactory.getFactory().addSlow(new Task() {
            @Override
            protected void execute() {
                ActorData actorData = actorDao.getActorDataFromCache(uid);
                if (actorData == null) {
                    logger.error("can not find actor. uid={}", uid);
                    return;
                }
                RoomRoomPKStartNotifyMsg msg = new RoomRoomPKStartNotifyMsg();
                msg.setName(actorData.getName());
                msg.setRoom_id(roomId);
                msg.setVip_level(vipInfoDao.getIntVipLevelFromCache(uid));
                msg.setHead(ImageUrlGenerator.generateRoomUserUrl(actorData.getHead(), msg.getVip_level()));
                msg.setType(pkType);
                msg.setGift_token(UUID.randomUUID().toString().replace("-", ""));
                // 推送消息给好友
                Set<String> friendSet = friendsListRedis.getFriendList(uid);
                if (!CollectionUtils.isEmpty(friendSet)) {
                    logger.info("friendSet.size={}", friendSet.size());
                    for (String aid : friendSet) {
                        roomWebSender.sendPlayerWebMsg("", uid, aid, msg, false);
                    }
                }
                if (pkType == 1) {
                    return;
                }
                msg.setType(3);
                // 推送消息给会员
                Set<String> memberUidList = roomMemberRedis.getMemberList(roomId);
                if (!CollectionUtils.isEmpty(memberUidList)) {
                    logger.info("memberUidList.size={}", memberUidList.size());
                    for (String aid : memberUidList) {
                        if (friendSet.contains(aid)) {
                            continue;
                        }
                        roomWebSender.sendPlayerWebMsg("", uid, aid, msg, false);
                    }
                }
            }
        });
    }

    private void doReportEvent(PkGame pkGame) {
        PkGameLogEvent event = new PkGameLogEvent();
        event.setUid(pkGame.getCreator());
        event.setPk_creator(pkGame.getCreator());
        event.setPk_receiver(pkGame.getReceiver());
        event.setRoom_id(pkGame.getRoom_id());
        event.setGift_id(pkGame.getGift_id());
        event.setPk_duration(pkGame.getTime());
        event.setPk_creator_type(1);
        event.setPk_start_type(pkGame.getType());
        event.setPk_status(pkGame.getStatus());
        event.setCreate_time(pkGame.getCreate_time());
        event.setStart_time(pkGame.getStart_time());
        event.setEnd_time(pkGame.getEnd_time());
        event.setPk_winner(pkGame.getWinner());
        event.setPk_creator_count(pkGame.getCreator_count());
        event.setPk_receiver_count(pkGame.getReceiver_count());
        event.setCtime(DateHelper.getNowSeconds());
        eventReport.track(new EventDTO(event));
    }

    private void doReportEvent(RoomPkGame pkGame) {
        PkGameLogEvent event = new PkGameLogEvent();
        event.setUid(RoomUtils.getRoomHostId(pkGame.getCreator()));
        event.setPk_creator(pkGame.getCreator());
        event.setPk_receiver(pkGame.getReceiver());
        event.setPk_duration(pkGame.getTime());
        event.setPk_creator_type(2);
        event.setPk_start_type(pkGame.getType());
        event.setPk_status(pkGame.getStatus());
        event.setCreate_time(pkGame.getCreate_time());
        event.setStart_time(pkGame.getStart_time());
        event.setEnd_time(pkGame.getEnd_time());
        event.setPk_winner(pkGame.getWinner());
        event.setPk_creator_count(pkGame.getCreator_count());
        event.setPk_receiver_count(pkGame.getReceiver_count());
        event.setCtime(DateHelper.getNowSeconds());
        eventReport.track(new EventDTO(event));
    }
}
