package com.quhong.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Interner;
import com.google.common.collect.Interners;
import com.quhong.analysis.ActivitySpecialItemsChangeEvent;
import com.quhong.analysis.EventDTO;
import com.quhong.analysis.EventReport;
import com.quhong.constant.ActivityHttpCode;
import com.quhong.constant.DetectOriginConstant;
import com.quhong.core.concurrency.BaseTaskFactory;
import com.quhong.core.concurrency.tasks.Task;
import com.quhong.core.config.ServerConfig;
import com.quhong.core.date.DateSupport;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.CommonMqTopicData;
import com.quhong.data.RechargeInfo;
import com.quhong.data.SendGiftData;
import com.quhong.data.vo.PetFeedVO;
import com.quhong.data.vo.TaskConfigVO;
import com.quhong.dto.TextDTO;
import com.quhong.enums.CommonMqTaskConstant;
import com.quhong.enums.HttpCode;
import com.quhong.exception.CommonH5Exception;
import com.quhong.feign.IDetectService;
import com.quhong.handler.ActivityRechargeHandler;
import com.quhong.mongo.data.OtherRankingActivityData;
import com.quhong.mongo.data.ResourceKeyConfigData;
import com.quhong.mysql.dao.GiftDao;
import com.quhong.mysql.dao.PetFeedDao;
import com.quhong.mysql.dao.RechargeDailyInfoDao;
import com.quhong.mysql.dao.WhiteTestDao;
import com.quhong.mysql.data.PetFeedData;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.*;

/**
 * 养宠活动
 */
@Service
public class PetFeedService extends OtherActivityService implements TaskMsgHandler, ActivityRechargeHandler {


    private static final Logger logger = LoggerFactory.getLogger(PetFeedService.class);
    private static final String ACTIVITY_TITLE_EN = "Pet feed";
    public static String ACTIVITY_ID = "Pet_feed";
    private static String ACTIVITY_URL = String.format("https://static.youstar.live/pet_feed/?activityId=%s", ACTIVITY_ID);


    // 喂养自己宠物100g获得爱心数
    private static final int FEED_SELF_PET_LOVE = 1;
    // 投球自己宠物获得爱心数
    private static final int THROW_BALL_SELF_PET_LOVE = 5;
    // 喂养Ta人宠物500自己获得爱心数
    private static final int FEED_OTHER_PET_LOVE = 1;
    // 投球Ta人宠物自己获得爱心数
    private static final int THROW_BALL_OTHER_PET_LOVE = 1;

    // 成为成年宠物的食量
    private static final int ADULT_PET_FOOD = 5000;
    // 年龄阶段每日需要的食物
    private static final List<Integer> AGE_STAGE_FOOD_LIST = Arrays.asList(1000, 2000);
    // 年龄阶段每日需要的玩具
    private static final List<Integer> AGE_STAGE_TOY_LIST = Arrays.asList(5, 10);

    // 每日喂食Ta人的宠物最大数量
    private static final int MAX_FEED_OTHER_PET = 500;
    // 每日投球Ta人宠物最大数量
    private static final int MAX_THROW_BALL_OTHER_PET = 1;

    // 签到奖励食物数量
    private static final List<Integer> SIGN_REWARD_FOOD_LIST = Arrays.asList(100, 100, 100, 150, 150, 150, 0);
    // 签到奖励玩具数量
    private static final List<Integer> SIGN_REWARD_TOY_LIST = Arrays.asList(0, 0, 0, 0, 0, 0, 1);

    private static final String CRASH_SHOP_DRAW_KEY = "CrashExchangeShop";

    private static final List<TaskConfigVO> DAILY_CONFIG_TASK_LIST = new ArrayList<>(); // 每日基础任务

    private static final List<TaskConfigVO> DAILY_CONFIG_ADVANCE_TASK_LIST = new ArrayList<>(); // 每日进阶任务

    private static final Interner<String> stringPool = Interners.newWeakInterner();

    private static final String eventExchangeTitle = "Crash Exchange Shop-exchange";

    private static final int HISTORY_USER_MAX_SIZE = 1000;
    private static final int HISTORY_PAGE_SIZE = 20;

    // 回归用户绑定触发
    public static final String INVITE_BIND_BACK_USER = "invite_bind_back_user";

    private static final List<String> TASK_ITEM_LIST = Arrays.asList(
            CommonMqTaskConstant.ON_MIC_TIME_5_MINUTE,
            CommonMqTaskConstant.INVITE_BIND_USER,
            INVITE_BIND_BACK_USER);


    private static final List<Integer> ALL_PET_TYPE = Arrays.asList(1, 2, 3);
    public static final int PET_STATUS_NORMAL = 1; // 领养状态
    public static final int PET_STATUS_RELEASE = 2; // 放养状态

    static {
        // 每日基础任务 currentProcess为每个单位获取的粮食数
        DAILY_CONFIG_TASK_LIST.add(new TaskConfigVO
                (1, 50, "share_snapchat", "", "", "", 0, "", "", "", ""));
        DAILY_CONFIG_TASK_LIST.add(new TaskConfigVO
                (1, 50, "on_mic_time_5_minute", "", "", "", 0, "", "", "", ""));
        DAILY_CONFIG_TASK_LIST.add(new TaskConfigVO
                (10, 100, "send_gift_500num", "", "", "", 0, "", "", "", ""));
        DAILY_CONFIG_TASK_LIST.add(new TaskConfigVO
                (10, 50, "receive_gift_500num", "", "", "", 0, "", "", "", ""));

        // 每日进阶任务 currentProcess为每个单位获取的玩具数
        DAILY_CONFIG_ADVANCE_TASK_LIST.add(new TaskConfigVO
                (5, 1, "recharge_110_diamond", "", "", "", 0, "", "", "", ""));

        DAILY_CONFIG_ADVANCE_TASK_LIST.add(new TaskConfigVO
                (5, 1, "invite_new_user", "", "", "", 0, "", "", "", ""));

        DAILY_CONFIG_ADVANCE_TASK_LIST.add(new TaskConfigVO
                (5, 1, "invite_old_user", "", "", "", 0, "", "", "", ""));

    }

    @Resource
    private WhiteTestDao whiteTestDao;
    @Resource
    protected ResourceKeyHandlerService resourceKeyHandlerService;
    @Autowired(required = false)
    private EventReport eventReport;
    @Resource
    private GiftDao giftDao;
    @Resource
    private RechargeDailyInfoDao rechargeDailyInfoDao;
    @Resource
    private PetFeedDao petFeedDao;
    @Resource
    private IDetectService idetectService;

    @PostConstruct
    public void init() {
        if (ServerConfig.isNotProduct()) {
            ACTIVITY_ID = "ccc";
            ACTIVITY_URL = String.format("https://test2.qmovies.tv/pet_feed/?activityId=%s", ACTIVITY_ID);
        }

        logger.info("pet feed init");
    }

    // 生涯业务数据锁
    private String getCareerLockKey(String activityId, String uid) {
        return String.format("careerLockKey:%s:%s", activityId, uid);
    }

    // 每日业务数据锁
    private String getDailyLockKey(String activityId, String uid) {
        return String.format("dailyLockKey:%s:%s", activityId, uid);
    }


    // 活动期间总数据,filed为uid
    private String getHashCareerKey(String activityId) {
        return String.format("pet_feed:career:%s", activityId);
    }

    // 个人每日宠物喂养情况,filed为uid
    private String getHashDayKey(String activityId, String dateStr) {
        return String.format("pet_feed:day:feed:%s:%s", activityId, dateStr);
    }

    // 个人每日任务完成情况,filed为uid
    private String getHashDayTaskKey(String activityId, String dateStr) {
        return String.format("pet_feed:day:task:%s:%s", activityId, dateStr);
    }

    // 每日已被喂养的宠物，set
    private String getSetDayFeedPetKey(String activityId, String dateStr) {
        return String.format("pet_feed:day:feed:%s:%s", activityId, dateStr);
    }

    // 已参与活动的用户，set
    private String getSetAllFeedPetKey(String activityId) {
        return String.format("pet_feed:all:uid:%s", activityId);
    }

    // 个人的抽奖历史记录key
    private String getListHistoryKey(String activityId, String uid) {
        return String.format("pet_feed:history:%s:%s", activityId, uid);
    }


    /**
     * 领取宠物
     *
     * @param activityId
     * @param uid
     * @param petName
     * @param petType
     */
    public void getPet(String activityId, String uid, String petName, int petType) {
        checkActivityTime(activityId);
        if (!ALL_PET_TYPE.contains(petType) || StringUtils.isEmpty(petName)) {
            logger.info("getPet param error. petType={} petName={}", petType, petName);
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }
        if (petName.length() > 24) {
            logger.info("length too long petName length:{}", petName.length());
            throw new CommonH5Exception(ActivityHttpCode.PARAM_ERROR);
        }
        if (idetectService.detectText(new TextDTO(petName, DetectOriginConstant.ACTIVITY_RELATED, uid)).getData().getIsSafe() == 0) {
            logger.info("dirty word in pet name. petName={}", petName);
            throw new CommonH5Exception(ActivityHttpCode.DIRTY_WORD);
        }

        PetFeedData petFeedData = petFeedDao.selectByUid(uid);
        int ret = 1;
        if (petFeedData == null) {
            petFeedData = new PetFeedData();
            petFeedData.setUid(uid);
            petFeedData.setPetName(petName);
            petFeedData.setPetType(petType);
            petFeedData.setPetStatus(PET_STATUS_NORMAL);
            petFeedData.setEatTotalFood(0);

            petFeedData.setFood(0);
            petFeedData.setToys(0);
            petFeedData.setCtime(DateHelper.getNowSeconds());
            petFeedData.setMtime(DateHelper.getNowSeconds());
            ret = petFeedDao.insert(petFeedData);
        } else if (petFeedData.getPetStatus() == PET_STATUS_RELEASE) {
            // 放养状态，可以再领养
            petFeedData.setPetStatus(PET_STATUS_NORMAL);
            petFeedData.setPetName(petName);
            petFeedData.setPetType(petType);
            petFeedData.setEatTotalFood(0);
            petFeedData.setCtime(DateHelper.getNowSeconds());
            petFeedData.setMtime(DateHelper.getNowSeconds());
            ret = petFeedDao.updateOne(petFeedData);
        }
        if (ret != 1) {
            logger.error("feedPet error. uid={}", uid);
            throw new CommonH5Exception(HttpCode.SERVER_ERROR);
        }
        synchronized (stringPool.intern(getCareerLockKey(activityId, uid))) {
            String careerKey = getHashCareerKey(activityId);
            PetFeedVO.CareerInfo careerInfo = cacheDataService.getPetFeedVOCareerInfo(careerKey, uid);
            careerInfo.setPetName(petName);
            careerInfo.setPetType(petType);
            careerInfo.setEatTotalFood(0);
            careerInfo.setPetStatus(PET_STATUS_NORMAL);
            careerInfo.setCtime(DateHelper.getNowSeconds());
            activityCommonRedis.setCommonHashData(careerKey, uid, JSONObject.toJSONString(careerInfo));
        }
        String allFeedPetKey = getSetAllFeedPetKey(activityId);
        activityCommonRedis.addCommonSetData(allFeedPetKey, uid);
    }

    /**
     * 签到
     *
     * @param activityId
     * @param uid
     */
    public int signDay(String activityId, String uid) {
        int isShowSignPopup = 0;
        if (!inActivityTime(activityId)) {
            return 0;
        }
        if (!isJoinActivity(activityId, uid)) {
            return 0;
        }
        String currentDate = getDayByBase(activityId, uid);
        synchronized (stringPool.intern(getCareerLockKey(activityId, uid))) {
            String careerKey = getHashCareerKey(activityId);
            PetFeedVO.CareerInfo careerInfo = cacheDataService.getPetFeedVOCareerInfo(careerKey, uid);
            String alreadySignDate = careerInfo.getSignDate();
            if (currentDate.equals(alreadySignDate)) {
                // logger.info("signDay already. uid={}", uid);
                return 0;
            }
            LocalDate nowDate = DateSupport.ARABIAN.parse(alreadySignDate);
            LocalDate tomorrowDate = nowDate.minusDays(-1);
            // 获取字符格式 yyyy-MM-dd
            String tomorrow = DateSupport.format(tomorrowDate);

            if (tomorrow.equals(currentDate)) {
                int signDay = careerInfo.getSignDay() + 1 > 7 ? 1 : careerInfo.getSignDay() + 1;
                careerInfo.setSignDay(signDay);
            } else {
                careerInfo.setSignDay(1);
            }
            careerInfo.setSignDate(currentDate);
            int food = SIGN_REWARD_FOOD_LIST.get(careerInfo.getSignDay() - 1);
            int toys = SIGN_REWARD_TOY_LIST.get(careerInfo.getSignDay() - 1);
            if (food > 0) {
                careerInfo.setFood(careerInfo.getFood() + food);
            }
            if (toys > 0) {
                careerInfo.setToys(careerInfo.getToys() + toys);
            }
            String json = JSONObject.toJSONString(careerInfo);
            activityCommonRedis.setCommonHashData(careerKey, uid, json);
            logger.info("success signDay alreadySignTomorrow={} alreadySignDate={} currentDate={} " +
                            "signDay={} addFood={} addToys={} json={}"
                    , tomorrow, alreadySignDate, currentDate, careerInfo.getSignDay(), food, toys, json);
            isShowSignPopup = 1;
        }
        return isShowSignPopup;
    }


    public PetFeedVO petFeedConfig(String activityId, String uid) {
        OtherRankingActivityData activity = otherActivityService.getOtherRankingActivityNull(activityId);

        PetFeedVO vo = new PetFeedVO();

        return vo;
    }

    public void releasePet(String activityId, String uid) {
        checkActivityTime(activityId);
        if (!isJoinActivity(activityId, uid)) {
            logger.info("not join activity. uid={}", uid);
            throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "pet not found");
        }
        PetFeedData petFeedData = petFeedDao.selectByUid(uid);
        if (petFeedData == null || petFeedData.getPetStatus() == PET_STATUS_RELEASE) {
            logger.info("pet not found. uid={}", uid);
            throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "pet not found");
        }
        String careerKey = getHashCareerKey(activityId);
        synchronized (stringPool.intern(getCareerLockKey(activityId, uid))) {
            PetFeedVO.CareerInfo careerInfo = cacheDataService.getPetFeedVOCareerInfo(careerKey, uid);
            if (careerInfo.getEatTotalFood() < ADULT_PET_FOOD) {
                logger.info("pet is not adult. uid={}", uid); // 不能放养幼年期的宠物
                throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "pet is not adult");
            }
            careerInfo.setPetStatus(PET_STATUS_RELEASE);
            activityCommonRedis.setCommonHashData(careerKey, uid, JSONObject.toJSONString(careerInfo));
        }
        petFeedData.setPetStatus(PET_STATUS_RELEASE);
        petFeedData.setMtime(DateHelper.getNowSeconds());
        petFeedDao.updateOne(petFeedData);
        activityCommonRedis.removeCommonSetData(getSetAllFeedPetKey(activityId), uid);
    }

    public void exchange(String activityId, String uid, String metaId) {
        if (!inActivityTime(activityId)) {
            throw new CommonH5Exception(ActivityHttpCode.NOT_ACTIVE_TIME);
        }

        if (StringUtils.isEmpty(metaId)) {
            logger.info("metaId is empty. uid={} metaId={}", uid, metaId);
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }
        // 获取兑换商品配置
        ResourceKeyConfigData keyConfigData = resourceKeyHandlerService.getConfigData(CRASH_SHOP_DRAW_KEY);
        ResourceKeyConfigData.ResourceMeta targetMeta = null;

        if (keyConfigData != null && !CollectionUtils.isEmpty(keyConfigData.getResourceMetaList())) {
            for (ResourceKeyConfigData.ResourceMeta meta : keyConfigData.getResourceMetaList()) {
                if (metaId.equals(meta.getMetaId())) {
                    targetMeta = meta;
                    break;
                }
            }
        }

        if (targetMeta == null) {
            logger.info("targetMeta is null. uid={} metaId={}", uid, metaId);
            throw new CommonH5Exception(ActivityHttpCode.INCORRECT_INPUT_ID);
        }
        int requiredPoints = Integer.parseInt(targetMeta.getRateNumber());
        synchronized (stringPool.intern(getCareerLockKey(activityId, uid))) {
            String careerKey = getHashCareerKey(activityId);
            PetFeedVO.CareerInfo careerInfo = cacheDataService.getPetFeedVOCareerInfo(careerKey, uid);
            int currentPoints = careerInfo.getLove();


            if (currentPoints < requiredPoints) {
                throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "love not enough");
            }

            // 扣除积分
            careerInfo.setLove(currentPoints - requiredPoints);
            activityCommonRedis.setCommonHashData(careerKey, uid, JSONObject.toJSONString(careerInfo));
        }
        // 发放奖励
        resourceKeyHandlerService.sendOneResourceData(uid, targetMeta, 905,
                eventExchangeTitle, eventExchangeTitle, eventExchangeTitle, ACTIVITY_URL, "", 1);

        // 保存兑换历史
        PetFeedVO.HistoryRecordVO historyMeta = new PetFeedVO.HistoryRecordVO();
        historyMeta.setActionType(7);
        historyMeta.setLove(requiredPoints);
        historyMeta.setName(targetMeta.getResourceNameAr());
        historyMeta.setCtime(DateHelper.getNowSeconds());
        leftPushAllHistoryList(uid, Arrays.asList(historyMeta));
        doReportSpecialItemsEvent(ACTIVITY_ID, uid, 2, 2, requiredPoints);
    }


    public PetFeedVO.HistoryRecordPageVO getHistoryListPageRecord(String activityId, String uid, int page) {
        PetFeedVO.HistoryRecordPageVO vo = new PetFeedVO.HistoryRecordPageVO();
        int start = (page - 1) * HISTORY_PAGE_SIZE;
        int end = page * HISTORY_PAGE_SIZE;
        String key = getListHistoryKey(activityId, uid);
        List<String> jsonList = activityCommonRedis.getCommonListPageRecord(key, start, end);
        List<PetFeedVO.HistoryRecordVO> resultList = new ArrayList<>();
        for (String json : jsonList) {
            PetFeedVO.HistoryRecordVO rewardData = JSON.parseObject(json, PetFeedVO.HistoryRecordVO.class);
            resultList.add(rewardData);
        }
        vo.setHistoryRecordList(resultList);
        vo.setNextPage(resultList.size() < HISTORY_PAGE_SIZE ? -1 : page + 1);
        return vo;
    }

    public void setShareSnapchat(String activityId, String uid) {
        checkActivityTime(activityId);
        String dateStr = getDayByBase(ACTIVITY_ID, uid);
        String dailyTaskKey = getHashDayTaskKey(ACTIVITY_ID, dateStr);
        PetFeedVO.DailyTaskInfo dailyTaskInfo = cacheDataService.getPetFeedVODailyTaskInfo(dailyTaskKey, dateStr, uid);
        if (dailyTaskInfo.getShareSnapchatStatus() == 1) {
            return;
        }
        String careerKey = getHashCareerKey(ACTIVITY_ID);
        synchronized (stringPool.intern(getCareerLockKey(ACTIVITY_ID, uid))) {
            dailyTaskInfo.setShareSnapchatStatus(1);
            activityCommonRedis.setCommonHashData(dailyTaskKey, dateStr, JSONObject.toJSONString(dailyTaskInfo));

            PetFeedVO.CareerInfo careerInfo = cacheDataService.getPetFeedVOCareerInfo(careerKey, uid);
            careerInfo.setFood(careerInfo.getFood() + DAILY_CONFIG_TASK_LIST.get(0).getCurrentProcess());
            activityCommonRedis.setCommonHashData(careerKey, uid, JSONObject.toJSONString(careerInfo));
        }
    }

    @Override
    public void taskMsgProcess(CommonMqTopicData data) {
        String uid = data.getUid();
        String aid = data.getAid();
        String roomId = data.getRoomId();
        String item = data.getItem();

        if (!TASK_ITEM_LIST.contains(item)) {
            return;
        }
        if (otherActivityService.getOtherRankingActivityNull(ACTIVITY_ID) == null) {
            return;
        }
        if (!inActivityTime(ACTIVITY_ID)) {
            // 临界时间处理
            return;
        }

        if (!checkAc(uid)) {
            return;
        }
        syncAddFoodOrToys(uid, data, null, null);
    }

    @Override
    public void process(RechargeInfo rechargeInfo) {
        if (rechargeInfo == null || !StringUtils.hasLength(rechargeInfo.getUid()) || rechargeInfo.getRechargeMoney() == null) {
            return;
        }
        int rechargeType = rechargeInfo.getRechargeType();
        if (rechargeType != 1 || rechargeInfo.getRechargeDiamond() <= 0) {
            return;
        }
        if (otherActivityService.getOtherRankingActivityNull(ACTIVITY_ID) == null) {
            return;
        }
        if (!inActivityTime(ACTIVITY_ID)) {
            // 临界时间处理
            return;
        }
        String uid = rechargeInfo.getUid();
        if (!checkAc(uid)) {
            return;
        }
        syncAddFoodOrToys(uid, null, null, rechargeInfo);
    }

    public void sendGiftHandle(SendGiftData giftData, String activityId) {
        if (giftData.getFrom_uid() == null) {
            return;
        }
        syncAddFoodOrToys(giftData.getFrom_uid(), null, giftData, null);
    }

    private void syncAddFoodOrToys(String uid, CommonMqTopicData data, SendGiftData giftData, RechargeInfo rechargeInfo) {
        if (!isJoinActivity(uid, ACTIVITY_ID)) {
            return;
        }
        String dateStr = getDayByBase(ACTIVITY_ID, uid);
        String careerKey = getHashCareerKey(ACTIVITY_ID);
        String dailyTaskKey = getHashDayTaskKey(ACTIVITY_ID, dateStr);
        // String dailyKey = getHashDayKey(ACTIVITY_ID, dateStr);
        synchronized (stringPool.intern(getCareerLockKey(ACTIVITY_ID, uid))) {
            PetFeedVO.CareerInfo careerInfo = cacheDataService.getPetFeedVOCareerInfo(careerKey, uid);
            PetFeedVO.DailyTaskInfo dailyTaskInfo = cacheDataService.getPetFeedVODailyTaskInfo(dailyTaskKey, dateStr, uid);
        }
    }

    private boolean checkAc(String uid) {
        OtherRankingActivityData activityData = getOtherRankingActivityNull(ACTIVITY_ID);
        if (ServerConfig.isProduct() && activityData.getAcNameEn().startsWith("test")) {
            boolean isWhiteTest = whiteTestDao.isMemberByType(uid, WhiteTestDao.WHITE_TYPE_RID);
            if (!isWhiteTest) {
                // 灰度测试,只统计测试用户的
                return false;
            }
        }
        return true;
    }

    // 保存历史记录
    private void leftPushAllHistoryList(String uid, List<PetFeedVO.HistoryRecordVO> srcList) {
        String historyKey = getListHistoryKey(ACTIVITY_ID, uid);
        List<String> strList = new ArrayList<>();
        for (PetFeedVO.HistoryRecordVO meta : srcList) {
            String json = JSONObject.toJSONString(meta);
            strList.add(json);
        }
        if (CollectionUtils.isEmpty(strList)) {
            return;
        }
        activityCommonRedis.leftPushAllCommonList(historyKey, strList, HISTORY_USER_MAX_SIZE);
    }

    private boolean isJoinActivity(String activityId, String uid) {
        String allFeedPetKey = getSetAllFeedPetKey(activityId);
        return activityCommonRedis.isCommonSetData(allFeedPetKey, uid) > 0;
    }


    public void distributionRanking(String activityId) {
        BaseTaskFactory.getFactory().addSlow(new Task() {
            @Override
            protected void execute() {
                distributionRankingToMysql(activityId);
            }
        });

    }

    /**
     * 个人数据写mysql做持久化
     *
     * @param activityId
     */
    private void distributionRankingToMysql(String activityId) {
        try {
            Map<String, String> hashMap = activityCommonRedis.getCommonHashAllMapStr(getHashCareerKey(activityId));
            for (Map.Entry<String, String> entry : hashMap.entrySet()) {
                String uid = entry.getKey();
                PetFeedVO.CareerInfo careerInfo = JSON.parseObject(entry.getValue(), PetFeedVO.CareerInfo.class);
                PetFeedData petFeedData = new PetFeedData();
                petFeedData.setUid(uid);
                petFeedData.setPetName(careerInfo.getPetName());
                petFeedData.setPetType(careerInfo.getPetType());
                petFeedData.setPetStatus(careerInfo.getPetStatus());
                petFeedData.setEatTotalFood(careerInfo.getEatTotalFood());
                petFeedData.setFood(careerInfo.getFood());
                petFeedData.setToys(careerInfo.getToys());
                petFeedData.setCtime(careerInfo.getCtime());
                petFeedData.setMtime(DateHelper.getNowSeconds());
                petFeedDao.updateByUid(petFeedData);
            }
        } catch (Exception e) {
            logger.error("distributionRanking error: {}", e.getMessage(), e);
        }
    }

    private void handleRes(String aid, String resKey, String eventTitle) {
        resourceKeyHandlerService.sendResourceData(aid, resKey,
                eventTitle, eventTitle, eventTitle, ACTIVITY_URL, "");
    }

    /**
     * 上报机会获取消耗事件
     */
    private void doReportSpecialItemsEvent(String activityId, String uid, int action, int source, int num) {
        ActivitySpecialItemsChangeEvent event = new ActivitySpecialItemsChangeEvent();
        event.setUid(uid);
        event.setCtime(DateHelper.getNowSeconds());
        event.setChange_action(action);
        event.setActivity_special_items_id("0");
        event.setActivity_name(ACTIVITY_TITLE_EN);
        event.setActive_id(activityId);
        event.setActivity_special_items_resource(source);
        event.setResource_desc("");
        event.setChange_nums(num);
        eventReport.track(new EventDTO(event));
    }


}
