package com.quhong.service;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.google.common.collect.Interner;
import com.google.common.collect.Interners;
import com.quhong.analysis.ActivitySpecialItemsChangeEvent;
import com.quhong.analysis.EventDTO;
import com.quhong.analysis.EventReport;
import com.quhong.constant.ActivityHttpCode;
import com.quhong.constant.DetectOriginConstant;
import com.quhong.core.concurrency.BaseTaskFactory;
import com.quhong.core.concurrency.tasks.Task;
import com.quhong.core.config.ServerConfig;
import com.quhong.core.date.DateSupport;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.CommonMqTopicData;
import com.quhong.data.RechargeInfo;
import com.quhong.data.SendGiftData;
import com.quhong.data.vo.PetFeedVO;
import com.quhong.data.vo.TaskConfigVO;
import com.quhong.dto.TextDTO;
import com.quhong.enums.CommonMqTaskConstant;
import com.quhong.enums.HttpCode;
import com.quhong.exception.CommonH5Exception;
import com.quhong.feign.IDetectService;
import com.quhong.handler.ActivityRechargeHandler;
import com.quhong.image.ImageUrlGenerator;
import com.quhong.mongo.data.OtherRankingActivityData;
import com.quhong.mongo.data.ResourceKeyConfigData;
import com.quhong.mysql.dao.GiftDao;
import com.quhong.mysql.dao.PetFeedDao;
import com.quhong.mysql.dao.RechargeDailyInfoDao;
import com.quhong.mysql.dao.WhiteTestDao;
import com.quhong.mysql.data.PetFeedData;
import com.quhong.mongo.dao.ActorDao;
import com.quhong.data.ActorData;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.time.LocalDate;
import java.util.*;

/**
 * 养宠活动
 */
@Service
public class PetFeedService extends OtherActivityService implements TaskMsgHandler, ActivityRechargeHandler {


    private static final Logger logger = LoggerFactory.getLogger(PetFeedService.class);
    private static final String ACTIVITY_TITLE_EN = "Pet feed";
    public static String ACTIVITY_ID = "Pet_feed";
    private static String ACTIVITY_URL = String.format("https://static.youstar.live/pet_feed/?activityId=%s", ACTIVITY_ID);


    // 喂养自己宠物100g获得爱心数
    private static final int FEED_SELF_PET_LOVE = 1;
    // 投球自己宠物获得爱心数
    private static final int THROW_BALL_SELF_PET_LOVE = 5;
    // 喂养Ta人宠物500自己获得爱心数
    private static final int FEED_OTHER_PET_LOVE = 1;
    // 投球Ta人宠物自己获得爱心数
    private static final int THROW_BALL_OTHER_PET_LOVE = 1;

    // 成为成年宠物的食量
    private static final int ADULT_PET_FOOD = 5000;
    // 年龄阶段每日需要的食物
    private static final List<Integer> AGE_STAGE_FOOD_LIST = Arrays.asList(1000, 2000);
    // 年龄阶段每日需要的玩具
    private static final List<Integer> AGE_STAGE_TOY_LIST = Arrays.asList(5, 10);

    // 每日喂食Ta人的宠物最大数量
    private static final int MAX_FEED_OTHER_PET = 500;
    // 每日投球Ta人宠物最大数量
    private static final int MAX_THROW_BALL_OTHER_PET = 1;

    // 签到奖励食物数量
    private static final List<Integer> SIGN_REWARD_FOOD_LIST = Arrays.asList(100, 100, 100, 150, 150, 150, 0);
    // 签到奖励玩具数量
    private static final List<Integer> SIGN_REWARD_TOY_LIST = Arrays.asList(0, 0, 0, 0, 0, 0, 1);

    private static final String CRASH_SHOP_DRAW_KEY = "CrashExchangeShop";

    private static final List<TaskConfigVO> DAILY_CONFIG_TASK_LIST = new ArrayList<>(); // 每日基础任务

    private static final List<TaskConfigVO> DAILY_CONFIG_ADVANCE_TASK_LIST = new ArrayList<>(); // 每日进阶任务

    private static final Interner<String> stringPool = Interners.newWeakInterner();

    private static final String eventExchangeTitle = "Crash Exchange Shop-exchange";

    private static final int HISTORY_USER_MAX_SIZE = 1000;
    private static final int HISTORY_PAGE_SIZE = 20;

    // 回归用户绑定触发
    public static final String INVITE_BIND_BACK_USER = "invite_bind_back_user";

    // 任务奖励常量
    private static final int SHARE_SNAPCHAT_FOOD_REWARD = 50; // 分享到snapchat获得食物数量
    private static final int ON_MIC_5_MINUTE_FOOD_REWARD = 50; // 上麦5分钟获得食物数量
    private static final int GIFT_DIAMOND_UNIT = 500; // 送礼/收礼钻石单位
    private static final int GIFT_FOOD_SEND_REWARD_PER_UNIT = 100; // 每个钻石单位获得的食物数量
    private static final int GIFT_FOOD_RECEIVE_REWARD_PER_UNIT = 50; // 每个钻石单位获得的食物数量
    private static final int MAX_GIFT_UNITS = 10; // 送礼/收礼最大单位数

    private static final int RECHARGE_DIAMOND_THRESHOLD = 110; // 充值钻石门槛
    private static final int TOY_REWARD_PER_TASK = 1; // 每个进阶任务获得的玩具数量
    private static final int MAX_ADVANCE_TASK_UNITS = 5; // 进阶任务最大单位数

    // 喂养和玩耍类型常量
    private static final int FEED_TYPE_FOOD = 1; // 喂养
    private static final int FEED_TYPE_TOY = 2; // 玩耍
    private static final int FEED_AMOUNT_ALL = -1; // 全部喂食
    private static final int FEED_AMOUNT_NORMAL = 100; // 正常喂食数量
    private static final int TOY_AMOUNT_NORMAL = 1; // 正常玩耍数量

    // 历史记录动作类型常量
    private static final int ACTION_TYPE_FEED_SELF = 1; // 喂食自己宠物
    private static final int ACTION_TYPE_TOY_SELF = 2; // 投球自己宠物
    private static final int ACTION_TYPE_FEED_OTHER = 3; // 喂养他人宠物
    private static final int ACTION_TYPE_FEED_OTHER_GOAL = 4; // 喂养他人达成目标
    private static final int ACTION_TYPE_TOY_OTHER = 5; // 投球他人宠物
    private static final int ACTION_TYPE_TOY_OTHER_GOAL = 6; // 投球他人达成目标
    private static final int ACTION_TYPE_SHOP_EXCHANGE = 7; // 商店兑换
    private static final int ACTION_TYPE_OTHER_FEED_ME = 8; // 别人喂养我的宠物
    private static final int ACTION_TYPE_OTHER_TOY_ME = 9; // 别人投球我的宠物

    private static final List<String> TASK_ITEM_LIST = Arrays.asList(
            CommonMqTaskConstant.ON_MIC_TIME_5_MINUTE,
            CommonMqTaskConstant.INVITE_BIND_USER,
            INVITE_BIND_BACK_USER);


    private static final List<Integer> ALL_PET_TYPE = Arrays.asList(1, 2, 3);
    public static final int PET_STATUS_NORMAL = 1; // 领养状态
    public static final int PET_STATUS_RELEASE = 2; // 放养状态

    static {
        // 每日基础任务 currentProcess为每个单位获取的粮食数
        DAILY_CONFIG_TASK_LIST.add(new TaskConfigVO
                (1, 50, "share_snapchat", "", "", "", 0, "", "", "", ""));
        DAILY_CONFIG_TASK_LIST.add(new TaskConfigVO
                (1, 50, "on_mic_time_5_minute", "", "", "", 0, "", "", "", ""));
        DAILY_CONFIG_TASK_LIST.add(new TaskConfigVO
                (10, 100, "send_gift_500num", "", "", "", 0, "", "", "", ""));
        DAILY_CONFIG_TASK_LIST.add(new TaskConfigVO
                (10, 50, "receive_gift_500num", "", "", "", 0, "", "", "", ""));

        // 每日进阶任务 currentProcess为每个单位获取的玩具数
        DAILY_CONFIG_ADVANCE_TASK_LIST.add(new TaskConfigVO
                (5, 1, "recharge_110_diamond", "", "", "", 0, "", "", "", ""));

        DAILY_CONFIG_ADVANCE_TASK_LIST.add(new TaskConfigVO
                (5, 1, "invite_new_user", "", "", "", 0, "", "", "", ""));

        DAILY_CONFIG_ADVANCE_TASK_LIST.add(new TaskConfigVO
                (5, 1, "invite_old_user", "", "", "", 0, "", "", "", ""));

    }

    @Resource
    private WhiteTestDao whiteTestDao;
    @Resource
    protected ResourceKeyHandlerService resourceKeyHandlerService;
    @Autowired(required = false)
    private EventReport eventReport;
    @Resource
    private GiftDao giftDao;
    @Resource
    private RechargeDailyInfoDao rechargeDailyInfoDao;
    @Resource
    private PetFeedDao petFeedDao;
    @Resource
    private IDetectService idetectService;
    @Resource
    private ActorDao actorDao;

    @PostConstruct
    public void init() {
        if (ServerConfig.isNotProduct()) {
            ACTIVITY_ID = "ccc";
            ACTIVITY_URL = String.format("https://test2.qmovies.tv/pet_feed/?activityId=%s", ACTIVITY_ID);
        }

        logger.info("pet feed init");
    }

    // 生涯业务数据锁
    private String getCareerLockKey(String activityId, String uid) {
        return String.format("careerLockKey:%s:%s", activityId, uid);
    }

    // 每日业务数据锁
    private String getDailyLockKey(String activityId, String uid) {
        return String.format("dailyLockKey:%s:%s", activityId, uid);
    }


    // 活动期间总数据,filed为uid
    private String getHashCareerKey(String activityId) {
        return String.format("pet_feed:career:%s", activityId);
    }

    // 个人每日宠物喂养情况,filed为uid
    private String getHashDayKey(String activityId, String dateStr) {
        return String.format("pet_feed:day:feed:%s:%s", activityId, dateStr);
    }

    // 个人每日任务完成情况,filed为uid
    private String getHashDayTaskKey(String activityId, String dateStr) {
        return String.format("pet_feed:day:task:%s:%s", activityId, dateStr);
    }

    // 每日已被喂养的宠物，set
    private String getSetDayFeedPetKey(String activityId, String dateStr) {
        return String.format("pet_feed:day:feed:%s:%s", activityId, dateStr);
    }

    // 已参与活动的用户，set
    private String getSetAllFeedPetKey(String activityId) {
        return String.format("pet_feed:all:uid:%s", activityId);
    }

    // 个人的抽奖历史记录key
    private String getListHistoryKey(String activityId, String uid) {
        return String.format("pet_feed:history:%s:%s", activityId, uid);
    }


    /**
     * 领取宠物
     *
     * @param activityId
     * @param uid
     * @param petName
     * @param petType
     */
    public void getPet(String activityId, String uid, String petName, int petType) {
        checkActivityTime(activityId);
        if (!ALL_PET_TYPE.contains(petType) || StringUtils.isEmpty(petName)) {
            logger.info("getPet param error. petType={} petName={}", petType, petName);
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }
        if (petName.length() > 24) {
            logger.info("length too long petName length:{}", petName.length());
            throw new CommonH5Exception(ActivityHttpCode.PARAM_ERROR);
        }
        if (idetectService.detectText(new TextDTO(petName, DetectOriginConstant.ACTIVITY_RELATED, uid)).getData().getIsSafe() == 0) {
            logger.info("dirty word in pet name. petName={}", petName);
            throw new CommonH5Exception(ActivityHttpCode.DIRTY_WORD);
        }

        PetFeedData petFeedData = petFeedDao.selectByUid(uid);
        int ret = 1;
        if (petFeedData == null) {
            petFeedData = new PetFeedData();
            petFeedData.setUid(uid);
            petFeedData.setPetName(petName);
            petFeedData.setPetType(petType);
            petFeedData.setPetStatus(PET_STATUS_NORMAL);
            petFeedData.setEatTotalFood(0);

            petFeedData.setFood(0);
            petFeedData.setToys(0);
            petFeedData.setCtime(DateHelper.getNowSeconds());
            petFeedData.setMtime(DateHelper.getNowSeconds());
            ret = petFeedDao.insert(petFeedData);
        } else if (petFeedData.getPetStatus() == PET_STATUS_RELEASE) {
            // 放养状态，可以再领养
            petFeedData.setPetStatus(PET_STATUS_NORMAL);
            petFeedData.setPetName(petName);
            petFeedData.setPetType(petType);
            petFeedData.setEatTotalFood(0);
            petFeedData.setCtime(DateHelper.getNowSeconds());
            petFeedData.setMtime(DateHelper.getNowSeconds());
            ret = petFeedDao.updateOne(petFeedData);
        }
        if (ret != 1) {
            logger.error("feedPet error. uid={}", uid);
            throw new CommonH5Exception(HttpCode.SERVER_ERROR);
        }
        synchronized (stringPool.intern(getCareerLockKey(activityId, uid))) {
            String careerKey = getHashCareerKey(activityId);
            PetFeedVO.CareerInfo careerInfo = cacheDataService.getPetFeedVOCareerInfo(careerKey, uid);
            careerInfo.setPetName(petName);
            careerInfo.setPetType(petType);
            careerInfo.setEatTotalFood(0);
            careerInfo.setPetStatus(PET_STATUS_NORMAL);
            careerInfo.setCtime(DateHelper.getNowSeconds());
            activityCommonRedis.setCommonHashData(careerKey, uid, JSONObject.toJSONString(careerInfo));
        }

        String dateStr = getDayByBase(activityId, uid);
        String dailyKey = getHashDayKey(activityId, dateStr);
        synchronized (stringPool.intern(getDailyLockKey(activityId, uid))) {
            // 清空宠物当日喂养数据
            PetFeedVO.DailyInfo dailyFeedInfo = cacheDataService.getPetFeedVODailyInfo(dailyKey, dateStr, uid);
            dailyFeedInfo.setEatFood(0);
            dailyFeedInfo.setPlayToys(0);
            activityCommonRedis.setCommonHashData(dailyKey, dateStr, JSONObject.toJSONString(dailyFeedInfo));
        }

        String allFeedPetKey = getSetAllFeedPetKey(activityId);
        activityCommonRedis.addCommonSetData(allFeedPetKey, uid);
    }

    /**
     * 签到
     *
     * @param activityId
     * @param uid
     */
    private int signDay(String activityId, String uid) {
        int isShowSignPopup = 0;
        if (!inActivityTime(activityId)) {
            return 0;
        }
        if (!isJoinActivity(activityId, uid)) {
            return 0;
        }
        String currentDate = getDayByBase(activityId, uid);
        synchronized (stringPool.intern(getCareerLockKey(activityId, uid))) {
            String careerKey = getHashCareerKey(activityId);
            PetFeedVO.CareerInfo careerInfo = cacheDataService.getPetFeedVOCareerInfo(careerKey, uid);
            String alreadySignDate = careerInfo.getSignDate();
            if (currentDate.equals(alreadySignDate)) {
                // logger.info("signDay already. uid={}", uid);
                return 0;
            }
            LocalDate nowDate = DateSupport.ARABIAN.parse(alreadySignDate);
            LocalDate tomorrowDate = nowDate.minusDays(-1);
            // 获取字符格式 yyyy-MM-dd
            String tomorrow = DateSupport.format(tomorrowDate);

            if (tomorrow.equals(currentDate)) {
                int signDay = careerInfo.getSignDay() + 1 > 7 ? 1 : careerInfo.getSignDay() + 1;
                careerInfo.setSignDay(signDay);
            } else {
                careerInfo.setSignDay(1);
            }
            careerInfo.setSignDate(currentDate);
            int food = SIGN_REWARD_FOOD_LIST.get(careerInfo.getSignDay() - 1);
            int toys = SIGN_REWARD_TOY_LIST.get(careerInfo.getSignDay() - 1);
            if (food > 0) {
                careerInfo.setFood(careerInfo.getFood() + food);
            }
            if (toys > 0) {
                careerInfo.setToys(careerInfo.getToys() + toys);
            }
            String json = JSONObject.toJSONString(careerInfo);
            activityCommonRedis.setCommonHashData(careerKey, uid, json);
            recordMysql(uid, careerInfo);
            logger.info("success signDay alreadySignTomorrow={} alreadySignDate={} currentDate={} " +
                            "signDay={} addFood={} addToys={} json={}"
                    , tomorrow, alreadySignDate, currentDate, careerInfo.getSignDay(), food, toys, json);
            isShowSignPopup = 1;
        }
        return isShowSignPopup;
    }


    public PetFeedVO petFeedConfig(String activityId, String uid) {
        OtherRankingActivityData activity = otherActivityService.getOtherRankingActivityNull(activityId);

        PetFeedVO vo = new PetFeedVO();
        vo.setStartTime(activity.getStartTime());
        vo.setEndTime(activity.getEndTime());

        PetFeedVO.MyInfoVO myInfoVO = new PetFeedVO.MyInfoVO();
        PetFeedVO.PetInfoVO petInfoVO = new PetFeedVO.PetInfoVO();
        List<TaskConfigVO> dailyTaskList = new ArrayList<>(); // 每日基础任务
        List<TaskConfigVO> dailyAdvanceTaskList = new ArrayList<>(); // 每日进阶任务

        vo.setMyInfoVO(myInfoVO);
        vo.setPetInfoVO(petInfoVO);
        vo.setDailyTaskList(dailyTaskList);
        vo.setDailyAdvanceTaskList(dailyAdvanceTaskList);
        return vo;
    }

    /**
     * @param activityId
     * @param uid
     * @param feedType   1 喂养 2 玩耍
     * @param amount     数量  喂食只能-1或100 -1表示全部喂食(只有喂养自己宠物时有此选项)或100食物, 玩耍只能是1
     * @param aid
     * @return
     */
    public PetFeedVO feedPet(String activityId, String uid, int feedType, int amount, String aid) {
        checkActivityTime(activityId);
        // 参数验证
        if (feedType != FEED_TYPE_FOOD && feedType != FEED_TYPE_TOY) {
            logger.info("invalid feedType. uid={}, feedType={}", uid, feedType);
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }

        if (feedType == FEED_TYPE_FOOD && amount != FEED_AMOUNT_ALL && amount != FEED_AMOUNT_NORMAL) {
            logger.info("invalid food amount. uid={}, amount={}", uid, amount);
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }

        if (feedType == FEED_TYPE_TOY && amount != TOY_AMOUNT_NORMAL) {
            logger.info("invalid toy amount. uid={}, amount={}", uid, amount);
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }

        PetFeedVO vo = new PetFeedVO();
        PetFeedVO.MyInfoVO myInfoVO = new PetFeedVO.MyInfoVO();
        PetFeedVO.PetInfoVO petInfoVO = new PetFeedVO.PetInfoVO();

        if (StringUtils.isEmpty(aid)) {

            // 喂养或玩耍自己的宠物
            if (!isJoinActivity(activityId, uid)) {
                logger.info("not join activity. uid={}", uid);
                throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "not join activity");
            }
            feedSelfPet(activityId, uid, feedType, amount, myInfoVO, petInfoVO);

            ActorData actorData = actorDao.getActorDataFromCache(uid);
            myInfoVO.setMyName(actorData.getName());
            myInfoVO.setMyHead(ImageUrlGenerator.generateRoomUserUrl(actorData.getHead()));

            petInfoVO.setAid(uid);
            petInfoVO.setAidName(actorData.getName());
            petInfoVO.setAidHead(ImageUrlGenerator.generateRoomUserUrl(actorData.getHead()));
        } else {
            ActorData aidActorData = actorDao.getActorDataFromCache(aid);
            if (aidActorData == null) {
                logger.info("not find aid. aid={}", aid);
                throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "target user not exist");
            }
            // 喂养或玩耍他人的宠物
            if (!isJoinActivity(activityId, aid)) {
                logger.info("target not join activity. aid={}", aid);
                throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "target not join activity");
            }
            feedOtherPet(activityId, uid, aid, feedType, amount, myInfoVO, petInfoVO);

            ActorData actorData = actorDao.getActorDataFromCache(uid);
            myInfoVO.setMyName(actorData.getName());
            myInfoVO.setMyHead(ImageUrlGenerator.generateRoomUserUrl(actorData.getHead()));
            petInfoVO.setAid(aid);
            petInfoVO.setAidName(aidActorData.getName());
            petInfoVO.setAidHead(ImageUrlGenerator.generateRoomUserUrl(aidActorData.getHead()));
        }
        vo.setMyInfoVO(myInfoVO);
        vo.setPetInfoVO(petInfoVO);
        return vo;
    }

    /**
     * 喂养或玩耍自己的宠物
     */
    private void feedSelfPet(String activityId, String uid, int feedType, int amount,
                             PetFeedVO.MyInfoVO myInfoVO, PetFeedVO.PetInfoVO petInfoVO) {
        String dateStr = getDayByBase(activityId, uid);
        String careerKey = getHashCareerKey(activityId);
        String dailyKey = getHashDayKey(activityId, dateStr);

        synchronized (stringPool.intern(getCareerLockKey(activityId, uid))) {
            PetFeedVO.CareerInfo careerInfo = cacheDataService.getPetFeedVOCareerInfo(careerKey, uid);
            synchronized (stringPool.intern(getDailyLockKey(activityId, uid))) {
                PetFeedVO.DailyInfo dailyInfo = cacheDataService.getPetFeedVODailyInfo(dailyKey, dateStr, uid);
                // 检查宠物状态
                if (careerInfo.getPetStatus() != PET_STATUS_NORMAL) {
                    logger.info("pet not in normal status. uid={}, status={}", uid, careerInfo.getPetStatus());
                    throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "pet not available");
                }

                // 计算宠物年龄阶段
                int ageStage = calculateAgeStage(careerInfo.getEatTotalFood());
                int maxDailyFood = ageStage < AGE_STAGE_FOOD_LIST.size() ? AGE_STAGE_FOOD_LIST.get(ageStage) : AGE_STAGE_FOOD_LIST.get(AGE_STAGE_FOOD_LIST.size() - 1);
                int maxDailyToys = ageStage < AGE_STAGE_TOY_LIST.size() ? AGE_STAGE_TOY_LIST.get(ageStage) : AGE_STAGE_TOY_LIST.get(AGE_STAGE_TOY_LIST.size() - 1);

                List<PetFeedVO.HistoryRecordVO> historyRecords = new ArrayList<>();
                boolean careerUpdated = false;
                boolean dailyUpdated = false;
                int loveReward = 0;
                if (feedType == FEED_TYPE_FOOD) {
                    // 喂养逻辑
                    loveReward = processSelfFeeding(careerInfo, dailyInfo, amount, maxDailyFood, historyRecords);
                    if (loveReward > 0) {
                        careerUpdated = true;
                        dailyUpdated = true;
                        activityCommonRedis.addCommonSetData(getSetDayFeedPetKey(activityId, dateStr), uid);
                    }
                } else if (feedType == FEED_TYPE_TOY) {
                    // 玩耍逻辑
                    loveReward = processSelfPlaying(careerInfo, dailyInfo, amount, maxDailyToys, historyRecords);
                    if (loveReward > 0) {
                        careerUpdated = true;
                        dailyUpdated = true;
                    }
                }

                // 保存数据
                if (careerUpdated) {
                    activityCommonRedis.setCommonHashData(careerKey, uid, JSONObject.toJSONString(careerInfo));
                }
                if (dailyUpdated) {
                    activityCommonRedis.setCommonHashData(dailyKey, uid, JSONObject.toJSONString(dailyInfo));
                }

                // 保存历史记录
                if (!historyRecords.isEmpty()) {
                    leftPushAllHistoryList(uid, historyRecords);
                }

                // 设置返回数据
                setMyInfoVO(myInfoVO, careerInfo, dailyInfo);
                setPetInfoVO(petInfoVO, careerInfo, dailyInfo, ageStage, maxDailyFood, maxDailyToys, loveReward);
            }
        }
    }

    /**
     * 喂养或玩耍他人的宠物
     */
    private void feedOtherPet(String activityId, String uid, String aid, int feedType, int amount,
                              PetFeedVO.MyInfoVO myInfoVO, PetFeedVO.PetInfoVO petInfoVO) {
        String dateStr = getDayByBase(activityId, uid);
        String myCareerKey = getHashCareerKey(activityId);
        String myDailyKey = getHashDayKey(activityId, dateStr);

        String targetCareerKey = getHashCareerKey(activityId);
        String targetDailyKey = getHashDayKey(activityId, dateStr);

        String careerUidLockKey = getCareerLockKey(activityId, uid);
        String dailyAidLockKey = getDailyLockKey(activityId, aid);

        synchronized (stringPool.intern(careerUidLockKey)) {
            synchronized (stringPool.intern(dailyAidLockKey)) {
                // 获取自己的数据
                PetFeedVO.CareerInfo myCareerInfo = cacheDataService.getPetFeedVOCareerInfo(myCareerKey, uid);
                PetFeedVO.DailyInfo myDailyInfo = cacheDataService.getPetFeedVODailyInfo(myDailyKey, dateStr, uid);

                // 获取目标用户的数据
                PetFeedVO.CareerInfo targetCareerInfo = cacheDataService.getPetFeedVOCareerInfo(targetCareerKey, aid);
                PetFeedVO.DailyInfo targetDailyInfo = cacheDataService.getPetFeedVODailyInfo(targetDailyKey, dateStr, aid);

                // 检查目标宠物状态
                if (targetCareerInfo.getPetStatus() != PET_STATUS_NORMAL) {
                    logger.info("target pet not in normal status. aid={}, status={}", aid, targetCareerInfo.getPetStatus());
                    throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "target pet not available");
                }

                // 计算目标宠物年龄阶段
                int targetAgeStage = calculateAgeStage(targetCareerInfo.getEatTotalFood());
                int maxDailyFood = targetAgeStage < AGE_STAGE_FOOD_LIST.size() ? AGE_STAGE_FOOD_LIST.get(targetAgeStage) : AGE_STAGE_FOOD_LIST.get(AGE_STAGE_FOOD_LIST.size() - 1);
                int maxDailyToys = targetAgeStage < AGE_STAGE_TOY_LIST.size() ? AGE_STAGE_TOY_LIST.get(targetAgeStage) : AGE_STAGE_TOY_LIST.get(AGE_STAGE_TOY_LIST.size() - 1);

                List<PetFeedVO.HistoryRecordVO> myHistoryRecords = new ArrayList<>();
                List<PetFeedVO.HistoryRecordVO> targetHistoryRecords = new ArrayList<>();
                boolean myCareerUpdated = false;
                boolean myDailyUpdated = false;
                boolean targetCareerUpdated = false;
                boolean targetDailyUpdated = false;

                int loveReward = 0;
                if (feedType == FEED_TYPE_FOOD) {
                    // 喂养他人宠物逻辑
                    loveReward = processOtherFeeding(uid, aid, myCareerInfo, myDailyInfo, targetCareerInfo, targetDailyInfo,
                            amount, maxDailyFood, myHistoryRecords, targetHistoryRecords);
                    myCareerUpdated = myDailyUpdated = targetCareerUpdated = targetDailyUpdated = true;
                    activityCommonRedis.addCommonSetData(getSetDayFeedPetKey(activityId, dateStr), aid);
                } else if (feedType == FEED_TYPE_TOY) {
                    // 投球他人宠物逻辑
                    loveReward = processOtherPlaying(uid, aid, myCareerInfo, myDailyInfo, targetCareerInfo, targetDailyInfo,
                            amount, maxDailyToys, myHistoryRecords, targetHistoryRecords);
                    myCareerUpdated = myDailyUpdated = targetCareerUpdated = targetDailyUpdated = true;
                }

                // 保存数据
                if (myCareerUpdated) {
                    activityCommonRedis.setCommonHashData(myCareerKey, uid, JSONObject.toJSONString(myCareerInfo));
                    recordMysql(uid, myCareerInfo);
                }
                if (myDailyUpdated) {
                    activityCommonRedis.setCommonHashData(myDailyKey, uid, JSONObject.toJSONString(myDailyInfo));
                }
                if (targetCareerUpdated) {
                    activityCommonRedis.setCommonHashData(targetCareerKey, aid, JSONObject.toJSONString(targetCareerInfo));
                    recordMysql(aid, targetCareerInfo);
                }
                if (targetDailyUpdated) {
                    activityCommonRedis.setCommonHashData(targetDailyKey, aid, JSONObject.toJSONString(targetDailyInfo));
                }

                // 保存历史记录
                if (!myHistoryRecords.isEmpty()) {
                    leftPushAllHistoryList(uid, myHistoryRecords);
                }
                if (!targetHistoryRecords.isEmpty()) {
                    leftPushAllHistoryList(aid, targetHistoryRecords);
                }

                // 设置返回数据
                setMyInfoVO(myInfoVO, myCareerInfo, myDailyInfo);
                setPetInfoVO(petInfoVO, targetCareerInfo, targetDailyInfo, targetAgeStage, maxDailyFood, maxDailyToys, loveReward);
            }
        }
    }

    /**
     * 计算宠物年龄阶段
     *
     * @param eatTotalFood 已吃总食物量
     * @return 年龄阶段 0-幼年 1-成年
     */
    private int calculateAgeStage(int eatTotalFood) {
        if (eatTotalFood < ADULT_PET_FOOD) {
            return 0; // 幼年
        } else {
            return 1; // 成年
        }
    }

    /**
     * 处理自己喂养宠物
     */
    private int processSelfFeeding(PetFeedVO.CareerInfo careerInfo, PetFeedVO.DailyInfo dailyInfo,
                                   int amount, int maxDailyFood, List<PetFeedVO.HistoryRecordVO> historyRecords) {
        int currentEatFood = dailyInfo.getEatFood();
        int remainingFood = maxDailyFood - currentEatFood;

        if (remainingFood <= 0) {
            logger.info("pet already full today. currentEatFood={}, maxDailyFood={}", currentEatFood, maxDailyFood);
            throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "pet already full today");
        }

        if (careerInfo.getFood() < FEED_AMOUNT_NORMAL) {
            logger.info("not enough food to feed. userFood={}, remainingFood={}", careerInfo.getFood(), remainingFood);
            throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "not enough food available");
        }
        int actualFeedAmount;
        if (amount == FEED_AMOUNT_ALL) {
            // 全部喂食
            actualFeedAmount = Math.min(careerInfo.getFood(), remainingFood);
        } else {
            // 正常喂食100g
            // actualFeedAmount = Math.min(FEED_AMOUNT_NORMAL, Math.min(careerInfo.getFood(), remainingFood));
            actualFeedAmount = FEED_AMOUNT_NORMAL;
        }

        if (actualFeedAmount <= 0) {
            logger.info("no food to feed. userFood={}, remainingFood={}", careerInfo.getFood(), remainingFood);
            throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "no food available");
        }

        // 更新数据
        careerInfo.setFood(careerInfo.getFood() - actualFeedAmount);
        careerInfo.setEatTotalFood(careerInfo.getEatTotalFood() + actualFeedAmount);
        dailyInfo.setEatFood(dailyInfo.getEatFood() + actualFeedAmount);
        dailyInfo.setIsDoneMyPetFood(1);

        // 计算爱心奖励：每100g获得1爱心
        int loveReward = (actualFeedAmount / 100) * FEED_SELF_PET_LOVE;
        if (loveReward > 0) {
            careerInfo.setLove(careerInfo.getLove() + loveReward);
        }

        // 添加历史记录
        PetFeedVO.HistoryRecordVO record = new PetFeedVO.HistoryRecordVO();
        record.setActionType(ACTION_TYPE_FEED_SELF);
        record.setNum(actualFeedAmount);
        record.setLove(loveReward);
        record.setCtime(DateHelper.getNowSeconds());
        historyRecords.add(record);

        logger.info("自己喂养宠物成功. actualFeedAmount={}, loveReward={}", actualFeedAmount, loveReward);
        return loveReward;
    }

    /**
     * 处理自己投球宠物
     */
    private int processSelfPlaying(PetFeedVO.CareerInfo careerInfo, PetFeedVO.DailyInfo dailyInfo,
                                   int amount, int maxDailyToys, List<PetFeedVO.HistoryRecordVO> historyRecords) {
        int currentPlayToys = dailyInfo.getPlayToys();
        int remainingToys = maxDailyToys - currentPlayToys;

        if (remainingToys <= 0) {
            logger.info("pet already played enough today. currentPlayToys={}, maxDailyToys={}", currentPlayToys, maxDailyToys);
            throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "pet already played enough today");
        }
        if (careerInfo.getToys() < TOY_AMOUNT_NORMAL) {
            logger.info("not enough toys to play. userToys={}, remainingToys={}", careerInfo.getToys(), remainingToys);
            throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "not enough toys available");
        }

        // int actualToyAmount = Math.min(TOY_AMOUNT_NORMAL, Math.min(careerInfo.getToys(), remainingToys));

        int actualToyAmount = TOY_AMOUNT_NORMAL;

        if (actualToyAmount <= 0) {
            logger.info("no toys to play. userToys={}, remainingToys={}", careerInfo.getToys(), remainingToys);
            throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "no toys available");
        }

        // 更新数据
        careerInfo.setToys(careerInfo.getToys() - actualToyAmount);
        dailyInfo.setPlayToys(dailyInfo.getPlayToys() + actualToyAmount);

        // 计算爱心奖励：每次获得5爱心
        int loveReward = actualToyAmount * THROW_BALL_SELF_PET_LOVE;
        careerInfo.setLove(careerInfo.getLove() + loveReward);

        // 添加历史记录
        PetFeedVO.HistoryRecordVO record = new PetFeedVO.HistoryRecordVO();
        record.setActionType(ACTION_TYPE_TOY_SELF);
        record.setNum(actualToyAmount);
        record.setLove(loveReward);
        record.setCtime(DateHelper.getNowSeconds());
        historyRecords.add(record);

        logger.info("自己投球宠物成功. actualToyAmount={}, loveReward={}", actualToyAmount, loveReward);
        return loveReward;
    }

    /**
     * 处理喂养他人宠物
     */
    private int processOtherFeeding(String uid, String aid, PetFeedVO.CareerInfo myCareerInfo, PetFeedVO.DailyInfo myDailyInfo,
                                    PetFeedVO.CareerInfo targetCareerInfo, PetFeedVO.DailyInfo targetDailyInfo,
                                    int amount, int maxDailyFood, List<PetFeedVO.HistoryRecordVO> myHistoryRecords,
                                    List<PetFeedVO.HistoryRecordVO> targetHistoryRecords) {
        // 检查自己今日喂养他人宠物的限制
        int myFeedOtherPetFood = myDailyInfo.getFeedOtherPetFood();
        int remainingFeedOther = MAX_FEED_OTHER_PET - myFeedOtherPetFood;

        if (remainingFeedOther <= 0) {
            logger.info("已达到今日喂养他人宠物上限. uid={}, feedOtherPetFood={}", uid, myFeedOtherPetFood);
            throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "daily limit reached for feeding others");
        }

        // 检查目标宠物今日可接受的食物量
        int targetCurrentEatFood = targetDailyInfo.getEatFood();
        int targetRemainingFood = maxDailyFood - targetCurrentEatFood;

        if (targetRemainingFood <= 0) {
            logger.info("目标宠物今日已吃饱. aid={}, currentEatFood={}, maxDailyFood={}", aid, targetCurrentEatFood, maxDailyFood);
            throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "target pet already full today");
        }

        // 检查自己是否有足够的食物
        if (myCareerInfo.getFood() < FEED_AMOUNT_NORMAL) {
            logger.info("not enough food to feed. userFood={}, remainingFeedOther={}, targetRemainingFood={}",
                    myCareerInfo.getFood(), remainingFeedOther, targetRemainingFood);
            throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "not enough food available");
        }

        // 计算实际喂养量
        // int actualFeedAmount = Math.min(FEED_AMOUNT_NORMAL,
        //         Math.min(myCareerInfo.getFood(),
        //                 Math.min(remainingFeedOther, targetRemainingFood)));

        int actualFeedAmount = FEED_AMOUNT_NORMAL;

        if (actualFeedAmount <= 0) {
            logger.info("无法喂养他人宠物. userFood={}, remainingFeedOther={}, targetRemainingFood={}",
                    myCareerInfo.getFood(), remainingFeedOther, targetRemainingFood);
            throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "cannot feed other pet");
        }

        // 更新自己的数据
        myCareerInfo.setFood(myCareerInfo.getFood() - actualFeedAmount);
        myDailyInfo.setFeedOtherPetFood(myDailyInfo.getFeedOtherPetFood() + actualFeedAmount);

        // 更新目标宠物的数据
        targetCareerInfo.setEatTotalFood(targetCareerInfo.getEatTotalFood() + actualFeedAmount);
        targetDailyInfo.setEatFood(targetDailyInfo.getEatFood() + actualFeedAmount);

        // 计算爱心奖励
        // 自己：累积500g获得1爱心
        int newFeedOtherTotal = myDailyInfo.getFeedOtherPetFood();
        int myLoveReward = 0;
        if (newFeedOtherTotal == MAX_FEED_OTHER_PET) {
            myLoveReward = FEED_OTHER_PET_LOVE;
            myCareerInfo.setLove(myCareerInfo.getLove() + myLoveReward);
        }

        // 目标用户：每100g获得1爱心
        int targetLoveReward = (actualFeedAmount / 100) * FEED_SELF_PET_LOVE;
        if (targetLoveReward > 0) {
            targetCareerInfo.setLove(targetCareerInfo.getLove() + targetLoveReward);
        }

        // 获取用户昵称
        // String aidName = getUserName(aid);
        // String uidName = getUserName(uid);
        String aidName = "";
        String uidName = "";

        // 添加历史记录
        PetFeedVO.HistoryRecordVO myRecord = new PetFeedVO.HistoryRecordVO();
        myRecord.setActionType(ACTION_TYPE_FEED_OTHER);
        myRecord.setNum(actualFeedAmount);
        myRecord.setAid(aid);
        myRecord.setName(aidName);
        myRecord.setLove(myLoveReward);
        myRecord.setCtime(DateHelper.getNowSeconds());
        myHistoryRecords.add(myRecord);
        if (myLoveReward > 0) {
            PetFeedVO.HistoryRecordVO myRecord2 = new PetFeedVO.HistoryRecordVO();
            myRecord2.setActionType(ACTION_TYPE_FEED_OTHER_GOAL);
            myRecord2.setNum(actualFeedAmount);
            myRecord2.setAid(aid);
            myRecord2.setName(aidName);
            myRecord2.setLove(myLoveReward);
            myRecord2.setCtime(DateHelper.getNowSeconds());
            myHistoryRecords.add(myRecord2);
        }


        PetFeedVO.HistoryRecordVO targetRecord = new PetFeedVO.HistoryRecordVO();
        targetRecord.setActionType(ACTION_TYPE_OTHER_FEED_ME);
        targetRecord.setNum(actualFeedAmount);
        targetRecord.setAid(uid);
        targetRecord.setName(uidName);
        targetRecord.setLove(targetLoveReward);
        targetRecord.setCtime(DateHelper.getNowSeconds());
        targetHistoryRecords.add(targetRecord);

        logger.info("喂养他人宠物成功. uid={}, aid={}, actualFeedAmount={}, myLoveReward={}, targetLoveReward={}",
                uid, aid, actualFeedAmount, myLoveReward, targetLoveReward);
        return targetLoveReward;
    }

    /**
     * 处理投球他人宠物
     */
    private int processOtherPlaying(String uid, String aid, PetFeedVO.CareerInfo myCareerInfo, PetFeedVO.DailyInfo myDailyInfo,
                                    PetFeedVO.CareerInfo targetCareerInfo, PetFeedVO.DailyInfo targetDailyInfo,
                                    int amount, int maxDailyToys, List<PetFeedVO.HistoryRecordVO> myHistoryRecords,
                                    List<PetFeedVO.HistoryRecordVO> targetHistoryRecords) {
        // 检查自己今日投球他人宠物的限制
        int myThrowBallOtherPet = myDailyInfo.getThrowBallOtherPet();

        if (myThrowBallOtherPet >= MAX_THROW_BALL_OTHER_PET) {
            logger.info("已达到今日投球他人宠物上限. uid={}, throwBallOtherPet={}", uid, myThrowBallOtherPet);
            throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "daily limit reached for playing with others");
        }

        // 检查目标宠物今日可接受的玩耍次数
        int targetCurrentPlayToys = targetDailyInfo.getPlayToys();
        int targetRemainingToys = maxDailyToys - targetCurrentPlayToys;

        if (targetRemainingToys <= 0) {
            logger.info("目标宠物今日已玩够. aid={}, currentPlayToys={}, maxDailyToys={}", aid, targetCurrentPlayToys, maxDailyToys);
            throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "target pet already played enough today");
        }

        // 检查自己是否有足够的玩具
        if (myCareerInfo.getToys() < TOY_AMOUNT_NORMAL) {
            logger.info("not enough toys to play. userToys={}, remainingThrowBall={}, targetRemainingToys={}",
                    myCareerInfo.getToys(), MAX_THROW_BALL_OTHER_PET - myThrowBallOtherPet, targetRemainingToys);
            throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "not enough toys available");
        }

        // 计算实际投球量
        // int actualToyAmount = Math.min(TOY_AMOUNT_NORMAL,
        //         Math.min(myCareerInfo.getToys(),
        //                 Math.min(MAX_THROW_BALL_OTHER_PET - myThrowBallOtherPet, targetRemainingToys)));

        int actualToyAmount = TOY_AMOUNT_NORMAL;

        if (actualToyAmount <= 0) {
            logger.info("无法投球他人宠物. userToys={}, remainingThrowBall={}, targetRemainingToys={}",
                    myCareerInfo.getToys(), MAX_THROW_BALL_OTHER_PET - myThrowBallOtherPet, targetRemainingToys);
            throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "cannot play with other pet");
        }

        // 更新自己的数据
        myCareerInfo.setToys(myCareerInfo.getToys() - actualToyAmount);
        myDailyInfo.setThrowBallOtherPet(myDailyInfo.getThrowBallOtherPet() + actualToyAmount);

        // 更新目标宠物的数据
        targetDailyInfo.setPlayToys(targetDailyInfo.getPlayToys() + actualToyAmount);

        // 计算爱心奖励
        // 自己：每次获得1爱心
        int myLoveReward = actualToyAmount * THROW_BALL_OTHER_PET_LOVE;
        myCareerInfo.setLove(myCareerInfo.getLove() + myLoveReward);

        // 目标用户：每次获得5爱心
        int targetLoveReward = actualToyAmount * THROW_BALL_SELF_PET_LOVE;
        targetCareerInfo.setLove(targetCareerInfo.getLove() + targetLoveReward);

        // 获取用户昵称
        String aidName = "";
        String uidName = "";

        // 添加历史记录
        PetFeedVO.HistoryRecordVO myRecord = new PetFeedVO.HistoryRecordVO();
        myRecord.setActionType(ACTION_TYPE_TOY_OTHER_GOAL); // 投球他人达成目标（因为每次都有奖励）
        myRecord.setNum(actualToyAmount);
        myRecord.setAid(aid);
        myRecord.setName(aidName);
        myRecord.setLove(myLoveReward);
        myRecord.setCtime(DateHelper.getNowSeconds());
        myHistoryRecords.add(myRecord);

        PetFeedVO.HistoryRecordVO targetRecord = new PetFeedVO.HistoryRecordVO();
        targetRecord.setActionType(ACTION_TYPE_OTHER_TOY_ME);
        targetRecord.setNum(actualToyAmount);
        targetRecord.setAid(uid);
        targetRecord.setName(uidName);
        targetRecord.setLove(targetLoveReward);
        targetRecord.setCtime(DateHelper.getNowSeconds());
        targetHistoryRecords.add(targetRecord);

        logger.info("投球他人宠物成功. uid={}, aid={}, actualToyAmount={}, myLoveReward={}, targetLoveReward={}",
                uid, aid, actualToyAmount, myLoveReward, targetLoveReward);
        return targetLoveReward;
    }

    /**
     * 设置我的信息VO
     */
    private void setMyInfoVO(PetFeedVO.MyInfoVO myInfoVO, PetFeedVO.CareerInfo careerInfo, PetFeedVO.DailyInfo dailyInfo) {
        myInfoVO.setFood(careerInfo.getFood());
        myInfoVO.setToys(careerInfo.getToys());
        myInfoVO.setLove(careerInfo.getLove());
        myInfoVO.setSignDate(careerInfo.getSignDate());
        myInfoVO.setSignDay(careerInfo.getSignDay());
        myInfoVO.setSignReminderStatus(careerInfo.getSignReminderStatus());
        myInfoVO.setIsDoneMyPetFood(dailyInfo.getIsDoneMyPetFood());
        myInfoVO.setHavePet(careerInfo.getPetStatus() == PET_STATUS_NORMAL ? 1 : 0);
    }

    /**
     * 设置宠物信息VO
     */
    private void setPetInfoVO(PetFeedVO.PetInfoVO petInfoVO, PetFeedVO.CareerInfo careerInfo, PetFeedVO.DailyInfo dailyInfo,
                              int ageStage, int maxDailyFood, int maxDailyToys, int loveReward) {
        petInfoVO.setPetName(careerInfo.getPetName());
        petInfoVO.setPetType(careerInfo.getPetType());
        petInfoVO.setEatTotalFood(careerInfo.getEatTotalFood());
        petInfoVO.setCtime(careerInfo.getCtime());
        petInfoVO.setEatFood(dailyInfo.getEatFood());
        petInfoVO.setPlayToys(dailyInfo.getPlayToys());
        petInfoVO.setAgeStage(ageStage);
        petInfoVO.setNeedFood(maxDailyFood);
        petInfoVO.setNeedToys(maxDailyToys);
        petInfoVO.setAddLove(loveReward);

        // 计算陪伴天数
        int nowSeconds = DateHelper.getNowSeconds();
        int accompanyDay = (nowSeconds - careerInfo.getCtime()) / (24 * 60 * 60) + 1;
        petInfoVO.setAccompanyDay(Math.max(1, accompanyDay));
    }

    /**
     * 获取用户昵称
     */
    private String getUserName(String uid) {
        try {
            ActorData actorData = actorDao.getActorDataFromCache(uid);
            return actorData != null ? actorData.getName() : "";
        } catch (Exception e) {
            logger.error("获取用户昵称失败. uid={}, error={}", uid, e.getMessage());
            return "";
        }
    }


    public void releasePet(String activityId, String uid) {
        checkActivityTime(activityId);
        if (!isJoinActivity(activityId, uid)) {
            logger.info("not join activity. uid={}", uid);
            throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "pet not found");
        }
        PetFeedData petFeedData = petFeedDao.selectByUid(uid);
        if (petFeedData == null || petFeedData.getPetStatus() == PET_STATUS_RELEASE) {
            logger.info("pet not found. uid={}", uid);
            throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "pet not found");
        }
        String careerKey = getHashCareerKey(activityId);
        synchronized (stringPool.intern(getCareerLockKey(activityId, uid))) {
            PetFeedVO.CareerInfo careerInfo = cacheDataService.getPetFeedVOCareerInfo(careerKey, uid);
            if (careerInfo.getEatTotalFood() < ADULT_PET_FOOD) {
                logger.info("pet is not adult. uid={}", uid); // 不能放养幼年期的宠物
                throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "pet is not adult");
            }
            careerInfo.setPetStatus(PET_STATUS_RELEASE);
            activityCommonRedis.setCommonHashData(careerKey, uid, JSONObject.toJSONString(careerInfo));
        }
        petFeedData.setPetStatus(PET_STATUS_RELEASE);
        petFeedData.setMtime(DateHelper.getNowSeconds());
        petFeedDao.updateOne(petFeedData);
        activityCommonRedis.removeCommonSetData(getSetAllFeedPetKey(activityId), uid);
    }

    public void exchange(String activityId, String uid, String metaId) {
        if (!inActivityTime(activityId)) {
            throw new CommonH5Exception(ActivityHttpCode.NOT_ACTIVE_TIME);
        }

        if (StringUtils.isEmpty(metaId)) {
            logger.info("metaId is empty. uid={} metaId={}", uid, metaId);
            throw new CommonH5Exception(HttpCode.PARAM_ERROR);
        }
        // 获取兑换商品配置
        ResourceKeyConfigData keyConfigData = resourceKeyHandlerService.getConfigData(CRASH_SHOP_DRAW_KEY);
        ResourceKeyConfigData.ResourceMeta targetMeta = null;

        if (keyConfigData != null && !CollectionUtils.isEmpty(keyConfigData.getResourceMetaList())) {
            for (ResourceKeyConfigData.ResourceMeta meta : keyConfigData.getResourceMetaList()) {
                if (metaId.equals(meta.getMetaId())) {
                    targetMeta = meta;
                    break;
                }
            }
        }

        if (targetMeta == null) {
            logger.info("targetMeta is null. uid={} metaId={}", uid, metaId);
            throw new CommonH5Exception(ActivityHttpCode.INCORRECT_INPUT_ID);
        }
        int requiredPoints = Integer.parseInt(targetMeta.getRateNumber());
        synchronized (stringPool.intern(getCareerLockKey(activityId, uid))) {
            String careerKey = getHashCareerKey(activityId);
            PetFeedVO.CareerInfo careerInfo = cacheDataService.getPetFeedVOCareerInfo(careerKey, uid);
            int currentPoints = careerInfo.getLove();


            if (currentPoints < requiredPoints) {
                throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "love not enough");
            }

            // 扣除积分
            careerInfo.setLove(currentPoints - requiredPoints);
            activityCommonRedis.setCommonHashData(careerKey, uid, JSONObject.toJSONString(careerInfo));
            recordMysql(uid, careerInfo);
        }
        // 发放奖励
        resourceKeyHandlerService.sendOneResourceData(uid, targetMeta, 905,
                eventExchangeTitle, eventExchangeTitle, eventExchangeTitle, ACTIVITY_URL, "", 1);

        // 保存兑换历史
        PetFeedVO.HistoryRecordVO historyMeta = new PetFeedVO.HistoryRecordVO();
        historyMeta.setActionType(ACTION_TYPE_SHOP_EXCHANGE);
        historyMeta.setLove(requiredPoints);
        historyMeta.setName(targetMeta.getResourceNameAr());
        historyMeta.setCtime(DateHelper.getNowSeconds());
        leftPushAllHistoryList(uid, Arrays.asList(historyMeta));
        doReportSpecialItemsEvent(ACTIVITY_ID, uid, 2, 2, requiredPoints);
    }


    public PetFeedVO.HistoryRecordPageVO getHistoryListPageRecord(String activityId, String uid, int page) {
        PetFeedVO.HistoryRecordPageVO vo = new PetFeedVO.HistoryRecordPageVO();
        int start = (page - 1) * HISTORY_PAGE_SIZE;
        int end = page * HISTORY_PAGE_SIZE;
        String key = getListHistoryKey(activityId, uid);
        List<String> jsonList = activityCommonRedis.getCommonListPageRecord(key, start, end);
        List<PetFeedVO.HistoryRecordVO> resultList = new ArrayList<>();
        for (String json : jsonList) {
            PetFeedVO.HistoryRecordVO rewardData = JSON.parseObject(json, PetFeedVO.HistoryRecordVO.class);
            if (!StringUtils.isEmpty(rewardData.getAid()) && StringUtils.isEmpty(rewardData.getName())) {
                rewardData.setName(getUserName(rewardData.getAid()));
            }
            resultList.add(rewardData);
        }
        vo.setHistoryRecordList(resultList);
        vo.setNextPage(resultList.size() < HISTORY_PAGE_SIZE ? -1 : page + 1);
        return vo;
    }

    public void setShareSnapchat(String activityId, String uid) {
        checkActivityTime(activityId);
        if (!isJoinActivity(activityId, uid)) {
            logger.info("not join activity. uid={}", uid);
            throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "not join activity");
        }
        String dateStr = getDayByBase(ACTIVITY_ID, uid);
        String dailyTaskKey = getHashDayTaskKey(ACTIVITY_ID, dateStr);
        PetFeedVO.DailyTaskInfo dailyTaskInfo = cacheDataService.getPetFeedVODailyTaskInfo(dailyTaskKey, dateStr, uid);
        if (dailyTaskInfo.getShareSnapchatStatus() == 1) {
            logger.info("already share snapchat. uid={}", uid);
            throw new CommonH5Exception(HttpCode.PARAM_ERROR.getCode(), "already share snapchat");
        }
        String careerKey = getHashCareerKey(ACTIVITY_ID);
        synchronized (stringPool.intern(getCareerLockKey(ACTIVITY_ID, uid))) {
            dailyTaskInfo.setShareSnapchatStatus(1);
            activityCommonRedis.setCommonHashData(dailyTaskKey, dateStr, JSONObject.toJSONString(dailyTaskInfo));

            PetFeedVO.CareerInfo careerInfo = cacheDataService.getPetFeedVOCareerInfo(careerKey, uid);
            careerInfo.setFood(careerInfo.getFood() + SHARE_SNAPCHAT_FOOD_REWARD);
            activityCommonRedis.setCommonHashData(careerKey, uid, JSONObject.toJSONString(careerInfo));
            recordMysql(uid, careerInfo);
        }
    }

    @Override
    public void taskMsgProcess(CommonMqTopicData data) {
        String uid = data.getUid();
        String aid = data.getAid();
        String roomId = data.getRoomId();
        String item = data.getItem();

        if (!TASK_ITEM_LIST.contains(item)) {
            return;
        }
        if (otherActivityService.getOtherRankingActivityNull(ACTIVITY_ID) == null) {
            return;
        }
        if (!inActivityTime(ACTIVITY_ID)) {
            // 临界时间处理
            return;
        }

        if (!checkAc(uid)) {
            return;
        }
        syncAddFoodOrToys(uid, data, null, null);
    }

    @Override
    public void process(RechargeInfo rechargeInfo) {
        if (rechargeInfo == null || !StringUtils.hasLength(rechargeInfo.getUid()) || rechargeInfo.getRechargeMoney() == null) {
            return;
        }
        int rechargeType = rechargeInfo.getRechargeType();
        if (rechargeType != 1 || rechargeInfo.getRechargeDiamond() <= 0) {
            return;
        }
        if (otherActivityService.getOtherRankingActivityNull(ACTIVITY_ID) == null) {
            return;
        }
        if (!inActivityTime(ACTIVITY_ID)) {
            // 临界时间处理
            return;
        }
        String uid = rechargeInfo.getUid();
        if (!checkAc(uid)) {
            return;
        }
        syncAddFoodOrToys(uid, null, null, rechargeInfo);
    }

    public void sendGiftHandle(SendGiftData giftData, String activityId) {
        if (giftData.getFrom_uid() == null) {
            return;
        }
        syncAddFoodOrToys(giftData.getFrom_uid(), null, giftData, null);
        for (String toUid : giftData.getAid_list()) {
            syncAddFoodOrToys(toUid, null, giftData, null, true);
        }
    }

    private void syncAddFoodOrToys(String uid, CommonMqTopicData data, SendGiftData giftData, RechargeInfo rechargeInfo) {
        syncAddFoodOrToys(uid, data, giftData, rechargeInfo, false);
    }

    private void syncAddFoodOrToys(String uid, CommonMqTopicData data, SendGiftData giftData, RechargeInfo rechargeInfo, boolean isReceiveGift) {
        if (!isJoinActivity(uid, ACTIVITY_ID)) {
            return;
        }
        String dateStr = getDayByBase(ACTIVITY_ID, uid);
        String careerKey = getHashCareerKey(ACTIVITY_ID);
        String dailyTaskKey = getHashDayTaskKey(ACTIVITY_ID, dateStr);

        synchronized (stringPool.intern(getCareerLockKey(ACTIVITY_ID, uid))) {
            PetFeedVO.CareerInfo careerInfo = cacheDataService.getPetFeedVOCareerInfo(careerKey, uid);
            PetFeedVO.DailyTaskInfo dailyTaskInfo = cacheDataService.getPetFeedVODailyTaskInfo(dailyTaskKey, dateStr, uid);

            boolean careerInfoUpdated = false;
            boolean dailyTaskInfoUpdated = false;

            // 处理基础任务 - 获得食物
            if (data != null) {
                String item = data.getItem();
                if (CommonMqTaskConstant.ON_MIC_TIME_5_MINUTE.equals(item)) {
                    // 上麦5分钟任务
                    if (dailyTaskInfo.getOnMic5Status() == 0) {
                        dailyTaskInfo.setOnMic5Status(1);
                        careerInfo.setFood(careerInfo.getFood() + ON_MIC_5_MINUTE_FOOD_REWARD);
                        careerInfoUpdated = true;
                        dailyTaskInfoUpdated = true;
                        logger.info("用户完成上麦5分钟任务，获得{}g食物. uid={}", ON_MIC_5_MINUTE_FOOD_REWARD, uid);
                    }
                } else if (CommonMqTaskConstant.INVITE_BIND_USER.equals(item)) {
                    // 邀请新用户注册
                    int previousInviteNewUser = dailyTaskInfo.getInviteNewUser();
                    if (previousInviteNewUser >= MAX_ADVANCE_TASK_UNITS) {
                        return;
                    }
                    dailyTaskInfo.setInviteNewUser(previousInviteNewUser + 1);

                    // 计算玩具增量：每邀请1个新用户获得1个玩具，最多5个
                    int previousToys = Math.min(previousInviteNewUser, MAX_ADVANCE_TASK_UNITS);
                    int currentToys = Math.min(dailyTaskInfo.getInviteNewUser(), MAX_ADVANCE_TASK_UNITS);
                    int toyIncrement = currentToys - previousToys;

                    if (toyIncrement > 0) {
                        careerInfo.setToys(careerInfo.getToys() + TOY_REWARD_PER_TASK);
                        careerInfoUpdated = true;
                        logger.info("用户邀请新用户注册，获得玩具. uid={}, 新用户数={}, 获得玩具={}", uid, dailyTaskInfo.getInviteNewUser(), toyIncrement);
                    }
                    dailyTaskInfoUpdated = true;
                } else if (INVITE_BIND_BACK_USER.equals(item)) {
                    // 召回老朋友回归
                    int previousInviteOldUser = dailyTaskInfo.getInviteOldUser();
                    if (previousInviteOldUser >= MAX_ADVANCE_TASK_UNITS) {
                        return;
                    }
                    dailyTaskInfo.setInviteOldUser(previousInviteOldUser + 1);

                    // 计算玩具增量：每召回1个老朋友获得1个玩具，最多5个
                    int previousToys = Math.min(previousInviteOldUser, MAX_ADVANCE_TASK_UNITS);
                    int currentToys = Math.min(dailyTaskInfo.getInviteOldUser(), MAX_ADVANCE_TASK_UNITS);
                    int toyIncrement = currentToys - previousToys;

                    if (toyIncrement > 0) {
                        careerInfo.setToys(careerInfo.getToys() + TOY_REWARD_PER_TASK);
                        careerInfoUpdated = true;
                        logger.info("用户召回老朋友回归，获得玩具. uid={}, 老朋友数={}, 获得玩具={}", uid, dailyTaskInfo.getInviteOldUser(), toyIncrement);
                    }
                    dailyTaskInfoUpdated = true;
                }
            }

            // 处理送礼任务 - 获得食物
            if (giftData != null) {
                if (isReceiveGift) {
                    int previousUnits = (dailyTaskInfo.getReceiveGiftDiamond()) / GIFT_DIAMOND_UNIT;
                    int previousMaxUnits = Math.min(previousUnits, MAX_GIFT_UNITS);
                    if (previousMaxUnits >= MAX_GIFT_UNITS) {
                        return;
                    }

                    // 处理收礼任务 - 获得食物
                    int giftPrice = giftData.getPrice() * giftData.getNumber();
                    dailyTaskInfo.setReceiveGiftDiamond(dailyTaskInfo.getReceiveGiftDiamond() + giftPrice);

                    // 每500钻获得50g食物
                    int receiveGiftUnits = dailyTaskInfo.getReceiveGiftDiamond() / GIFT_DIAMOND_UNIT;
                    int actualReceiveGiftUnits = Math.min(receiveGiftUnits, MAX_GIFT_UNITS);

                    // 计算增量食物
                    int foodIncrement = (actualReceiveGiftUnits - previousMaxUnits) * GIFT_FOOD_RECEIVE_REWARD_PER_UNIT;

                    if (foodIncrement > 0) {
                        careerInfo.setFood(careerInfo.getFood() + foodIncrement);
                        careerInfoUpdated = true;
                        logger.info("用户收礼获得食物. uid={}, 收礼钻石={}, 获得食物={}", uid, giftPrice, foodIncrement);
                    }
                    dailyTaskInfoUpdated = true;

                } else {

                    int previousUnits = (dailyTaskInfo.getSendGiftDiamond()) / GIFT_DIAMOND_UNIT;
                    int previousMaxUnits = Math.min(previousUnits, MAX_GIFT_UNITS);
                    if (previousMaxUnits >= MAX_GIFT_UNITS) {
                        return;
                    }
                    int num = giftData.getAid_list().size();
                    int giftPrice = giftData.getPrice() * giftData.getNumber() * num;
                    dailyTaskInfo.setSendGiftDiamond(dailyTaskInfo.getSendGiftDiamond() + giftPrice);
                    // 每500钻获得50g食物
                    int sendGiftUnits = dailyTaskInfo.getSendGiftDiamond() / GIFT_DIAMOND_UNIT;
                    int actualSendGiftUnits = Math.min(sendGiftUnits, MAX_GIFT_UNITS);
                    // 计算增量食物
                    int foodIncrement = (actualSendGiftUnits - previousMaxUnits) * GIFT_FOOD_SEND_REWARD_PER_UNIT;

                    if (foodIncrement > 0) {
                        careerInfo.setFood(careerInfo.getFood() + foodIncrement);
                        careerInfoUpdated = true;
                        logger.info("用户送礼获得食物. uid={}, 送礼钻石={}, 获得食物={}", uid, giftPrice, foodIncrement);
                    }
                    dailyTaskInfoUpdated = true;
                }
            }

            // 处理充值任务 - 获得玩具
            if (rechargeInfo != null && rechargeInfo.getRechargeDiamond() != null && rechargeInfo.getRechargeDiamond() >= RECHARGE_DIAMOND_THRESHOLD) {
                int previousToys = Math.min(dailyTaskInfo.getRechargeTimes(), MAX_ADVANCE_TASK_UNITS);
                if (previousToys >= MAX_ADVANCE_TASK_UNITS) {
                    return;
                }
                dailyTaskInfo.setRechargeTimes(dailyTaskInfo.getRechargeTimes() + 1);
                int rechargeToys = Math.min(dailyTaskInfo.getRechargeTimes(), MAX_ADVANCE_TASK_UNITS);
                int toyIncrement = rechargeToys - previousToys;

                if (toyIncrement > 0) {
                    careerInfo.setToys(careerInfo.getToys() + toyIncrement);
                    careerInfoUpdated = true;
                    logger.info("用户充值获得玩具. uid={}, 充值钻石={}, 获得玩具={}", uid, rechargeInfo.getRechargeDiamond(), toyIncrement);
                }
                dailyTaskInfoUpdated = true;
            }

            // 保存更新的数据
            if (careerInfoUpdated) {
                activityCommonRedis.setCommonHashData(careerKey, uid, JSONObject.toJSONString(careerInfo));
                recordMysql(uid, careerInfo);
            }
            if (dailyTaskInfoUpdated) {
                activityCommonRedis.setCommonHashData(dailyTaskKey, uid, JSONObject.toJSONString(dailyTaskInfo));
            }
        }
    }

    private boolean checkAc(String uid) {
        OtherRankingActivityData activityData = getOtherRankingActivityNull(ACTIVITY_ID);
        if (ServerConfig.isProduct() && activityData.getAcNameEn().startsWith("test")) {
            boolean isWhiteTest = whiteTestDao.isMemberByType(uid, WhiteTestDao.WHITE_TYPE_RID);
            if (!isWhiteTest) {
                // 灰度测试,只统计测试用户的
                return false;
            }
        }
        return true;
    }

    // 保存历史记录
    private void leftPushAllHistoryList(String uid, List<PetFeedVO.HistoryRecordVO> srcList) {
        String historyKey = getListHistoryKey(ACTIVITY_ID, uid);
        List<String> strList = new ArrayList<>();
        for (PetFeedVO.HistoryRecordVO meta : srcList) {
            String json = JSONObject.toJSONString(meta);
            strList.add(json);
        }
        if (CollectionUtils.isEmpty(strList)) {
            return;
        }
        activityCommonRedis.leftPushAllCommonList(historyKey, strList, HISTORY_USER_MAX_SIZE);
    }

    private boolean isJoinActivity(String activityId, String uid) {
        String allFeedPetKey = getSetAllFeedPetKey(activityId);
        return activityCommonRedis.isCommonSetData(allFeedPetKey, uid) > 0;
    }


    public void distributionRanking(String activityId) {
        BaseTaskFactory.getFactory().addSlow(new Task() {
            @Override
            protected void execute() {
                distributionRankingToMysql(activityId);
            }
        });

    }

    /**
     * 个人数据写mysql做持久化
     *
     * @param activityId
     */
    private void distributionRankingToMysql(String activityId) {
        try {
            Map<String, String> hashMap = activityCommonRedis.getCommonHashAllMapStr(getHashCareerKey(activityId));
            for (Map.Entry<String, String> entry : hashMap.entrySet()) {
                String uid = entry.getKey();
                PetFeedVO.CareerInfo careerInfo = JSON.parseObject(entry.getValue(), PetFeedVO.CareerInfo.class);
                recordMysql(uid, careerInfo);
            }
        } catch (Exception e) {
            logger.error("distributionRanking error: {}", e.getMessage(), e);
        }
    }

    private void recordMysql(String uid, PetFeedVO.CareerInfo careerInfo) {
        PetFeedData petFeedData = new PetFeedData();
        petFeedData.setUid(uid);
        petFeedData.setEatTotalFood(careerInfo.getEatTotalFood());
        petFeedData.setFood(careerInfo.getFood());
        petFeedData.setToys(careerInfo.getToys());
        petFeedData.setLove(careerInfo.getLove());
        petFeedData.setMtime(DateHelper.getNowSeconds());
        petFeedDao.updateByUid(petFeedData);
    }

    private void handleRes(String aid, String resKey, String eventTitle) {
        resourceKeyHandlerService.sendResourceData(aid, resKey,
                eventTitle, eventTitle, eventTitle, ACTIVITY_URL, "");
    }

    /**
     * 上报机会获取消耗事件
     */
    private void doReportSpecialItemsEvent(String activityId, String uid, int action, int source, int num) {
        ActivitySpecialItemsChangeEvent event = new ActivitySpecialItemsChangeEvent();
        event.setUid(uid);
        event.setCtime(DateHelper.getNowSeconds());
        event.setChange_action(action);
        event.setActivity_special_items_id("0");
        event.setActivity_name(ACTIVITY_TITLE_EN);
        event.setActive_id(activityId);
        event.setActivity_special_items_resource(source);
        event.setResource_desc("");
        event.setChange_nums(num);
        eventReport.track(new EventDTO(event));
    }


}
