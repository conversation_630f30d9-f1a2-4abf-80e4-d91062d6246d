package com.quhong.service;

import com.quhong.analysis.CreateGameLogEvent;
import com.quhong.analysis.EventDTO;
import com.quhong.analysis.EventReport;
import com.quhong.analysis.GameRecordLogEvent;
import com.quhong.constant.AchieveBadgeConstant;
import com.quhong.constant.MoneyTypeConstant;
import com.quhong.core.concurrency.BaseTaskFactory;
import com.quhong.core.concurrency.queues.SlowTaskQueue;
import com.quhong.core.concurrency.tasks.Task;
import com.quhong.core.config.ServerConfig;
import com.quhong.core.distribution.DistributeLock;
import com.quhong.core.timers.DelayTask;
import com.quhong.core.timers.LoopTask;
import com.quhong.core.timers.TimerService;
import com.quhong.core.utils.DateHelper;
import com.quhong.dailyTask.CommonTaskService;
import com.quhong.dailyTask.DailyTaskService;
import com.quhong.data.ActorData;
import com.quhong.data.CommonMqTopicData;
import com.quhong.data.DailyTaskMqData;
import com.quhong.data.FingerGuessInfo;
import com.quhong.data.dto.MoneyDetailReq;
import com.quhong.data.dto.MoneyTypeDTO;
import com.quhong.dto.FingerGuessDTO;
import com.quhong.enums.*;
import com.quhong.exception.GameException;
import com.quhong.feign.DataCenterService;
import com.quhong.image.ImageUrlGenerator;
import com.quhong.mongo.dao.ActorConfigDao;
import com.quhong.mongo.dao.ActorDao;
import com.quhong.mongo.dao.FingerGuessDao;
import com.quhong.mongo.data.FingerGuessData;
import com.quhong.mq.MqSenderService;
import com.quhong.msg.obj.GiftInfoObject;
import com.quhong.msg.obj.GiftReceiveInfoObject;
import com.quhong.msg.obj.GiftSendInfoObject;
import com.quhong.msg.obj.RidInfoObject;
import com.quhong.msg.room.CreateFingerGuessPushMsg;
import com.quhong.msg.room.RoomGiftMsg;
import com.quhong.msg.room.RoomRoshamboEntranceChangeMsg;
import com.quhong.msg.room.RoomRoshamboResultMsg;
import com.quhong.mysql.dao.GiftDao;
import com.quhong.mysql.dao.VipInfoDao;
import com.quhong.mysql.data.GiftData;
import com.quhong.redis.FingerGuessRedis;
import com.quhong.redis.OperationConfigRedis;
import com.quhong.room.RoomWebSender;
import com.quhong.room.cache.RoomActorCache;
import com.quhong.room.data.RoomActorDetailData;
import com.quhong.utils.ActorUtils;
import com.quhong.utils.RoomUtils;
import com.quhong.vo.*;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;
import org.springframework.util.CollectionUtils;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.*;
import java.util.concurrent.ThreadLocalRandom;
import java.util.concurrent.TimeUnit;

/**
 * 猜拳游戏
 *
 * <AUTHOR>
 * @date 2022/11/3
 */
@Service
public class FingerGuessService extends SlowTaskQueue {

    private static final Logger logger = LoggerFactory.getLogger(FingerGuessService.class);
    private static final String FINGER_GUESS_LOCK_KEY = "finger_guess_";

    // 创建猜拳游戏费用
    private static final String CREATE_ROSHAMBO_TITLE = "Create Roshambo";
    private static final String CREATE_ROSHAMBO_DESC = "Create Roshambo";
    private static final int CREATE_ACT_TYPE = 67;

    // 加入猜拳游戏费用
    private static final String JOIN_ROSHAMBO_TITLE = "Join Roshambo";
    private static final String JOIN_ROSHAMBO_DESC = "Join Roshambo";
    private static final int JOIN_ACT_TYPE = 68;

    // 返还猜拳游戏费用
    private static final String RETURN_ROSHAMBO_TITLE = "Return Roshambo";
    private static final String RETURN_ROSHAMBO_DESC = "Return Roshambo";
    private static final int RETURN_ACT_TYPE = 69;

    // 猜拳游戏胜利奖励
    private static final String WIN_ROSHAMBO_TITLE = "Win Roshambo";
    private static final String WIN_ROSHAMBO_DESC = "Win Roshambo";
    private static final int WIN_ACT_TYPE = 70;

    public static final FingerGuessGiftVO FINGER_GUESS_GIFT_VO = new FingerGuessGiftVO();
    private static final int MILLISECONDS_TO_SLEEP = 5000;
    private static final int PAGE_SIZE = 20;
    private static final int FINGER_GUESS_MAX_TIME = 30 * 60;
    private static final List<Integer> DEBUG_GIFT_ID_LIST = Arrays.asList(487, 653, 58);
    private static final List<Integer> PRO_GIFT_ID_LIST = Arrays.asList(416, 89, 82);
    private static final int LOOP_TIME = 5 * 1000; // 每5秒扫描一下是否有超时要结束的猜拳游戏
    public static final int GAME_EXPIRE_SECONDS = (int) TimeUnit.MINUTES.toSeconds(30);

    private static final String DEBUG_RANKING_H5_URL = "https://test2.qmovies.tv/roshambo_ranking/";
    private static final String DEBUG_HISTORY_H5_URL = "https://test2.qmovies.tv/history/";
    private static final String PRO_RANKING_H5_URL = "https://static.youstar.live/roshambo_ranking/";
    private static final String PRO_HISTORY_H5_URL = "https://static.youstar.live/history/";
    private static final Queue<Boolean> SEED = new LinkedList<>();
    private static final List<Boolean> SEED_A = Arrays.asList(true, true, false, true, true, false, true, false, true, true);
    private static final List<Boolean> SEED_B = Arrays.asList(true, false, true, true, false, true, true, false, true, true);
    private static final List<Boolean> SEED_C = Arrays.asList(false, true, true, false, true, true, true, false, true, true);
    private static final List<Boolean> SEED_D = Arrays.asList(true, true, false, true, true, true, false, true, false, true);


    @Resource
    private ActorDao actorDao;
    @Resource
    private DataCenterService dataCenterService;
    @Resource
    private FingerGuessDao fingerGuessDao;
    @Resource
    private FingerGuessRedis fingerGuessRedis;
    @Resource
    private RoomWebSender roomWebSender;
    @Resource
    private GiftDao giftDao;
    @Resource
    private EventReport eventReport;
    @Resource
    private DailyTaskService dailyTaskService;
    @Resource
    private RoomActorCache roomActorCache;
    @Resource
    private ActorConfigDao actorConfigDao;
    @Resource
    private BadgeService badgeService;
    @Resource
    private VipInfoDao vipInfoDao;
    @Resource
    private MqSenderService mqSenderService;
    @Resource
    private CommonTaskService commonTaskService;
    @Resource
    private OperationConfigRedis operationConfigRedis;

    @PostConstruct
    public void postInit() {
        TimerService.getService().addDelay(new LoopTask(this, LOOP_TIME) {
            @Override
            protected void execute() {
                onTick();
            }
        });

        List<Integer> giftIds = ServerConfig.isNotProduct() ? DEBUG_GIFT_ID_LIST : PRO_GIFT_ID_LIST;
        List<FingerGuessGiftVO.Gift> list = new ArrayList<>();
        for (Integer giftId : giftIds) {
            GiftData giftData = giftDao.getGiftFromCache(giftId);
            if (giftData == null) {
                continue;
            }
            list.add(new FingerGuessGiftVO.Gift(giftData.getRid(), giftData.getPrice(), giftData.getGicon()));
        }
        FINGER_GUESS_GIFT_VO.setList(list);
        FINGER_GUESS_GIFT_VO.setHistoryUrl(ServerConfig.isNotProduct() ? DEBUG_HISTORY_H5_URL : PRO_HISTORY_H5_URL);
        FINGER_GUESS_GIFT_VO.setRankUrl(ServerConfig.isNotProduct() ? DEBUG_RANKING_H5_URL : PRO_RANKING_H5_URL);
    }

    private void onTick() {
        Set<String> gameIds = fingerGuessRedis.getFingerGuessWaitEndId(DateHelper.getNowSeconds());
        if (CollectionUtils.isEmpty(gameIds)) {
            return;
        }
        logger.info("dismiss finger guess game . gameIds.size={} gameIds={}", gameIds.size(), Arrays.toString(gameIds.toArray()));
        for (String gameId : gameIds) {
            fingerGuessRedis.removeFingerGuessWaitTime(gameId);
            // 结束等待超时游戏
            dismissFingerGuessGame(gameId);
        }
    }

    /**
     * 结束等待超时游戏
     */
    private void dismissFingerGuessGame(String gameId) {
        try {
            FingerGuessData data = fingerGuessDao.findData(gameId);
            if (data == null) {
                return;
            }
            data.setStatus(FingerGuessConstant.GAME_TIME_OUT_END);
            data.setEndTime(DateHelper.getNowSeconds());
            fingerGuessDao.save(data);
            fingerGuessRedis.removeFingerGuessId(gameId);
            fingerGuessRedis.removeFingerGuessInfo(gameId);
            sendDiamondsReward(data.getUid(), data.getCreatorRoomId(), 71, data.getgBeans(), RETURN_ROSHAMBO_TITLE, "");
            sendRoshamboEntranceChangeMsg();
        } catch (Exception e) {
            logger.error("dismiss finger guess game error. gameId={}", gameId);
        }
    }

    /**
     * 创建猜拳
     */
    public String create(FingerGuessDTO reqDTO) {
        String uid = reqDTO.getUid();
        String roomId = reqDTO.isRobot() ? RoomUtils.formatRoomId(uid) : reqDTO.getRoom_id();
        ActorData actorData = actorDao.getActorDataFromCache(uid);
        if (actorData == null) {
            logger.error("can not find actor. uid={}", uid);
            throw new GameException(GameHttpCode.THE_ACTOR_NOT_FOUND);
        }
        GiftData gift = giftDao.getGiftFromCache(reqDTO.getGift_id());
        if (gift == null) {
            logger.error("can not find gift data. giftId={}", reqDTO.getGift_id());
            throw new GameException(GameHttpCode.PARAM_ERROR);
        }
        int beans = gift.getPrice();
        if (!reqDTO.isRobot()) {
            deductCost(uid, roomId, beans, CREATE_ACT_TYPE, CREATE_ROSHAMBO_TITLE, CREATE_ROSHAMBO_DESC);
        }
        int nowSeconds = DateHelper.getNowSeconds();
        FingerGuessData fingerGuessData = new FingerGuessData(uid, reqDTO.getG_type(), reqDTO.getGift_id(), reqDTO.getG_icon(), beans, nowSeconds, roomId, reqDTO.isRobot() ? 1 : 0);
        fingerGuessDao.save(fingerGuessData);
        String fingerGuessId = fingerGuessData.get_id().toString();
        fingerGuessRedis.setFingerGuessIdInRedis(fingerGuessId);
        FingerGuessInfo guessInfo = new FingerGuessInfo();
        guessInfo.setHead(ImageUrlGenerator.generateRoomUserUrl(actorData.getHead(), vipInfoDao.getIntVipLevelFromCache(actorData.getUid())));
        guessInfo.setCreatorUid(uid);
        guessInfo.setName(actorData.getName());
        guessInfo.setcTime(nowSeconds);
        guessInfo.setGid(fingerGuessData.getGid());
        guessInfo.setgIcon(fingerGuessData.getgIcon());
        guessInfo.setgDiamonds(fingerGuessData.getgBeans());
        guessInfo.setGameId(fingerGuessId);
        fingerGuessRedis.saveFingerGuessInfo(guessInfo);
        // 写入redis做过期用
        int endTimestamp = nowSeconds + GAME_EXPIRE_SECONDS;
        fingerGuessRedis.saveFingerGuessWaitTime(fingerGuessId, endTimestamp);
        sendCreateFingerGuessPushMsg(uid, roomId, actorData);
        sendRoshamboEntranceChangeMsg();
        // 创建猜拳时上报数数
        doReportEvent(roomId, reqDTO.isRobot(), guessInfo);
        return fingerGuessId;
    }

    private void doReportEvent(String roomId, boolean isRobot, FingerGuessInfo guessInfo) {
        CreateGameLogEvent event = new CreateGameLogEvent();
        event.setUid(guessInfo.getCreatorUid());
        event.setIs_robot(isRobot ? 1 : 0);
        event.setRoom_id(roomId);
        event.setGame_id(guessInfo.getGameId());
        event.setGame_type(4);
        event.setCost_type(2);
        event.setCost_number(guessInfo.getgDiamonds());
        event.setCtime(DateHelper.getNowSeconds());
        eventReport.track(new EventDTO(event));
    }

    /**
     * 猜拳动画结束后发送广播
     */
    public void sendBroadcastMessage(FingerGuessDTO reqDTO) {
        FingerGuessData data = fingerGuessDao.findData(reqDTO.getG_id());
        if (data == null) {
            return;
        }
        String creatorUid = data.getUid();
        ActorData creator = actorDao.getActorDataFromCache(creatorUid);
        if (creator == null) {
            logger.error("can not find actor. uid={}", creatorUid);
            return;
        }
        ActorData aidPlayer = actorDao.getActorDataFromCache(data.getAid());
        if (aidPlayer == null) {
            logger.error("can not find actor. uid={}", data.getAid());
            return;
        }
        sendBroadcastMessage(creator, aidPlayer, data);
    }

    /**
     * 猜拳动画结束后发送广播
     */
    private void sendBroadcastMessage(ActorData creator, ActorData aidPlayer, FingerGuessData data) {
        TimerService.getService().addDelay(new DelayTask(MILLISECONDS_TO_SLEEP) {
            @Override
            protected void execute() {
                try {
                    logger.info("send broadcast message.");
                    sendRoomRoshamboResultMsg(data, creator, aidPlayer);
                    if (!StringUtils.isEmpty(data.getWinner())) {
                        sendRoomGiftMsg(data);
                    }
                } catch (Exception e) {
                    logger.error("send broadcast message error. uid={} gid={} {}", aidPlayer.getUid(), data.get_id().toString(), e.getMessage(), e);
                }
            }
        });
    }

    /**
     * 发送创建猜拳游戏的消息
     */
    private void sendCreateFingerGuessPushMsg(String uid, String roomId, ActorData actorData) {
        if (StringUtils.isEmpty(roomId)) {
            return;
        }
        BaseTaskFactory.getFactory().addSlow(new Task() {
            @Override
            protected void execute() {
                CreateFingerGuessPushMsg msg = new CreateFingerGuessPushMsg();
                msg.setFrom_name(actorData.getName());
                msg.setFrom_head(ImageUrlGenerator.generateRoomUserUrl(actorData.getHead(), vipInfoDao.getIntVipLevelFromCache(actorData.getUid())));
                roomWebSender.sendRoomWebMsg(roomId, uid, msg, true);
            }
        });
    }

    /**
     * 发送等待参与的猜拳游戏数量改变消息
     */
    private void sendRoshamboEntranceChangeMsg() {
        int guessGameNum = fingerGuessRedis.getAllGuessNum();
        BaseTaskFactory.getFactory().addSlow(new Task() {
            @Override
            protected void execute() {
                RoomRoshamboEntranceChangeMsg msg = new RoomRoshamboEntranceChangeMsg();
                msg.setNumber(guessGameNum);
                roomWebSender.sendRoomWebMsg(RoomWebSender.ALL_ROOM, null, msg, false);
            }
        });
    }

    public JoinFingerGuessVO join(FingerGuessDTO reqDTO) {
        try (DistributeLock lock = new DistributeLock(FINGER_GUESS_LOCK_KEY + reqDTO.getG_id())) {
            lock.lock();
            return doJoin(reqDTO);
        } catch (GameException e) {
            throw e;
        } catch (Exception e) {
            logger.error("join finger guess game error. roomId={} uid={} gameId={}", reqDTO.getRoomId(), reqDTO.getUid(), reqDTO.getG_id());
            throw e;
        }
    }

    /**
     * 加入猜拳
     */
    public JoinFingerGuessVO doJoin(FingerGuessDTO reqDTO) {
        String uid = reqDTO.getUid();
        ActorData actorData = actorDao.getActorDataFromCache(uid);
        if (actorData == null) {
            logger.error("can not find actor. uid={}", uid);
            throw new GameException(GameHttpCode.THE_ACTOR_NOT_FOUND);
        }
        FingerGuessData data = fingerGuessDao.findData(reqDTO.getG_id());
        if (data == null) {
            logger.error("can not find finger guess data. uid={}, gameId={}", uid, reqDTO.getG_id());
            throw new GameException(GameHttpCode.PARAM_ERROR);
        }
        if (data.getStatus() == FingerGuessConstant.GAME_END) {
            logger.info("the game has been started by others. uid={} gameId={}", uid, reqDTO.getG_id());
            throw new GameException(GameHttpCode.STARTED_BY_OTHERS);
        }
        if (data.getStatus() == FingerGuessConstant.GAME_TIME_OUT_END) {
            logger.info("The game has expired. uid={} gameId={}", uid, reqDTO.getG_id());
            throw new GameException(GameHttpCode.GAME_HAS_EXPIRED);
        }
        if (uid.equals(data.getUid())) {
            logger.info("you can not join your own game. uid={} gameId={}", uid, reqDTO.getG_id());
            throw new GameException(GameHttpCode.CAN_NOT_JOIN_YOUR_OWN_GAME);
        }
        if (!reqDTO.isRobot()) {
            deductCost(uid, reqDTO.getRoom_id(), data.getgBeans(), JOIN_ACT_TYPE, JOIN_ROSHAMBO_TITLE, JOIN_ROSHAMBO_DESC);
        }
        JoinFingerGuessVO vo = new JoinFingerGuessVO();
        vo.setGiftToken("");
        String creatorUid = data.getUid();
        data.setStatus(1);
        data.setAid(uid);
        data.setRecRoomId(reqDTO.getRoom_id());
        data.setAidGesture(reqDTO.getG_type());
        data.setEndTime(DateHelper.getNowSeconds());
        // 机器人参与游戏
        String robotUid = determineRobotResult(reqDTO, data);
        fingerGuessRedis.removeFingerGuessWaitTime(reqDTO.getG_id());
        String gameResult = judgeVictory(creatorUid, uid, data.getUidGesture(), data.getAidGesture());
        int nowTime = DateHelper.getNowSeconds();
        if (StringUtils.isEmpty(gameResult)) {
            // 平局返回双方的钻石
            returnBothBeans(creatorUid, uid, data.getCreatorRoomId(), data.getRecRoomId(), data.getgBeans(), reqDTO.getG_id());
            vo.setgResult(2);
        } else if (gameResult.equals(uid)) {
            // 加入者赢
            addBeansForWinner(uid, creatorUid, data.getRecRoomId(), data.getgBeans(), reqDTO.getG_id(), false, robotUid);
            data.setWinner(uid);
            vo.setgResult(0);
            addRankingGiftNum(uid, FingerGuessConstant.RANKING_TYPE_WINNERS, data.getGid(), nowTime);
            commonTaskService.sendCommonTaskMq(new CommonMqTopicData(uid, "", "", data.getGid() + "", CommonMqTaskConstant.WIN_FINGER_GUESS, 1));
        } else if (gameResult.equals(creatorUid)) {
            // 创建者赢
            addBeansForWinner(creatorUid, uid, data.getCreatorRoomId(), data.getgBeans(), reqDTO.getG_id(), true, robotUid);
            data.setWinner(creatorUid);
            vo.setgResult(1);
            addRankingGiftNum(creatorUid, FingerGuessConstant.RANKING_TYPE_WINNERS, data.getGid(), nowTime);
            commonTaskService.sendCommonTaskMq(new CommonMqTopicData(creatorUid, "", "", data.getGid() + "", CommonMqTaskConstant.WIN_FINGER_GUESS, 1));
        }
        fingerGuessDao.save(data);
        addRankingGiftNum(uid, FingerGuessConstant.RANKING_TYPE_PARTICIPANTS, 0, nowTime);
        addRankingGiftNum(creatorUid, FingerGuessConstant.RANKING_TYPE_PARTICIPANTS, 0, nowTime);
        vo.setgType(data.getUidGesture());
        vo.setMyGType(data.getAidGesture());
        vo.setgIcon(reqDTO.getG_icon());
        vo.setHead(ImageUrlGenerator.generateRoomUserUrl(actorData.getHead(), vipInfoDao.getIntVipLevelFromCache(actorData.getUid())));
        vo.setName(actorData.getName());
        ActorData creator = actorDao.getActorDataFromCache(creatorUid);
        vo.setCreateHead(creator != null ? ImageUrlGenerator.generateRoomUserUrl(creator.getHead(), vipInfoDao.getIntVipLevelFromCache(creatorUid)) : "");
        vo.setCreateName(creator != null ? creator.getName() : "");
        vo.setgId(data.get_id().toString());
        // 删除redis数据
        fingerGuessRedis.removeFingerGuessId(reqDTO.getG_id());
        fingerGuessRedis.removeFingerGuessInfo(reqDTO.getG_id());
        sendRoshamboEntranceChangeMsg();
        // 玩猜拳游戏的每日任务
        dailyTaskService.sendToMq(new DailyTaskMqData(uid, 9, DateHelper.ARABIAN.formatDateInDay2(), 1));
        // 参与猜拳游戏
        commonTaskService.sendCommonTaskMq(new CommonMqTopicData(creatorUid, "", "", "", CommonMqTaskConstant.PLAY_FINGER_GUESS, 1));
        commonTaskService.sendCommonTaskMq(new CommonMqTopicData(uid, "", "", "", CommonMqTaskConstant.PLAY_FINGER_GUESS, 1));

        sendBroadcastMessage(creator, actorData, data);
        return vo;
    }

    private boolean doResult() {
        if (SEED.size() == 0) {
            // buildRandomSeed();
            int r = ThreadLocalRandom.current().nextInt(4);
            SEED.addAll(r == 0 ? SEED_A : r == 1 ? SEED_B : r == 2 ? SEED_C : SEED_D);
        }
        return Boolean.TRUE.equals(SEED.poll());
    }

    private void buildRandomSeed() {
        List<Boolean> seedList = new ArrayList<>();
        for (int i = 0; i < 10; i++) {
            seedList.add(i < 7);
        }
        Collections.shuffle(seedList);
        long count = seedList.subList(0, 5).stream().filter(b -> !b).count();
        if (count == 0 || count == 3) {
            buildRandomSeed();
            return;
        }
        SEED.addAll(seedList);
    }

    /**
     * 4、机器人创建和应战猜拳输赢规则
     * - 70%赢、30%输；每创建和应战10局，有3局输给用户；
     * - 用户赢得的钻石，系统每日0点（GMT+3）从用户账户扣除。
     *
     * @return robotUid
     */
    private String determineRobotResult(FingerGuessDTO reqDTO, FingerGuessData data) {
        // PVP
        if (!reqDTO.isRobot() && data.getRobot() != 1) {
            return null;
        }
        // EVE
        if (reqDTO.isRobot() && data.getRobot() == 1) {
            throw new GameException();
        }
        boolean robotWin = doResult();
        logger.info("determineRobotResult gameId={} uid={} robotWin={}", reqDTO.getG_id(), reqDTO.getUid(), robotWin);
        // 机器人参战
        if (reqDTO.isRobot()) {
            int uidGesture = data.getUidGesture();
            if (robotWin) {
                data.setAidGesture(uidGesture == 0 ? 1 : uidGesture == 1 ? 2 : 0);
            } else {
                data.setAidGesture(uidGesture == 0 ? 2 : uidGesture == 1 ? 0 : 1);
            }
            return reqDTO.getUid();
        }
        // 机器人创建的对局
        if (data.getRobot() == 1) {
            int aidGesture = reqDTO.getG_type();
            if (robotWin) {
                data.setUidGesture(aidGesture == 0 ? 1 : aidGesture == 1 ? 2 : 0);
            } else {
                data.setUidGesture(aidGesture == 0 ? 2 : aidGesture == 1 ? 0 : 1);
            }
            return data.getUid();
        }
        return null;
    }

    private void addRankingGiftNum(String uid, int rankType, int giftId, int ctime) {
        int uidGiftNum = fingerGuessRedis.getRankingGiftNum(uid, rankType, giftId);
        fingerGuessRedis.saveRanking(uid, rankType, giftId, uidGiftNum + 1, ctime);
    }

    /**
     * 猜拳大厅
     */
    public FingerGuessHallVO hall(FingerGuessDTO reqDTO) {
        int page = reqDTO.getPage() != null ? reqDTO.getPage() : 1;
        int start = (page - 1) * PAGE_SIZE;
        int end = page * PAGE_SIZE;
        FingerGuessHallVO vo = new FingerGuessHallVO();
        int allGuessNum = fingerGuessRedis.getAllGuessNum();
        List<String> list = fingerGuessRedis.findList(start, end);
        List<FingerGuessHallVO.GameInfo> voList = new ArrayList<>();
        if (!CollectionUtils.isEmpty(list)) {
            for (String id : list) {
                FingerGuessInfo data = fingerGuessRedis.getFingerGuessInfo(id);
                if (data == null) {
                    continue;
                }
                FingerGuessHallVO.GameInfo game = new FingerGuessHallVO.GameInfo();
                int endTime = data.getcTime() + FINGER_GUESS_MAX_TIME - DateHelper.getNowSeconds();
                game.setLeft_time(endTime);
                game.setG_id(data.getGameId());
                game.setGift_id(data.getGid());
                game.setG_icon(data.getgIcon());
                game.setG_diamonds(data.getgDiamonds());
                game.setHead(data.getHead());
                game.setName(data.getName());
                game.setCreator_uid(data.getCreatorUid());
                voList.add(game);
            }
        }
        vo.setList(voList);
        vo.setNextUrl(allGuessNum > end ? String.valueOf(page + 1) : "");
        vo.setNumber(allGuessNum);
        vo.setRankUrl(ServerConfig.isNotProduct() ? DEBUG_RANKING_H5_URL : PRO_RANKING_H5_URL);
        return vo;
    }

    /**
     * 获取猜拳礼物列表
     */
    public FingerGuessGiftVO giftList() {
        return FINGER_GUESS_GIFT_VO;
    }

    /**
     * 个人的猜拳记录，不展示超时的
     */
    public PageVO myHistory(String uid, Integer page, Boolean isNew) {
        PageVO vo = new PageVO();
        page = page != null ? page : 1;
        int start = (page - 1) * PAGE_SIZE;
        List<FingerGuessData> historyList = fingerGuessDao.getHistory(uid, start, start + PAGE_SIZE, isNew);
        if (CollectionUtils.isEmpty(historyList)) {
            vo.setList(new ArrayList<>());
            vo.setNextUrl("");
            return vo;
        }
        List<FingerGuessHistoryVO> historyVOList = new ArrayList<>();
        for (FingerGuessData data : historyList) {
            String aid = Objects.equals(data.getAid(), uid) ? data.getUid() : data.getAid();
            ActorData actor = actorDao.getActorDataFromCache(data.getUid());
            if (actor == null) {
                continue;
            }
            FingerGuessHistoryVO historyVO = new FingerGuessHistoryVO();
            historyVO.setgType(Objects.equals(data.getAid(), uid) ? data.getUidGesture() : data.getAidGesture());
            historyVO.setgIcon(data.getgIcon());
            historyVO.setName(actor.getName());
            historyVO.setgResult(getGameResult(uid, aid, data.getWinner(), data.getStatus()));
            historyVO.setgTime(data.getcTime());
            historyVOList.add(historyVO);
        }
        vo.setList(historyVOList);
        vo.setNextUrl(historyList.size() < PAGE_SIZE ? "" : String.valueOf(page + 1));
        return vo;
    }

    /**
     * 猜拳排行榜
     */
    public FingerGuessRankingVO ranking(String uid, Integer rankType, Integer giftId) {
        if (rankType != FingerGuessConstant.RANKING_TYPE_PARTICIPANTS && rankType != FingerGuessConstant.RANKING_TYPE_WINNERS) {
            logger.error("rankType param error. rankType={}", rankType);
            throw new GameException(HttpCode.PARAM_ERROR);
        }
        if (rankType == FingerGuessConstant.RANKING_TYPE_WINNERS) {
            List<Integer> giftIds = ServerConfig.isNotProduct() ? DEBUG_GIFT_ID_LIST : PRO_GIFT_ID_LIST;
            if (!giftIds.contains(giftId)) {
                logger.error("giftId param error. giftId={}", giftId);
                throw new GameException(HttpCode.PARAM_ERROR);
            }
        } else {
            giftId = 0;
        }
        ActorData user = actorDao.getActorDataFromCache(uid);
        if (user == null) {
            logger.error("can not find actor. uid={}", uid);
            throw new GameException(GameHttpCode.THE_ACTOR_NOT_FOUND);
        }
        FingerGuessRankingVO vo = new FingerGuessRankingVO();
        vo.setMyRank(fingerGuessRedis.getFingerGuessRank(uid, rankType, giftId));
        vo.setMyName(user.getName());
        vo.setMyHead(ImageUrlGenerator.generateRoomUserUrl(user.getHead(), vipInfoDao.getIntVipLevelFromCache(user.getUid())));
        vo.setMyGiftNum(fingerGuessRedis.getRankingGiftNum(uid, rankType, giftId));
        Map<String, Integer> rankingMap = fingerGuessRedis.getRankingList(rankType, giftId);
        if (CollectionUtils.isEmpty(rankingMap)) {
            vo.setList(Collections.emptyList());
            return vo;
        }
        int rank = 1;
        List<FingerGuessRankingVO.Actor> list = new ArrayList<>();
        for (Map.Entry<String, Integer> entry : rankingMap.entrySet()) {
            ActorData actorData = actorDao.getActorDataFromCache(entry.getKey());
            if (actorData == null) {
                logger.error("can not find actor. uid={}", entry.getKey());
                continue;
            }
            list.add(new FingerGuessRankingVO.Actor(rank, actorData.getName(),
                    ImageUrlGenerator.generateRoomUserUrl(actorData.getHead(), vipInfoDao.getIntVipLevelFromCache(actorData.getUid())), entry.getValue()));
            rank++;
        }
        vo.setList(list);
        return vo;
    }

    /**
     * 给获胜的用户发放奖励
     */
    private void addBeansForWinner(String uid, String aid, String uRoomId, int beans, String gameId, boolean isCreatorWin, String robotUid) {
        int earnBeans;
        if (beans == 3) {
            earnBeans = 1;
        } else if (beans == 6) {
            earnBeans = 2;
        } else if (beans == 9) {
            earnBeans = 3;
        } else {
            earnBeans = beans * 3 / 10;
            earnBeans = earnBeans != 0 ? earnBeans : 1;
        }
        // 计算额外奖励
        int reward = operationConfigRedis.randomExtraGameReward(uid, 21, earnBeans);
        sendDiamondsReward(uid, uRoomId, RETURN_ACT_TYPE, beans, RETURN_ROSHAMBO_TITLE, RETURN_ROSHAMBO_DESC);
        sendDiamondsReward(uid, uRoomId, WIN_ACT_TYPE, reward, WIN_ROSHAMBO_TITLE, WIN_ROSHAMBO_DESC);

        // 猜拳成就勋章下发
        // dealWithFingerGuessBadge(uid, earnBeans);

        // 数数埋点
        GameRecordLogEvent event = new GameRecordLogEvent();
        event.setUid(uid);
        event.setRobot(uid.equals(robotUid) ? 1 : 0);
        event.setGame_type(EventGameTypeConstant.FINGER_GUESS);
        event.setGame_id(gameId);
        event.setCost_diamonds(beans);
        event.setGet_diamonds(beans + reward);
        event.setExtra_reward(reward - earnBeans);
        event.setIs_creator(isCreatorWin ? 1 : 0);
        event.setCtime(DateHelper.getNowSeconds());
        eventReport.track(new EventDTO(event));
        event.setGet_heart(0);
        event.setGet_diamonds(0);
        event.setIs_creator(isCreatorWin ? 0 : 1);
        event.setUid(aid);
        event.setRobot(aid.equals(robotUid) ? 1 : 0);
        eventReport.track(new EventDTO(event));
    }

    public void dealWithFingerGuessBadge(String uid, int earnBeans) {
        long fingerGuessCount = actorConfigDao.getLongUserConfig(uid, ActorConfigDao.FINGER_GUESS_COUNT, -1);
        if (fingerGuessCount > -1) {
            fingerGuessCount += earnBeans;
            logger.info("dealWithFingerGuessBadge uid: {} fingerGuessCount:{}", uid, fingerGuessCount);
            actorConfigDao.updateUserConfig(uid, ActorConfigDao.FINGER_GUESS_COUNT, fingerGuessCount);
            badgeService.doAchieveBadge(uid, AchieveBadgeConstant.TYPE_GUESS, fingerGuessCount, earnBeans);
        } else {
            try {
                MoneyTypeDTO dto = new MoneyTypeDTO();
                dto.setUid(uid);
                dto.setMoneyType(MoneyTypeConstant.FINGER_GUESS_WIN);
                ApiResult<Long> result = dataCenterService.esMoneyTypeTotal(dto);
                if (result.isError()) {
                    logger.error("esMoneyTypeTotal FingerGuess error uid:{}, code: {} msg:{}", uid, result.getCode().getCode(), result.getCode().getMsg());
                } else {
                    long totalFingerGuessBean = result.getData();
                    logger.info("esMoneyTypeTotal totalFingerGuessBean: {}, fingerGuessCount:{}", totalFingerGuessBean, fingerGuessCount);
                    if (totalFingerGuessBean > -1) {
                        badgeService.supplyAchieveBadge(uid, AchieveBadgeConstant.TYPE_GUESS, totalFingerGuessBean);
                        actorConfigDao.updateUserConfig(uid, ActorConfigDao.FINGER_GUESS_COUNT, totalFingerGuessBean);
                    }
                }
            } catch (Exception e) {
                logger.error("esMoneyTypeTotal fingerGuess error uid: {}, message:{}", uid, e.getMessage(), e);
            }
        }
    }

    /**
     * 返回双方的钻石
     */
    private void returnBothBeans(String uid, String aid, String uRoomId, String aRoomId, int beans, String gameId) {
        sendDiamondsReward(uid, uRoomId, RETURN_ACT_TYPE, beans, RETURN_ROSHAMBO_TITLE, RETURN_ROSHAMBO_DESC);
        sendDiamondsReward(aid, aRoomId, RETURN_ACT_TYPE, beans, RETURN_ROSHAMBO_TITLE, RETURN_ROSHAMBO_DESC);
        if (StringUtils.isEmpty(gameId)) {
            return;
        }
        // 数数埋点
        GameRecordLogEvent event = new GameRecordLogEvent();
        event.setUid(uid);
        event.setGame_type(EventGameTypeConstant.FINGER_GUESS);
        event.setGame_id(gameId);
        event.setCost_heart(beans);
        event.setGet_heart(beans);
        event.setIs_creator(1);
        event.setCtime(DateHelper.getNowSeconds());
        eventReport.track(new EventDTO(event));
        event.setIs_creator(0);
        event.setUid(aid);
        eventReport.track(new EventDTO(event));
    }

    /**
     * 获取猜拳的获胜方
     */
    private String judgeVictory(String creatorUid, String recUid, int creatorUidGesture, int recUidGesture) {
        if ((creatorUidGesture == 0 && recUidGesture == 2) || (creatorUidGesture == 1 && recUidGesture == 0) ||
                (creatorUidGesture == 2 && recUidGesture == 1)) {
            return creatorUid;
        } else if (creatorUidGesture == recUidGesture) {
            return "";
        } else {
            return recUid;
        }
    }

    /**
     * 扣除创建猜拳费用
     */
    private void deductCost(String uid, String roomId, int beans, int actType, String title, String desc) {
        MoneyDetailReq moneyDetailReq = new MoneyDetailReq();
        moneyDetailReq.setRandomId();
        moneyDetailReq.setUid(uid);
        moneyDetailReq.setRoomId(roomId);
        moneyDetailReq.setAtype(actType);
        moneyDetailReq.setChanged(-beans);
        moneyDetailReq.setTitle(title);
        moneyDetailReq.setDesc(desc);
        moneyDetailReq.setMtime(DateHelper.getNowSeconds());
        ApiResult<String> result = dataCenterService.reduceBeans(moneyDetailReq);
        if (!result.isOk()) {
            if (1 == result.getCode().getCode()) {
                throw new GameException(GameHttpCode.DIAMOND_NOT_ENOUGH);
            }
            logger.error("reduce beans error, uid={} msg={}", uid, result.getCode().getMsg());
            throw new GameException(HttpCode.SERVER_ERROR);
        }
    }

    /**
     * 下发钻石奖励
     *
     * @param uid     获取礼物的用户uid
     * @param roomId  房间id
     * @param aType   aType
     * @param changed 钻石数量
     * @param title   标题
     * @param desc    描述
     */
    public void sendDiamondsReward(String uid, String roomId, Integer aType, Integer changed, String title, String desc) {
        logger.info("send diamonds reward. uid={}, changed={}, title={}", uid, changed, title);
        MoneyDetailReq moneyDetailReq = new MoneyDetailReq();
        moneyDetailReq.setRandomId();
        moneyDetailReq.setUid(uid);
        moneyDetailReq.setRoomId(roomId);
        moneyDetailReq.setAtype(aType);
        moneyDetailReq.setChanged(changed);
        moneyDetailReq.setTitle(title);
        moneyDetailReq.setDesc(desc);
        moneyDetailReq.setMtime(DateHelper.getNowSeconds());
        mqSenderService.asyncChargeDiamonds(moneyDetailReq);
    }

    /**
     * 获取游戏结果 0赢 1输 2平局 3等待中 4已超时
     */
    private Integer getGameResult(String uid, String aid, String winner, Integer status) {
        if (status == FingerGuessConstant.GAME_WAITING) {
            return 3;
        } else if (status == FingerGuessConstant.GAME_TIME_OUT_END) {
            return 4;
        } else {
            if (uid.equals(winner)) {
                return 0;
            } else if (aid.equals(winner)) {
                return 1;
            } else {
                return 2;
            }
        }

    }

    /**
     * 发送房间礼物消息
     */
    private void sendRoomGiftMsg(FingerGuessData data) {
        RoomGiftMsg msg = new RoomGiftMsg();
        String winner = data.getWinner();
        String fromUid = Objects.equals(winner, data.getUid()) ? data.getAid() : data.getUid();
        msg.setReceiveInfoList(getReceiveInfoList(Collections.singletonList(winner)));
        msg.setSendInfo(getSendInfo(fromUid));
        msg.setGiftInfo(getGiftInfo(data.getGid()));
        msg.setSendType(1);
        msg.setIsAllMic(0);
        roomWebSender.sendRoomWebMsg(data.getRecRoomId(), "", msg, false);
    }

    private GiftInfoObject getGiftInfo(int gid) {
        GiftData giftData = giftDao.getGiftFromCache(gid);
        if (giftData == null) {
            logger.error("can not find gift data, gid={}", gid);
            return null;
        }
        GiftInfoObject object = new GiftInfoObject();
        object.setGiftId(giftData.getRid());
        object.setGiftType(giftData.getGatype());
        object.setGiftNumber(1);
        object.setGiftIcon(giftData.getGicon());
        object.setGiftTime(giftData.getGtime());
        object.setGiftPrice(giftData.getPrice());
        return object;
    }

    private List<GiftReceiveInfoObject> getReceiveInfoList(List<String> uids) {
        List<GiftReceiveInfoObject> receiveInfoList = new ArrayList<>();
        for (String uid : uids) {
            ActorData actorData = actorDao.getActorDataFromCache(uid);
            if (null == actorData) {
                logger.error("cannot find actor, uid={}", uid);
                continue;
            }
            RoomActorDetailData detailData = roomActorCache.getData("", uid, false);
            GiftReceiveInfoObject object = new GiftReceiveInfoObject();
            object.setUid(uid);
            object.setRid(actorData.getRid() + "");
            object.setRidInfo(new RidInfoObject(actorData.getRidData()));
            object.setHead(detailData.getHead());
            object.setName(detailData.getName());
            object.setUlvl(detailData.getLevel());
            object.setVipLevel(detailData.getVipLevel());
            object.setIdentify(detailData.getIdentify());
            receiveInfoList.add(object);
        }
        return receiveInfoList;
    }

    /**
     * 发送猜拳结果消息
     */
    private void sendRoomRoshamboResultMsg(FingerGuessData data, ActorData creator, ActorData aidPlayer) {
        RoomRoshamboResultMsg msg = new RoomRoshamboResultMsg();
        boolean isCreatorWin = Objects.equals(data.getWinner(), creator.getUid());
        msg.setG_result(StringUtils.isEmpty(data.getWinner()) ? 2 : 1);
        msg.setG_icon(data.getgIcon());
        msg.setWin_name(isCreatorWin ? creator.getName() : aidPlayer.getName());
        msg.setLoser_name(isCreatorWin ? aidPlayer.getName() : creator.getName());
        msg.setCreate_room_id(data.getCreatorRoomId());
        msg.setRec_room_id(data.getRecRoomId());
        if (!StringUtils.isEmpty(data.getCreatorRoomId())) {
            roomWebSender.sendRoomWebMsg(data.getCreatorRoomId(), null, msg, false);
        }
        if (!Objects.equals(data.getCreatorRoomId(), data.getRecRoomId())) {
            roomWebSender.sendRoomWebMsg(data.getRecRoomId(), null, msg, false);
        }
    }

    private GiftSendInfoObject getSendInfo(String uid) {
        ActorData actorData = actorDao.getActorData(uid);
        if (null == actorData) {
            throw new GameException(HttpCode.PARAM_ERROR);
        }
        RoomActorDetailData detailData = roomActorCache.getData(RoomUtils.formatRoomId(uid), uid, false);
        GiftSendInfoObject sendInfoObject = new GiftSendInfoObject();
        sendInfoObject.setUid(detailData.getAid());
        sendInfoObject.setName(detailData.getName());
        sendInfoObject.setHead(detailData.getHead());
        sendInfoObject.setUlvl(detailData.getLevel());
        sendInfoObject.setVipLevel(detailData.getVipLevel());
        sendInfoObject.setBadgeList(detailData.getBadgeList());
        sendInfoObject.setIdentify(detailData.getIdentify());
        sendInfoObject.setRid(detailData.getRid() + "");
        sendInfoObject.setRidInfo(new RidInfoObject(detailData.getRidData()));
        sendInfoObject.setBeans(Math.max(actorData.getBeans(), 0));
        sendInfoObject.setIsNewUser(ActorUtils.isNewDeviceAccount(uid, actorData.getFirstTnId()) ? 1 : 0);
        return sendInfoObject;
    }

    /**
     * 重置排行榜
     */
    public void clearRanking() {
        List<Integer> giftIds = ServerConfig.isNotProduct() ? DEBUG_GIFT_ID_LIST : PRO_GIFT_ID_LIST;
        for (Integer giftId : giftIds) {
            fingerGuessRedis.clearRanking(FingerGuessConstant.RANKING_TYPE_WINNERS, giftId);
        }
        fingerGuessRedis.clearRanking(FingerGuessConstant.RANKING_TYPE_PARTICIPANTS, 0);
    }
}
