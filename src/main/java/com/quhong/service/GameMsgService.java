package com.quhong.service;

import com.quhong.core.config.ServerConfig;
import com.quhong.data.ActorData;
import com.quhong.image.ImageUrlGenerator;
import com.quhong.mongo.dao.ActorDao;
import com.quhong.msg.obj.HighlightTextObject;
import com.quhong.msg.room.RoomNotificationMsg;
import com.quhong.room.RoomWebSender;
import com.quhong.room.redis.RoomPlayerRedis;
import org.springframework.stereotype.Service;
import org.springframework.util.StringUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@Service
public class GameMsgService {

    private final static String VIEW_EN = "Go to play >>";
    private final static String VIEW_AR = "انتقل إلى >> اللعب";
    public final static String URL_FRUIT_PARTY = ServerConfig.isProduct() ? "https://h5.bigluckygame.com/game_fruits/?appId=888888&gameName=fruit_party" : "https://api.springluckygame.com/game_fruits?appId=880088&gameName=fruit_party";
    private final static String FRUIT_TEXT_EN = "Congratulations to #username# playing Fruit Party for winning #win_beans# \uD83D\uDC8E.Go to play >>";
    private final static String FRUIT_TEXT_AR = "تهانينا ل #win_beans# لعب حفلة فواكه للفوز #username#\uD83D\uDC8E. انتقل إلى >> اللعب";

    public final static String GAME_NAME_EN = "Fruit Party";
    public final static String GAME_NAME_AR = "الفاكهة الطرف";
    private final static String COMMON_TEXT_EN = "Congratulations to #username# for winning #win_beans# diamonds in #gamename# game. Go Play >";
    private final static String COMMON_TEXT_AR = "تهانينا لـ #username# لفوزها بـ #win_beans# Diamonds في لعبة #gamename#. اذهب للعب>";
    private final static String COMMON_VIEW_EN = "Go Play >";
    private final static String COMMON_VIEW_AR = "اذهب للعب>";

    public final static String GAME_ROOM_BIG_WIN = "game_room_big_win"; // 概率游戏中奖消息
    public final static Integer GAME_ROOM_MSG_TYPE = ServerConfig.isProduct() ? RoomWebSender.ONLY_GAME_ROOM : 0;

    @Resource
    private ActorDao actorDao;
    @Resource
    private RoomPlayerRedis roomPlayerRedis;
    @Resource
    private RoomWebSender roomWebSender;

    public void pushFruitBigReward(String uid, int winBeans) {
        String roomId = roomPlayerRedis.getActorRoomStatus(uid);
        String toRoomId = StringUtils.isEmpty(roomId) ? RoomWebSender.ALL_ROOM : RoomWebSender.ALL_ROOM + "_" + roomId;
        ActorData actorData = actorDao.getActorDataFromCache(uid);
        String name = actorData.getName();
        RoomNotificationMsg msg = new RoomNotificationMsg();
        msg.setUid(uid);
        msg.setUser_name(name);
        msg.setUser_head(ImageUrlGenerator.generateMiniUrl(actorData.getHead()));
        msg.setText(FRUIT_TEXT_EN.replace("#username#", name).replace("#win_beans#", String.valueOf(winBeans)));
        msg.setText_ar(FRUIT_TEXT_AR.replace("#username#", name).replace("#win_beans#", String.valueOf(winBeans)));
        List<HighlightTextObject> list = new ArrayList<>();
        HighlightTextObject object = new HighlightTextObject();
        object.setText(name);
        object.setHighlightColor("#FFE200");
        list.add(object);
        HighlightTextObject beanObject = new HighlightTextObject();
        beanObject.setText(String.valueOf(winBeans));
        beanObject.setHighlightColor("#FFE200");
        list.add(beanObject);
        HighlightTextObject viewObject = new HighlightTextObject();
        viewObject.setText(VIEW_EN);
        viewObject.setHighlightColor("#37B5FF");
        list.add(viewObject);
        HighlightTextObject viewArObject = new HighlightTextObject();
        viewArObject.setText(VIEW_AR);
        viewArObject.setHighlightColor("#37B5FF");
        list.add(viewArObject);
        msg.setHighlight_text(list);
        msg.setHighlight_text_ar(list);
        msg.setWeb_type(1);
        msg.setWeb_url(URL_FRUIT_PARTY);
        roomWebSender.sendRoomWebMsg(toRoomId, null, msg, false, RoomWebSender.ONLY_GAME_ROOM);
    }

    public void pushCommonBigReward(String uid, int winBeans, String gName, String gNameAr
            , int webType, String webUrl) {
        String roomId = roomPlayerRedis.getActorRoomStatus(uid);
        String toRoomId = StringUtils.isEmpty(roomId) ? RoomWebSender.ALL_ROOM : RoomWebSender.ALL_ROOM + "_" + roomId;
        ActorData actorData = actorDao.getActorDataFromCache(uid);
        String name = actorData.getName();
        RoomNotificationMsg msg = new RoomNotificationMsg();
        msg.setUid(uid);
        msg.setUser_name(name);
//        msg.setUser_head(ImageUrlGenerator.generateMiniUrl(actorData.getHead()));
        msg.setText(COMMON_TEXT_EN.replace("#username#", name).replace("#win_beans#", String.valueOf(winBeans)).replace("#gamename#", gName));
        msg.setText_ar(COMMON_TEXT_AR.replace("#username#", name).replace("#win_beans#", String.valueOf(winBeans)).replace("#gamename#", gNameAr));
        List<HighlightTextObject> list = new ArrayList<>();
        HighlightTextObject object = new HighlightTextObject();
        object.setText(name);
        object.setHighlightColor("#FFE200");
        list.add(object);
        HighlightTextObject beanObject = new HighlightTextObject();
        beanObject.setText(String.valueOf(winBeans));
        beanObject.setHighlightColor("#FFE200");
        list.add(beanObject);
        HighlightTextObject viewObject = new HighlightTextObject();
        viewObject.setText(COMMON_VIEW_EN);
        viewObject.setHighlightColor("#37B5FF");
        list.add(viewObject);
        HighlightTextObject viewArObject = new HighlightTextObject();
        viewArObject.setText(COMMON_VIEW_AR);
        viewArObject.setHighlightColor("#37B5FF");
        list.add(viewArObject);
        msg.setHighlight_text(list);
        msg.setHighlight_text_ar(list);
        msg.setWeb_type(webType);
        msg.setWeb_url(webUrl);
        msg.setGame_type(GAME_ROOM_BIG_WIN);
        msg.setHide_head(1);
        msg.setBc_status(1);
        roomWebSender.sendRoomWebMsg(toRoomId, null, msg, false, GAME_ROOM_MSG_TYPE);
    }
}
