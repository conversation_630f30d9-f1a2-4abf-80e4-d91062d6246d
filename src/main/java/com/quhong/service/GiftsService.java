package com.quhong.service;

import com.alibaba.fastjson.JSON;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.dto.ResourcesDTO;
import com.quhong.data.dto.MoneyDetailReq;
import com.quhong.enums.BaseDataResourcesConstant;
import com.quhong.mq.MqSenderService;
import com.quhong.vo.GiftsMqVo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;

@Service
public class GiftsService {

    private static final Logger logger = LoggerFactory.getLogger(GiftsService.class);
    private static final String SOURCES_DETAIL = "cityTreasureHunt";
    @Resource
    private MqSenderService mqSenderService;

    /**
     * 下发资源
     */
    public void sendGiftToMq(GiftsMqVo data) {
        try {
            if (data.getStype().equals("diamond")) {
                MoneyDetailReq moneyDetailReq = new MoneyDetailReq();
                moneyDetailReq.setRandomId();
                moneyDetailReq.setUid(data.getUid());
                moneyDetailReq.setAtype(data.getAct_type());
                moneyDetailReq.setChanged(data.getDiamond());
                moneyDetailReq.setTitle(data.getTitle());
                moneyDetailReq.setDesc(data.getAct_desc());
                moneyDetailReq.setMtime(DateHelper.getNowSeconds());
                mqSenderService.asyncChargeDiamonds(moneyDetailReq);
            } else {
                ResourcesDTO resourcesDTO = new ResourcesDTO();
                resourcesDTO.setUid(data.getUid());
                resourcesDTO.setResId(String.valueOf(data.getSource_id()));
                resourcesDTO.setResType(sTypeToResType(data.getStype()));
                resourcesDTO.setActionType(BaseDataResourcesConstant.ACTION_GET);
                resourcesDTO.setGetWay(BaseDataResourcesConstant.TYPE_GAME_GET);
                resourcesDTO.setmTime(DateHelper.getNowSeconds());
                resourcesDTO.setNum(data.getNum());
                resourcesDTO.setDays(data.getDay() == 0 ? -1 : data.getDay());
                resourcesDTO.setItemsSourceDetail(SOURCES_DETAIL);
                mqSenderService.asyncHandleResources(resourcesDTO);
            }
        } catch (Exception e) {
            logger.error("send gift to mq error. data={}", JSON.toJSONString(data), e);
        }
    }

    /**
     * @param sType 资源类型 背包礼物:gift4、麦位框:mic2、气泡框:buddle6、入场动画:ride3、麦位声波:ripple7、钻石:diamond、勋章:badge1、浮屏:float_screen8
     * @return resType 资源类型 1 勋章 2 麦位框 3 坐骑 4 背包礼物  5 房间锁 6 聊天气泡 7 声波纹 8 浮屏 9 个人背景资源
     */
    private int sTypeToResType(String sType) {
        return sType.equals("gift") ? 4 : sType.equals("mic") ? 2 : sType.equals("buddle") ? 6 : sType.equals("ride") ? 3 : sType.equals("ripple") ? 7 : sType.equals("badge") ? 1 : 8;
    }
}
