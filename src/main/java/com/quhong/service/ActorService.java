package com.quhong.service;

import com.quhong.data.ActorData;
import com.quhong.mongo.config.MongoBean;
import com.quhong.mongo.dao.ActorDao;
import com.quhong.mongo.data.Actor;
import org.bson.types.ObjectId;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.mongodb.core.MongoTemplate;
import org.springframework.data.mongodb.core.query.Criteria;
import org.springframework.data.mongodb.core.query.Query;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Service;

import javax.annotation.Resource;
import java.util.List;

@Service
public class ActorService {

    private static final Logger logger = LoggerFactory.getLogger(ActorService.class);

    @Resource(name = MongoBean.MOVIES)
    private MongoTemplate mongoTemplate;
    @Resource
    private ActorDao actorDao;

    public Actor getActor(String uid) {
        Criteria criteria = Criteria.where("_id").is(new ObjectId(uid));
        Actor actor = mongoTemplate.findOne(new Query(criteria), Actor.class);
        if (null == actor) {
            logger.error("get actor from mongo db error, cannot find actor uid={}", uid);
            return new Actor();
        }
        return actor;
    }

    public List<Actor> getActorList(List<String> uidList) {
        Criteria criteria = Criteria.where("_id").in(uidList);
        return mongoTemplate.find(new Query(criteria), Actor.class);
    }

    public void updateHeart(String uid, int heart) {
        Query query = new Query(Criteria.where("_id").is(new ObjectId(uid)));
        Update update = new Update();
        update.set("heart_got", heart);
        mongoTemplate.updateFirst(query, update, Actor.class);
    }

    public ActorData getActorDataFromCache(String uid) {
        return actorDao.getActorDataFromCache(uid);
    }
}
