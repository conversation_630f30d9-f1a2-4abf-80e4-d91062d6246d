package com.quhong.mysql.dao;

import com.quhong.cache.CacheMap;
import com.quhong.mysql.data.GiftBoxConfData;
import com.quhong.mysql.mapper.ustar.GiftBoxConfMapper;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/2/7
 */
@Component
public class GiftBoxConfDao {

    private static final String ALL = "ALL";

    private static final long CACHE_TIME_MILLIS = 10 * 60 * 1000L;

    private final CacheMap<String, List<GiftBoxConfData>> cacheMap;

    @Resource
    private GiftBoxConfMapper giftBoxConfMapper;

    public GiftBoxConfDao() {
        this.cacheMap = new CacheMap<>(CACHE_TIME_MILLIS);
    }

    @PostConstruct
    public void postInit() {
        cacheMap.start();
    }

    public List<GiftBoxConfData> getGiftBoxConfDataList() {
        List<GiftBoxConfData> list = cacheMap.getData(ALL);
        if (!CollectionUtils.isEmpty(list)) {
            return list;
        }
        list = giftBoxConfMapper.getGiftBoxConfDataList();
        if (!CollectionUtils.isEmpty(list)) {
            cacheMap.cacheData(ALL, list);
        }
        return list;
    }
}
