package com.quhong.mysql.dao;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.quhong.cache.CacheMap;
import com.quhong.mysql.data.MomentTopicMemberData;
import com.quhong.mysql.data.PetFeedData;
import com.quhong.mysql.mapper.ustar.PetFeedMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.context.annotation.Lazy;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.List;

/**
 * 宠物活动喂养表 DAO
 */
@Lazy
@Component
public class PetFeedDao {
    private static final Logger logger = LoggerFactory.getLogger(PetFeedDao.class);

    @PostConstruct
    public void postInit() {
    }


    @Resource
    private PetFeedMapper petFeedMapper;


    /**
     * 插入新的宠物喂养数据
     */
    public int insert(PetFeedData petFeedData) {
        int result = 0;
        try {
            result = petFeedMapper.insert(petFeedData);
        } catch (Exception e) {
            logger.info("insert pet feed data error. {}", e.getMessage(), e);
        }
        return result;
    }

    /**
     * 根据uid更新宠物喂养数据
     */
    public int updateByUid(PetFeedData petFeedData) {
        int result = 0;
        try {
            UpdateWrapper<PetFeedData> updateWrapper = Wrappers.update();
            updateWrapper.lambda()
                    .eq(PetFeedData::getUid, petFeedData.getUid())
                    .set(PetFeedData::getEatTotalFood, petFeedData.getEatTotalFood())
                    .set(PetFeedData::getFood, petFeedData.getFood())
                    .set(PetFeedData::getToys, petFeedData.getToys())
                    .set(PetFeedData::getMtime, petFeedData.getMtime());
            result = petFeedMapper.update(null, updateWrapper);
        } catch (Exception e) {
            logger.info("updateByUid pet feed data error. {}", e.getMessage(), e);
        }
        return result;


    }

    public int updateOne(PetFeedData petFeedData) {
        return petFeedMapper.updateById(petFeedData);
    }


    /**
     * 根据uid查询宠物喂养数据
     */
    public PetFeedData selectByUid(String uid) {
        QueryWrapper<PetFeedData> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("uid", uid);
        return petFeedMapper.selectOne(queryWrapper);
    }

    /**
     * 根据宠物类型查询宠物列表
     */
    public List<PetFeedData> selectByPetType(Integer petType) {
        QueryWrapper<PetFeedData> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("pet_type", petType);
        return petFeedMapper.selectList(queryWrapper);
    }

    /**
     * 根据宠物状态查询宠物列表
     */
    public List<PetFeedData> selectByPetStatus(Integer petStatus) {
        QueryWrapper<PetFeedData> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("pet_status", petStatus);
        return petFeedMapper.selectList(queryWrapper);
    }

    /**
     * 查询所有宠物数据
     */
    public List<PetFeedData> selectAll() {
        return petFeedMapper.selectList(null);
    }


    /**
     * 根据id删除宠物数据
     */
    public int deleteById(Integer id) {
        return petFeedMapper.deleteById(id);
    }

    /**
     * 根据uid删除宠物数据
     */
    public int deleteByUid(String uid) {
        QueryWrapper<PetFeedData> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("uid", uid);
        int result = petFeedMapper.delete(queryWrapper);
        return result;
    }
}
