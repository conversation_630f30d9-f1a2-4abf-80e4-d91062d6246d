package com.quhong.mysql.dao;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.quhong.mysql.data.GiftBoxRecordData;
import com.quhong.mysql.mapper.ustar.GiftBoxRecordMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;
import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/2/8
 */
@Component
public class GiftBoxRecordDao {

    private static final Logger logger = LoggerFactory.getLogger(GiftBoxRecordDao.class);

    @Resource
    private GiftBoxRecordMapper giftBoxRecordMapper;

    public void insert(GiftBoxRecordData data) {
        giftBoxRecordMapper.insert(data);
    }

    public List<GiftBoxRecordData> selectList(Integer boxId) {
        QueryWrapper<GiftBoxRecordData> queryWrapper = new QueryWrapper<>();
        queryWrapper.eq("box_id", boxId);
        return giftBoxRecordMapper.selectList(queryWrapper);
    }
}
