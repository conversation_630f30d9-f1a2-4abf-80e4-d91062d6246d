package com.quhong.mysql.dao;

import com.baomidou.mybatisplus.core.conditions.query.QueryWrapper;
import com.baomidou.mybatisplus.core.conditions.update.UpdateWrapper;
import com.baomidou.mybatisplus.core.toolkit.Wrappers;
import com.quhong.core.utils.DateHelper;
import com.quhong.mysql.data.GiftBoxData;
import com.quhong.mysql.mapper.ustar.GiftBoxMapper;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

import javax.annotation.Resource;

/**
 * <AUTHOR>
 * @date 2023/2/7
 */
@Component
public class GiftBoxDao {

    private static final Logger logger = LoggerFactory.getLogger(GiftBoxDao.class);

    @Resource
    private GiftBoxMapper giftBoxMapper;

    public void insert(GiftBoxData data) {
        try {
            giftBoxMapper.insert(data);
        } catch (Exception e) {
            logger.error("insert gift box data error. {}", e.getMessage(), e);
        }
    }

    public GiftBoxData selectOne(Integer boxId) {
        return giftBoxMapper.selectById(boxId);
    }

    public void update(GiftBoxData data) {
        giftBoxMapper.updateById(data);
    }

    public void updateStatus(int boxId, int status) {
        UpdateWrapper<GiftBoxData> updateWrapper = Wrappers.update();
        updateWrapper.lambda()
                .eq(GiftBoxData::getId, boxId)
                .set(GiftBoxData::getStatus, status)
                .set(GiftBoxData::getmTime, DateHelper.getNowSeconds());
        giftBoxMapper.update(null, updateWrapper);
    }
}
