package com.quhong.mysql.data;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

/**
 * <AUTHOR>
 * @date 2023/2/7
 */
@TableName("t_gift_box")
public class GiftBoxData {

    /**
     * id
     */
    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * 用户id
     */
    private String uid;

    /**
     * 房间id
     */
    private String roomId;

    /**
     * 状态：2已结算，1有效，0无效
     */
    private Integer status;

    /**
     * 礼物红包类型
     */
    private Integer boxType;

    /**
     * 附带消息
     */
    private String msg;

    /**
     * 创建时间
     */
    private Integer cTime;

    /**
     * 修改时间
     */
    private Integer mTime;

    public GiftBoxData() {
    }

    public GiftBoxData(String uid, String roomId, Integer status, Integer boxType, String msg, Integer cTime, Integer mTime) {
        this.uid = uid;
        this.roomId = roomId;
        this.status = status;
        this.boxType = boxType;
        this.msg = msg;
        this.cTime = cTime;
        this.mTime = mTime;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public String getRoomId() {
        return roomId;
    }

    public void setRoomId(String roomId) {
        this.roomId = roomId;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getBoxType() {
        return boxType;
    }

    public void setBoxType(Integer boxType) {
        this.boxType = boxType;
    }

    public String getMsg() {
        return msg;
    }

    public void setMsg(String msg) {
        this.msg = msg;
    }

    public Integer getcTime() {
        return cTime;
    }

    public void setcTime(Integer cTime) {
        this.cTime = cTime;
    }

    public Integer getmTime() {
        return mTime;
    }

    public void setmTime(Integer mTime) {
        this.mTime = mTime;
    }

    @Override
    public String toString() {
        return "GiftBoxData{" +
                "id=" + id +
                ", uid='" + uid + '\'' +
                ", roomId='" + roomId + '\'' +
                ", status=" + status +
                ", boxType=" + boxType +
                ", msg='" + msg + '\'' +
                ", cTime=" + cTime +
                ", mTime=" + mTime +
                '}';
    }
}
