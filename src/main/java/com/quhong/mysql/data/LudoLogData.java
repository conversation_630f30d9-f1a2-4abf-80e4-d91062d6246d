package com.quhong.mysql.data;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.quhong.core.utils.DateHelper;

/**
 * Ludo中奖记录
 */
@TableName("t_ludo_log")
public class LudoLogData {

    @TableId(type = IdType.AUTO)
    private Integer id;
    private String uid;
    private String gameId;
    private Integer earnBeans;// 赚的的钻石
    private Integer ctime;

    public LudoLogData() {
    }

    public LudoLogData(String uid, String gameId, Integer earnBeans) {
        this.uid = uid;
        this.gameId = gameId;
        this.earnBeans = earnBeans;
        this.ctime = DateHelper.getNowSeconds();
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public String getGameId() {
        return gameId;
    }

    public void setGameId(String gameId) {
        this.gameId = gameId;
    }

    public Integer getEarnBeans() {
        return earnBeans;
    }

    public void setEarnBeans(Integer earnBeans) {
        this.earnBeans = earnBeans;
    }

    public Integer getCtime() {
        return ctime;
    }

    public void setCtime(Integer ctime) {
        this.ctime = ctime;
    }
}
