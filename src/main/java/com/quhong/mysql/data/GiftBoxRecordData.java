package com.quhong.mysql.data;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

/**
 * <AUTHOR>
 * @date 2023/2/8
 */
@TableName("t_gift_box_record")
public class GiftBoxRecordData {

    /**
     * id
     */
    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * 用户id
     */
    private String uid;

    /**
     * room_id
     */
    private String roomId;

    /**
     * 礼物红包类型
     */
    private Integer boxId;

    /**
     * 礼物id
     */
    private Integer giftId;

    /**
     * 创建时间
     */
    private Integer cTime;

    public GiftBoxRecordData() {
    }

    public GiftBoxRecordData(String uid, String roomId, Integer boxId, Integer giftId, Integer cTime) {
        this.uid = uid;
        this.roomId = roomId;
        this.boxId = boxId;
        this.giftId = giftId;
        this.cTime = cTime;
    }

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public String getRoomId() {
        return roomId;
    }

    public void setRoomId(String roomId) {
        this.roomId = roomId;
    }

    public Integer getBoxId() {
        return boxId;
    }

    public void setBoxId(Integer boxId) {
        this.boxId = boxId;
    }

    public Integer getGiftId() {
        return giftId;
    }

    public void setGiftId(Integer giftId) {
        this.giftId = giftId;
    }

    public Integer getcTime() {
        return cTime;
    }

    public void setcTime(Integer cTime) {
        this.cTime = cTime;
    }
}
