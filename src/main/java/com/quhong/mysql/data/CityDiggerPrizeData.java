package com.quhong.mysql.data;

import com.quhong.core.utils.DateHelper;
import com.quhong.vo.PrizeVo;

import java.io.Serializable;

/**
 * 城市寻宝中奖记录实体
 */
public class CityDiggerPrizeData implements Serializable {

    private static final long serialVersionUID = 8160683114242998189L;

    private int id;
    private String uid;
    private String name;
    private String prizeName;
    private String prizeNameAr;
    private String prizeType;//gift、mic、buddle、ride、ripple、diamond
    private int diamond;//钻石
    private int sourceId;//资源id
    private int day;//资源天数
    private int num;//资源数量
    private String link;
    private int ctime;

    public CityDiggerPrizeData() {
    }

    public CityDiggerPrizeData(String uid, String name, PrizeVo prizeVo) {
        this.uid = uid;
        this.name = name;
        this.prizeName = prizeVo.getName();
        this.prizeNameAr = prizeVo.getNameAr();
        this.prizeType = prizeVo.getType();
        this.diamond = prizeVo.getDiamond();
        this.sourceId = prizeVo.getSourceId();
        this.day = prizeVo.getDay();
        this.num = prizeVo.getNum();
        this.link = prizeVo.getLink();
        this.ctime = DateHelper.getNowSeconds();
    }

    public int getId() {
        return id;
    }

    public void setId(int id) {
        this.id = id;
    }

    public String getUid() {
        return uid;
    }

    public void setUid(String uid) {
        this.uid = uid;
    }

    public String getName() {
        return name;
    }

    public void setName(String name) {
        this.name = name;
    }

    public String getPrizeNameAr() {
        return prizeNameAr;
    }

    public void setPrizeNameAr(String prizeNameAr) {
        this.prizeNameAr = prizeNameAr;
    }

    public String getPrizeName() {
        return prizeName;
    }

    public void setPrizeName(String prizeName) {
        this.prizeName = prizeName;
    }

    public String getPrizeType() {
        return prizeType;
    }

    public void setPrizeType(String prizeType) {
        this.prizeType = prizeType;
    }

    public int getDiamond() {
        return diamond;
    }

    public void setDiamond(int diamond) {
        this.diamond = diamond;
    }

    public int getSourceId() {
        return sourceId;
    }

    public void setSourceId(int sourceId) {
        this.sourceId = sourceId;
    }

    public int getDay() {
        return day;
    }

    public void setDay(int day) {
        this.day = day;
    }

    public int getNum() {
        return num;
    }

    public void setNum(int num) {
        this.num = num;
    }

    public String getLink() {
        return link;
    }

    public void setLink(String link) {
        this.link = link;
    }

    public int getCtime() {
        return ctime;
    }

    public void setCtime(int ctime) {
        this.ctime = ctime;
    }
}
