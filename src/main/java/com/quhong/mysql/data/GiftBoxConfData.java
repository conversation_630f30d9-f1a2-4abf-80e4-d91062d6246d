package com.quhong.mysql.data;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

/**
 * <AUTHOR>
 * @date 2023/2/7
 */
@TableName("t_gift_box_conf")
public class GiftBoxConfData {

    /**
     * id
     */
    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * 礼物红包类型
     */
    private Integer boxType;

    /**
     * 礼物总价值
     */
    private Integer boxBeans;

    /**
     * 礼物id
     */
    private Integer giftId;

    /**
     * 礼物数量
     */
    private Integer giftNum;

    /**
     * 状态：1有效，0无效
     */
    private Integer status;

    /**
     * 创建时间
     */
    private Integer cTime;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public Integer getBoxType() {
        return boxType;
    }

    public void setBoxType(Integer boxType) {
        this.boxType = boxType;
    }

    public Integer getBoxBeans() {
        return boxBeans;
    }

    public void setBoxBeans(Integer boxBeans) {
        this.boxBeans = boxBeans;
    }

    public Integer getGiftId() {
        return giftId;
    }

    public void setGiftId(Integer giftId) {
        this.giftId = giftId;
    }

    public Integer getGiftNum() {
        return giftNum;
    }

    public void setGiftNum(Integer giftNum) {
        this.giftNum = giftNum;
    }

    public Integer getStatus() {
        return status;
    }

    public void setStatus(Integer status) {
        this.status = status;
    }

    public Integer getcTime() {
        return cTime;
    }

    public void setcTime(Integer cTime) {
        this.cTime = cTime;
    }
}
