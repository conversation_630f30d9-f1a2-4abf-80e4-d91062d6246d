package com.quhong.mysql.data;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;

/**
 * 转盘游戏记录
 *
 * <AUTHOR>
 * @date 2023/1/31
 */
@TableName("t_turntable_record")
public class TurntableRecordData {

    /**
     * id
     */
    @TableId(type = IdType.AUTO)
    private Integer id;

    /**
     * 房间id
     */
    private String roomId;

    /**
     * 游戏id
     */
    private String gameId;

    /**
     * 加入费用
     */
    private Integer joinBeans;

    /**
     * 玩家人数下限
     */
    private Integer minPlayers;

    /**
     * 玩家人数上限
     */
    private Integer maxPlayers;

    /**
     * 玩家人数
     */
    private Integer playersNum;

    /**
     * 总钻石数
     */
    private Integer totalBeans;

    /**
     * 赢家获得钻石数
     */
    private Integer winBeans;

    /**
     * 赢家
     */
    private String winner;

    /**
     * 转盘创建人
     */
    private String creator;

    /**
     * 创建时间
     */
    private Integer ctime;

    public Integer getId() {
        return id;
    }

    public void setId(Integer id) {
        this.id = id;
    }

    public String getRoomId() {
        return roomId;
    }

    public void setRoomId(String roomId) {
        this.roomId = roomId;
    }

    public String getGameId() {
        return gameId;
    }

    public void setGameId(String gameId) {
        this.gameId = gameId;
    }

    public Integer getJoinBeans() {
        return joinBeans;
    }

    public void setJoinBeans(Integer joinBeans) {
        this.joinBeans = joinBeans;
    }

    public Integer getMinPlayers() {
        return minPlayers;
    }

    public void setMinPlayers(Integer minPlayers) {
        this.minPlayers = minPlayers;
    }

    public Integer getMaxPlayers() {
        return maxPlayers;
    }

    public void setMaxPlayers(Integer maxPlayers) {
        this.maxPlayers = maxPlayers;
    }

    public Integer getPlayersNum() {
        return playersNum;
    }

    public void setPlayersNum(Integer playersNum) {
        this.playersNum = playersNum;
    }

    public Integer getTotalBeans() {
        return totalBeans;
    }

    public void setTotalBeans(Integer totalBeans) {
        this.totalBeans = totalBeans;
    }

    public Integer getWinBeans() {
        return winBeans;
    }

    public void setWinBeans(Integer winBeans) {
        this.winBeans = winBeans;
    }

    public String getWinner() {
        return winner;
    }

    public void setWinner(String winner) {
        this.winner = winner;
    }

    public String getCreator() {
        return creator;
    }

    public void setCreator(String creator) {
        this.creator = creator;
    }

    public Integer getCtime() {
        return ctime;
    }

    public void setCtime(Integer ctime) {
        this.ctime = ctime;
    }
}
