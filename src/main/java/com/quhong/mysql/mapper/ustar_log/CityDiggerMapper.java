package com.quhong.mysql.mapper.ustar_log;

import com.quhong.mysql.data.CityDiggerData;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Select;
import org.apache.ibatis.annotations.Update;

import java.util.List;

public interface CityDiggerMapper {

    @Select("select id,uid,step,consume,received,ctime,mtime from t_city_digger where uid=#{uid}")
    CityDiggerData getByUid(String uid);

    @Insert("insert into t_city_digger (uid,step,consume,received,ctime,mtime) " +
            "values (#{uid},#{step},#{consume},#{received},#{ctime},#{mtime})")
    void insert(CityDiggerData newData);

    @Update("update t_city_digger set step=#{step},consume=#{consume},received=#{received},mtime=#{mtime} where id=#{id}")
    void update(CityDiggerData data);

    @Select("select id,uid,step,consume from t_city_digger where consume>0 order by consume desc limit 20")
    List<CityDiggerData> getRanking();
}
