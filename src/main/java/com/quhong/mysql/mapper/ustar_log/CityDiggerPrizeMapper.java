package com.quhong.mysql.mapper.ustar_log;

import com.quhong.mysql.data.CityDiggerPrizeData;
import org.apache.ibatis.annotations.Insert;
import org.apache.ibatis.annotations.Param;
import org.apache.ibatis.annotations.Select;

import java.util.List;

public interface CityDiggerPrizeMapper {

    @Select("SELECT * FROM t_city_digger_prize main_table RIGHT JOIN " +
            "(SELECT id FROM t_city_digger_prize where uid=#{uid} order by ctime desc limit #{offset},#{pageSize}) " +
            "temp_table ON temp_table.id = main_table.id")
    List<CityDiggerPrizeData> getByUid(@Param("uid") String uid, @Param("offset") int offset, @Param("pageSize") int pageSize);

    @Insert("insert into t_city_digger_prize (uid,name,prize_name,prize_name_ar,prize_type,diamond,source_id,day,num,link,ctime) " +
            "values (#{uid},#{name},#{prizeName},#{prizeNameAr},#{prizeType},#{diamond},#{sourceId},#{day},#{num},#{link},#{ctime})")
    void insert(CityDiggerPrizeData data);

    @Select("select * from t_city_digger_prize order by ctime desc limit 20")
    List<CityDiggerPrizeData> getRecentPrize();
}
