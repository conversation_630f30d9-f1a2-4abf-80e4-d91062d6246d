package com.quhong.mysql.mapper.ustar_log;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.quhong.mysql.data.TurntablePlayerRecordData;
import org.apache.ibatis.annotations.Insert;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/1/31
 */
public interface TurntablePlayerRecordMapper extends BaseMapper<TurntablePlayerRecordData> {

    @Insert("<script>" +
            "INSERT INTO t_turntable_player_record (room_id, game_id, uid, changed, is_winner, ctime) VALUES" +
            "<foreach collection='list' item='item' separator=','>" +
            "(#{item.roomId}, #{item.gameId}, #{item.uid}, #{item.changed}, #{item.isWinner}, #{item.ctime})" +
            "</foreach>"+
            "</script>")
    void batchInsert(List<TurntablePlayerRecordData> list);
}
