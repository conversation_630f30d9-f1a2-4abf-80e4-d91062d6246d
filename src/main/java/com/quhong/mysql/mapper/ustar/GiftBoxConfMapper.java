package com.quhong.mysql.mapper.ustar;

import com.baomidou.mybatisplus.core.mapper.BaseMapper;
import com.quhong.mysql.data.GiftBoxConfData;
import org.apache.ibatis.annotations.Select;

import java.util.List;

/**
 * <AUTHOR>
 * @date 2023/2/7
 */
public interface GiftBoxConfMapper extends BaseMapper<GiftBoxConfData> {

    @Select({
            "<script>",
            "SELECT id, box_type, box_beans, gift_id, gift_num, `status`, c_time from `t_gift_box_conf` WHERE box_type in ",
            "(SELECT box_type FROM `t_gift_box_conf` WHERE `status` = 1 GROUP BY box_type)",
            "</script>"
    })
    List<GiftBoxConfData> getGiftBoxConfDataList();
}
