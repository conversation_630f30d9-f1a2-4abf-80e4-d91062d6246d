package com.quhong.ludo.service;

import com.quhong.enums.GameHttpCode;
import com.quhong.exception.GameException;
import com.quhong.ludo.constant.NodeConstant;
import com.quhong.ludo.data.GameInfo;
import com.quhong.ludo.data.NodeData;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

@Component
public class BoardNodeService {
    private static final Logger logger = LoggerFactory.getLogger(BoardNodeService.class);

    /**
     * 获取指定格子的棋盘数据
     *
     * @param nodeId 格子id
     */
    public NodeData getNodeData(GameInfo gameInfo, int nodeId) {
        if (nodeId < NodeConstant.FIRST_NODE || nodeId > NodeConstant.LAST_NODE) {
            logger.error("cannot find node data, gameId={} nodeId={}", gameInfo.getGameId(), nodeId);
            throw new GameException(GameHttpCode.SERVER_ERROR, gameInfo.toWebData());
        }
        return gameInfo.getBoardNodeList().get(nodeId - 1);
    }

    /**
     * 在棋盘中移除棋子
     *
     * @param nodeId     格子id
     * @param chessmanId 棋子id
     */
    public void removeChessman(GameInfo gameInfo, int nodeId, String chessmanId) {
        if (nodeId < 0) {
            return;
        }
        NodeData nodeData = getNodeData(gameInfo, nodeId);
        nodeData.getChessmanList().removeIf(chessman -> chessmanId.equals(chessman.getChessmanId()));
    }

}
