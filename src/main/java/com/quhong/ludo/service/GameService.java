package com.quhong.ludo.service;

import com.quhong.core.concurrency.BaseTaskFactory;
import com.quhong.core.concurrency.queues.SlowTaskQueue;
import com.quhong.core.concurrency.tasks.Task;
import com.quhong.core.timers.LoopTask;
import com.quhong.core.timers.TimerService;
import com.quhong.core.utils.DateHelper;
import com.quhong.enums.GameHttpCode;
import com.quhong.exception.GameException;
import com.quhong.ludo.constant.LudoConstant;
import com.quhong.ludo.data.GameInfo;
import com.quhong.ludo.data.GamePlayer;
import com.quhong.ludo.game.Game;
import com.quhong.ludo.game.NormalGame;
import com.quhong.redis.LudoRedis;
import com.quhong.service.LudoService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.util.StringUtils;

import javax.annotation.PostConstruct;
import javax.annotation.Resource;
import java.util.List;
import java.util.Map;
import java.util.concurrent.ConcurrentHashMap;

@Component
public class GameService extends SlowTaskQueue {
    private static final Logger logger = LoggerFactory.getLogger(GameService.class);
    private static final int LOOP_TIME = 60 * 1000;
    private static final int CHECK_TIME = 5 * 60;
    private static final int CLOSE_TIME = 10 * 60; // 全部玩家托管时超时自动关闭时间
    private static final int LIMIT_TIME = 2 * 60 * 60; // 两小时后自动关闭游戏（防止卡死）
    private final Map<String, Game> gameMap = new ConcurrentHashMap<>();

    @Resource
    private LudoRedis ludoRedis;
    @Resource
    private LudoService ludoService;

    @PostConstruct
    public void postInit() {
        // 从redis恢复用户数据
        BaseTaskFactory.getFactory().addSlow(new Task() {
            @Override
            protected void execute() {
                recoverGame();
            }
        });
        TimerService.getService().addDelay(new LoopTask(this, LOOP_TIME) {
            @Override
            protected void execute() {
                onTick();
            }
        });
    }

    private void recoverGame() {
        List<GameInfo> allGameInfo = ludoRedis.getAllGameInfo();
        if (allGameInfo.size() > 0) {
            synchronized (gameMap) {
                for (GameInfo gameInfo : allGameInfo) {
                    if (gameInfo.getStatus() == LudoConstant.MATCHING
                            || gameInfo.getStatus() == LudoConstant.PROCESSING) {
                        logger.info("recover ludo game from redis gameId={}", gameInfo.getGameId());
                        Game game = new NormalGame(gameInfo);
                        addGame(game);
                        for (GamePlayer gamePlayer : gameInfo.getPlayerList()) {
                            game.addPlayer(gamePlayer);
                            ludoRedis.savePlayerData(gamePlayer.getUid(), gameInfo.getGameId());
                        }
                    } else {
                        logger.info("remove finished or closed game gameId={}", gameInfo.getGameId());
                        ludoRedis.removeGameInfo(gameInfo.getGameId(), gameInfo.getRoomId());
                    }
                }
            }
        }
    }

    public Game getGame(String gameId) {
        if (StringUtils.isEmpty(gameId)) {
            return null;
        }
        Game game = gameMap.get(gameId);
        if (game == null) {
            synchronized (gameMap) {
                game = gameMap.get(gameId);
                if (game == null) {
                    logger.info("get game from game map. game not found gameId={}", gameId);
                    throw new GameException(GameHttpCode.GAME_NOT_FOUND);
                }
            }
        }
        return game;
    }

    public void addGame(Game game) {
        Game oldGame = gameMap.put(game.getGameId(), game);
        if (oldGame != null && oldGame != game) {
            oldGame.dispose();
            logger.error("add game error. old game exists. gameId={}", game.getGameId());
        }
    }

    /**
     * 移除游戏，并清除用户已在游戏的标记
     *
     * @param refund 是否需要退还游戏币
     */
    public void removeGame(GameInfo gameInfo, boolean refund) {
        Game game = gameMap.remove(gameInfo.getGameId());
        if (game == null) {
            logger.error("remove game. can not find game. gameId={}", gameInfo.getGameId());
        } else {
            logger.info("remove game. gameId={}", game.getGameId());
            game.dispose();
            for (GamePlayer gamePlayer : game.getPlayerMap().values()) {
                if (refund) {
                    // 未开始的游戏返还游戏币
                    if (LudoConstant.NONE == gameInfo.getStatus()
                            || LudoConstant.MATCHING == gameInfo.getStatus()
                            || LudoConstant.STUCK_GAME == gameInfo.getStatus()) {
                        ludoService.returnGameCurrency(game.getGameInfo(), gamePlayer.getUid());
                    }
                }
                // 移除玩家，清除用户已在游戏的标记
                game.removePlayer(gamePlayer.getUid());
                ludoRedis.removePlayerData(gamePlayer.getUid());
            }
            ludoRedis.removeRoomLudo(game.getRoomId());
        }
        ludoRedis.removeGameInfo(gameInfo.getGameId(), gameInfo.getRoomId());
    }

    public Game getGameByRoomId(String roomId) {
        String gameId = ludoRedis.getGameIdByRoomId(roomId);
        try {
            return getGame(gameId);
        } catch (Exception e) {
            return null;
        }
    }

    public GameInfo getGameInfoByRoomId(String roomId) {
        String gameId = ludoRedis.getGameIdByRoomId(roomId);
        if (StringUtils.isEmpty(gameId)) {
            return null;
        }
        try {
            Game game = getGame(gameId);
            return game.getGameInfo();
        } catch (Exception e) {
            logger.info("get game from game map error. game not found gameId={}", gameId);
            return null;
        }
    }

    public GameInfo check(String roomId, String uid) {
        String gameId = ludoRedis.getGameIdByRoomId(roomId);
        if (StringUtils.isEmpty(gameId)) {
            return null;
        }
        try {
            return ludoService.watch(uid, getGame(gameId), LudoConstant.WATCH);
        } catch (Exception e) {
            return null;
        }
    }

    private void onTick() {
        logger.info("dismiss game. gameMap.size={}", gameMap.size());
        for (Game game : gameMap.values()) {
            dismissGame(game);
        }
    }

    @SuppressWarnings("SynchronizationOnLocalVariableOrMethodParameter")
    private void dismissGame(Game game) {
        GameInfo gameInfo = game.getGameInfo();
        logger.info("dismiss game. gameId={}", game.getGameId());
        // 关闭五分钟未开始的游戏
        if ((LudoConstant.NONE == gameInfo.getStatus() || LudoConstant.MATCHING == gameInfo.getStatus())
                && DateHelper.getNowSeconds() - gameInfo.getCreateTime() > CHECK_TIME) {
            try {
                synchronized (gameInfo) {
                    logger.info("dismiss not started game. gameId={}", game.getGameId());
                    removeGame(gameInfo, true);
                    // 发送游戏超时自动关闭消息
                    ludoService.sendLudoChangeMsg(gameInfo, null, LudoConstant.CLOSE_GAME);
                    ludoService.sendLudoStatusChangeMsg(gameInfo.getRoomId(), null, LudoConstant.TIME_OUT_CLOSE);
                }
            } catch (Exception e) {
                logger.error("dismiss game error gameId={}", gameInfo.getGameId(), e);
            }
        } else if (LudoConstant.FINISH == gameInfo.getStatus()) {
            logger.info("dismiss finished game. gameId={}", game.getGameId());
            removeGame(gameInfo, false);
        } else if (DateHelper.getNowSeconds() - gameInfo.getModifyTime() > CLOSE_TIME) {
            List<GamePlayer> playerList = gameInfo.getPlayerList();
            long hostingCount = playerList.stream()
                    .filter(player -> player.getStatus() == LudoConstant.HOSTING || player.getStatus() == LudoConstant.EXIT).count();
            if (hostingCount == playerList.size()) {
                logger.info("dismiss not playing game. gameId={}", game.getGameId());
                ludoService.sendLudoChangeMsg(gameInfo, null, LudoConstant.CLOSE_GAME);
                ludoService.sendLudoStatusChangeMsg(gameInfo.getRoomId(), null, LudoConstant.TIME_OUT_CLOSE);
                removeGame(gameInfo, false);
            }
        }
        if (gameInfo.getStartTime() != 0 && DateHelper.getNowSeconds() - gameInfo.getStartTime() > LIMIT_TIME) {
            logger.error("dismiss stuck game. gameId={}", game.getGameId());
            ludoService.sendLudoChangeMsg(gameInfo, null, LudoConstant.CLOSE_GAME);
            ludoService.sendLudoStatusChangeMsg(gameInfo.getRoomId(), null, LudoConstant.TIME_OUT_CLOSE);
            gameInfo.setStatus(LudoConstant.STUCK_GAME);
            removeGame(gameInfo, true);
        }
    }
}
