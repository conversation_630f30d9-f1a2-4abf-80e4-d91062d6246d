package com.quhong.ludo.service;

import com.quhong.ludo.data.Chessman;
import com.quhong.ludo.data.GamePlayer;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;

@Component
public class ChessmanService {
    private static final Logger logger = LoggerFactory.getLogger(ChessmanService.class);


    public Chessman getChessman(GamePlayer gamePlayer, String chessmanId) {
        for (Chessman chessman : gamePlayer.getChessmanList()) {
            if (chessman.getChessmanId().equals(chessmanId)) {
                return chessman;
            }
        }
        logger.error("cannot find chessman, chessmanId={}", chessmanId);
        return null;
    }
}
