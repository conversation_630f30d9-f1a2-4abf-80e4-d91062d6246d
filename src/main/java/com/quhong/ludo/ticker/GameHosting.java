package com.quhong.ludo.ticker;

import com.quhong.core.utils.DateHelper;
import com.quhong.core.utils.SpringUtils;
import com.quhong.ludo.constant.LudoConstant;
import com.quhong.ludo.constant.NodeConstant;
import com.quhong.ludo.data.Chessman;
import com.quhong.ludo.data.GameInfo;
import com.quhong.ludo.data.GamePlayer;
import com.quhong.ludo.data.OpData;
import com.quhong.ludo.game.Game;
import com.quhong.ludo.service.BoardNodeService;
import com.quhong.service.LudoService;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.ArrayList;
import java.util.List;

@Component
public class GameHosting {
    private static final Logger logger = LoggerFactory.getLogger(GameHosting.class);
    private static GameHosting instance;

    @Resource
    private LudoService ludoService;
    @Resource
    private BoardNodeService boardNodeService;

    public static GameHosting getInstance() {
        if (null == instance) {
            instance = SpringUtils.getBean(GameHosting.class);
        }
        return instance;
    }

    public GameHosting() {
        instance = this;
    }

    public void tick(Game game) {
        gameHosting(game);
    }

    /**
     * 游戏托管
     */
    @SuppressWarnings("SynchronizationOnLocalVariableOrMethodParameter")
    private void gameHosting(Game game) {
        GameInfo gameInfo = game.getGameInfo();
        synchronized (gameInfo) {
            if (gameInfo.getStatus() == LudoConstant.NONE) {
                return;
            }
            OpData opData = gameInfo.getOpData();
            if (LudoConstant.PROCESSING == gameInfo.getStatus()) {
                String opUid = opData.getOpUid();
                GamePlayer gamePlayer = game.getPlayerMap().get(opUid);
                if (gamePlayer.getStatus() == LudoConstant.HOSTING
                        && DateHelper.getNowSeconds() - opData.getActionTime() > LudoConstant.HOSTING_ACTION_TIME) {
                    hosting(game, gamePlayer);
                } else if (DateHelper.getNowSeconds() - opData.getActionTime() > LudoConstant.HOSTING_TIME) {
                    gamePlayer.setStatus(LudoConstant.HOSTING);
                    hosting(game, gamePlayer);
                }
            }
        }
    }

    private void hosting(Game game, GamePlayer gamePlayer) {
        GameInfo gameInfo = game.getGameInfo();
        try {
            OpData opData = gameInfo.getOpData();
            if (0 == gamePlayer.getHostingStartTime()) {
                gamePlayer.setHostingStartTime(DateHelper.getNowSeconds());
            }
            if (opData.getOpType() == LudoConstant.THROW_DICE || opData.getNext() == LudoConstant.THROW_DICE_AGAIN) {
                ludoService.throwDice(gamePlayer.getUid(), game, true);
            } else if (opData.getOpType() == LudoConstant.CHESSMAN_MOVE) {
                hostingAction(game, gamePlayer, opData);
                ludoService.gameInfoChange(game);
            }
        } catch (Exception e) {
            logger.error("hosting player error gameId={} opUid={}", gameInfo.getGameId(), gamePlayer.getUid());
        }
    }

    private void hostingAction(Game game, GamePlayer gamePlayer, OpData opData) {
        GameInfo gameInfo = game.getGameInfo();
        if (CollectionUtils.isEmpty(opData.getDiceValueList())) {
            lastAction(gameInfo, gamePlayer, opData);
            return;
        }
        int finalPoint = NodeConstant.getFinalPoint(gamePlayer.getSide());
        //操作优先级：到达终点>撞子>出基地>进入地球或五角星格子>走棋
        List<Integer> diceValueList = new ArrayList<>(opData.getDiceValueList());
        List<Chessman> playerChessmanList = gamePlayer.getChessmanList();
        // 到达终点
        for (Integer dice : diceValueList) {
            for (Chessman chessman : playerChessmanList) {
                if (chessman.getNodeId() == finalPoint) {
                    continue;
                }
                if (chessman.getNodeId() + dice == finalPoint) {
                    ludoService.action(game, gamePlayer, diceValueList.indexOf(dice), chessman.getChessmanId(), true);
                    return;
                }
            }
        }
        // 撞子
        for (Integer dice : diceValueList) {
            for (Chessman chessman : playerChessmanList) {
                if (chessman.getNodeId() == chessman.getStartNodeId()) {
                    continue;
                }
                int nextNodeId = ludoService.getNextNodeId(chessman.getNodeId() + dice,
                        chessman.getNodeId(), gamePlayer.getSide());
                List<Chessman> chessmanList = boardNodeService.getNodeData(gameInfo, nextNodeId).getChessmanList();
                if (chessmanList.size() > 0) {
                    if (chessmanList.stream().anyMatch(chess -> !chess.getUid().equals(gamePlayer.getUid()))) {
                        ludoService.action(game, gamePlayer, diceValueList.indexOf(dice), chessman.getChessmanId(), true);
                        return;
                    }
                }
            }
        }
        // 出基地
        if (diceValueList.contains(6)) {
            for (Chessman chessman : playerChessmanList) {
                if (chessman.getNodeId() == chessman.getStartNodeId()) {
                    ludoService.action(game, gamePlayer, diceValueList.indexOf(6), chessman.getChessmanId(), true);
                    return;
                }
            }
        }
        // 进入地球或五角星格子
        for (Integer dice : diceValueList) {
            for (Chessman chessman : playerChessmanList) {
                if (chessman.getNodeId() == chessman.getStartNodeId()) {
                    continue;
                }
                int nextNodeId = ludoService.getNextNodeId(chessman.getNodeId() + dice,
                        chessman.getNodeId(), gamePlayer.getSide());
                if (NodeConstant.isProtectedNode(nextNodeId)) {
                    ludoService.action(game, gamePlayer, diceValueList.indexOf(dice), chessman.getChessmanId(), true);
                    return;
                }
            }
        }
        // 走棋，默认走第一个棋子
        for (Chessman chessman : playerChessmanList) {
            if (chessman.getNodeId() == chessman.getStartNodeId()) {
                continue;
            }
            // 选择可以移动的骰子
            for (Integer dice : diceValueList) {
                if (chessman.getNodeId() + dice < finalPoint) {
                    ludoService.action(game, gamePlayer, diceValueList.indexOf(dice), chessman.getChessmanId(), true);
                    return;
                }
            }
        }
        for (Chessman chessman : playerChessmanList) {
            if (chessman.getNodeId() == chessman.getStartNodeId() || chessman.getNodeId() == finalPoint) {
                continue;
            }
            ludoService.action(game, gamePlayer, 0, chessman.getChessmanId(), true);
            return;
        }
        // 托管异常，所有棋子都无法移动，切换到下一个玩家，如果快速模式有掷骰子机会则执行掷骰子
        logger.info("cannot find action, gameId={} side={} che1={} che2={} che3={} che4={}",
                game.getGameId(), gamePlayer.getSide(),
                gamePlayer.getChessmanList().get(0).getNodeId(),
                gamePlayer.getChessmanList().get(1).getNodeId(),
                gamePlayer.getChessmanList().get(2).getNodeId(),
                gamePlayer.getChessmanList().get(3).getNodeId());
        lastAction(gameInfo, gamePlayer, opData);
    }

    private void lastAction(GameInfo gameInfo, GamePlayer gamePlayer, OpData opData) {
        if (gamePlayer.getFastDiceCount() > 0) {
            // 清除无效的骰子?
            opData.getDiceValueList().clear();
            gamePlayer.setFastDiceCount(gamePlayer.getFastDiceCount() - 1);
            ludoService.setThrowDice(gameInfo, gamePlayer);
        } else {
            ludoService.setNextGameTurn(gameInfo);
        }
    }
}
