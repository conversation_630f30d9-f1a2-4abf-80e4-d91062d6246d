package com.quhong.ludo.game;

import com.quhong.core.dispose.Disposer;
import com.quhong.core.timers.LoopTask;
import com.quhong.core.timers.TimerService;
import com.quhong.ludo.data.GameInfo;

public abstract class AbstractGame extends Disposer implements Game {

    protected GameInfo gameInfo;
    protected LoopTask loopTask;

    @Override
    protected void doDispose() {
        removeTicker();
    }

    public AbstractGame(GameInfo gameInfo) {
        this.gameInfo = gameInfo;
        addTicker();
    }

    public void removeTicker() {
        if (loopTask != null) {
            loopTask.cancel();
            loopTask = null;
        }
    }

    public void addTicker() {
        removeTicker();
        loopTask = new LoopTask(2000) {
            @Override
            protected void execute() {
                try {
                    onTick();
                } catch (Exception e) {
                    logger.error("game tick error. gameId={} {}", gameInfo.getGameId(), e.getMessage());
                }
            }
        };
        TimerService.getService().addDelay(loopTask);
    }

    protected abstract void onTick();

    @Override
    public String getGameId() {
        return this.gameInfo.getGameId();
    }

    @Override
    public String getRoomId() {
        return this.gameInfo.getRoomId();
    }

    @Override
    public GameInfo getGameInfo() {
        return this.gameInfo;
    }
}
