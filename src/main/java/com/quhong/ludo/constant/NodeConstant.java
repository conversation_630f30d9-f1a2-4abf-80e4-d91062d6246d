package com.quhong.ludo.constant;

import com.quhong.ludo.data.NodeData;

import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

public class NodeConstant {

    public static final int PROTECTED = 1;
    public static final int LIST_FIVE_NODE = 2;
    public static final int FINAL_NODE = 3;
    public static final List<NodeData> NODE_DATA = new ArrayList<>();
    public static final Integer[] PROTECTED_NODE = {1, 14, 27, 40, 9, 22, 35, 48};
    public static final Integer[] BLUE_NODE = {1, 53, 54, 55, 56, 57, 58};
    public static final Integer[] RED_NODE = {27, 59, 60, 61, 62, 63, 64};
    public static final Integer[] GREEN_NODE = {14, 65, 66, 67, 68, 69, 70};
    public static final Integer[] YELLOW_NODE = {40, 71, 72, 73, 74, 75, 76};
    public static final int BLUE_OFFSET = 1;
    public static final int RED_OFFSET = 27;
    public static final int GREEN_OFFSET = 14;
    public static final int YELLOW_OFFSET = 40;
    public static final int REVERSION_NODE = 52;
    public static final int BLUE_TURNING = 51;
    public static final int RED_TURNING = 25;
    public static final int GREEN_TURNING = 12;
    public static final int YELLOW_TURNING = 38;
    public static final int BLUE_FINAL_START = 53;
    public static final int RED_FINAL_START = 59;
    public static final int GREEN_FINAL_START = 65;
    public static final int YELLOW_FINAL_START = 71;
    public static final int BLUE_FINAL = 58;
    public static final int RED_FINAL = 64;
    public static final int GREEN_FINAL = 70;
    public static final int YELLOW_FINAL = 76;
    public static final int FIRST_NODE = 1;
    public static final int LAST_NODE = 76;

    static {
        for (int i = FIRST_NODE; i <= LAST_NODE; i++) {
            NodeData nodeData = new NodeData();
            nodeData.setId(i);
            // 保护格
            if (isProtectedNode(i)) {
                nodeData.setType(PROTECTED);
            }
            // 颜色
            if (isBlueNode(i)) {
                nodeData.setSide(LudoConstant.BLUE_SIDE);
            } else if (isRedNode(i)) {
                nodeData.setSide(LudoConstant.RED_SIDE);
            } else if (isGreenNode(i)) {
                nodeData.setSide(LudoConstant.GREEN_SIDE);
            } else if (isYellowNode(i)) {
                nodeData.setSide(LudoConstant.YELLOW_SIDE);
            }
            if (i > 52) {
                if (i == BLUE_FINAL || i == RED_FINAL || i == GREEN_FINAL || i == YELLOW_FINAL) {
                    // 终点
                    nodeData.setType(FINAL_NODE);
                } else {
                    // 最后五格
                    nodeData.setType(LIST_FIVE_NODE);
                }
            }
            NODE_DATA.add(nodeData);
        }
    }

    public static int getSideOffset(int side) {
        if (side == LudoConstant.BLUE_SIDE) {
            return BLUE_OFFSET;
        } else if (side == LudoConstant.RED_SIDE) {
            return RED_OFFSET;
        } else if (side == LudoConstant.GREEN_SIDE) {
            return GREEN_OFFSET;
        } else {
            return YELLOW_OFFSET;
        }
    }

    public static boolean isFinalPoint(int side, int nodeId) {
        if (side == LudoConstant.BLUE_SIDE) {
            return BLUE_FINAL == nodeId;
        } else if (side == LudoConstant.RED_SIDE) {
            return RED_FINAL == nodeId;
        } else if (side == LudoConstant.GREEN_SIDE) {
            return GREEN_FINAL == nodeId;
        } else {
            return YELLOW_FINAL == nodeId;
        }
    }

    public static int getFinalPoint(int side) {
        if (side == LudoConstant.BLUE_SIDE) {
            return BLUE_FINAL;
        } else if (side == LudoConstant.RED_SIDE) {
            return RED_FINAL;
        } else if (side == LudoConstant.GREEN_SIDE) {
            return GREEN_FINAL;
        } else {
            return YELLOW_FINAL;
        }
    }

    public static int getTurning(int side) {
        if (side == LudoConstant.BLUE_SIDE) {
            return BLUE_TURNING;
        } else if (side == LudoConstant.RED_SIDE) {
            return RED_TURNING;
        } else if (side == LudoConstant.GREEN_SIDE) {
            return GREEN_TURNING;
        } else {
            return YELLOW_TURNING;
        }
    }

    public static int getFinialStart(int side) {
        if (side == LudoConstant.BLUE_SIDE) {
            return BLUE_FINAL_START;
        } else if (side == LudoConstant.RED_SIDE) {
            return RED_FINAL_START;
        } else if (side == LudoConstant.GREEN_SIDE) {
            return GREEN_FINAL_START;
        } else {
            return YELLOW_FINAL_START;
        }
    }

    public static boolean isProtectedNode(int nodeId) {
        return Arrays.stream(PROTECTED_NODE).anyMatch(integer -> integer == nodeId);
    }

    private static boolean isBlueNode(int nodeId) {
        return Arrays.stream(BLUE_NODE).anyMatch(integer -> integer == nodeId);
    }

    private static boolean isRedNode(int nodeId) {
        return Arrays.stream(RED_NODE).anyMatch(integer -> integer == nodeId);
    }

    private static boolean isGreenNode(int nodeId) {
        return Arrays.stream(GREEN_NODE).anyMatch(integer -> integer == nodeId);
    }

    private static boolean isYellowNode(int nodeId) {
        return Arrays.stream(YELLOW_NODE).anyMatch(integer -> integer == nodeId);
    }
}
