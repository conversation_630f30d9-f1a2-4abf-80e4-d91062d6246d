package com.quhong.ludo.constant;

public class LudoConstant {

    public static final int NONE = 0; // 无
    public static final int MATCHING = 1; // 匹配中
    public static final int PROCESSING = 2; // 进行中
    public static final int FINISH = 3; // 结束
    public static final int CLOSE_GAME = 4; // 游戏关闭
    public static final int STUCK_GAME = 5; // 游戏异常卡住

    public static final int CREATE_GAME = 1; // 创建游戏
    public static final int MANUALLY_CLOSE = 2; // 手动关闭
    public static final int TIME_OUT_CLOSE = 3; // 超时自动关闭

    public static final int GENERAL = 0; // 正常
    public static final int HOSTING = 1; // 托管
    public static final int EXIT = 2; // 退出
    public static final int VICTORY = 3; // 所有棋子已到达终点

    public static final int HOSTING_TIME = 10; // 超时托管秒数
    public static final int HOSTING_ACTION_TIME = 5; // 玩家托管时的等待时间

    public static final int THROW_DICE_AGAIN = 1; // 再掷骰子一次

    public static final int CLASSIC_MODE = 1; // 经典模式
    public static final int FAST_MODE = 2; // 快速模式

    public static final int THROW_DICE = 1; // 掷骰子
    public static final int CHESSMAN_MOVE = 2; // 棋子走动
    public static final int CHESSMAN_RETURN = 3; // 棋子被吃返回基地
    public static final int CHESSMAN_FINISH = 4; // 棋子到达终点
    public static final int DICE_SIX_THREE_TIMES = 5; // 连续投掷3次均为6点
    public static final int FIRST_OUT_BASE = 6; // 首次出基地

    public static final int BLUE_SIDE = 1; // 蓝色方
    public static final int RED_SIDE = 2; // 红色方
    public static final int GREEN_SIDE = 3; // 绿色方
    public static final int YELLOW_SIDE = 4; // 黄色方

    public static final int WATCH = 1; // 观看
    public static final int STOP_WATCH = 2; // 停止观看
}
