package com.quhong.exception;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONObject;
import com.quhong.core.config.ServerConfig;
import com.quhong.datas.HttpResult;
import com.quhong.enums.HttpCode;
import com.quhong.enums.SLangType;
import com.quhong.enums.ServerType;
import com.quhong.handler.HttpEnvData;
import com.quhong.handler.HttpPackData;
import com.quhong.intercepters.UserInterceptor;
import com.quhong.monitor.MonitorChecker;
import com.quhong.utils.AESUtils;
import com.quhong.utils.RequestUtils;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.slf4j.MDC;
import org.springframework.context.MessageSource;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.ControllerAdvice;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseBody;

import javax.annotation.Resource;
import javax.servlet.http.HttpServletRequest;
import java.util.Locale;

/**
 * 统一封装异常信息输出到前端
 */
@ControllerAdvice
@ResponseBody
public class CommonExceptionHandler {
    private final static Logger logger = LoggerFactory.getLogger(CommonExceptionHandler.class);
    protected static final Object EMPTY_OBJECT = new Object();

    @Resource
    private MessageSource message;
    private MonitorChecker monitorChecker;

    public CommonExceptionHandler() {
        this.monitorChecker = new MonitorChecker("ustar_java_exception");
    }


    private Locale getLocal(HttpServletRequest request) {
        String localeValue = (String) request.getAttribute(UserInterceptor.CLIENT_LANG);
        if (null == localeValue) {
            localeValue = "ar";
            logger.info("clientLang is null. path={}", request.getRequestURI());
        }
        return StringUtils.parseLocale(localeValue);
    }

    @ExceptionHandler(GameException.class)
    public String handleException(GameException e, HttpServletRequest request) {
        JSONObject paramObj = RequestUtils.getSendDataFromCache(request);
        HttpEnvData envData = null == paramObj ? new HttpEnvData() : HttpEnvData.create(paramObj);
        HttpResult result = new HttpResult();
        result.setCode(e.getHttpCode().getCode());
        result.setData(e.getData() == null ? EMPTY_OBJECT : e.getData());
        try {
            Locale locale = getLocal(request);
            result.setMsg(message.getMessage(e.getHttpCode().getMsg(), e.getArgs(), locale));
        } catch (Exception ex) {
            result.setMsg(e.getHttpCode().getMsg("ar".equals(getLocal(request).getLanguage()) ? SLangType.ARABIC : SLangType.ENGLISH));
        }
        String jsonString = JSON.toJSONString(result);
        boolean debug = RequestUtils.getParamInt(request, "debug") > 0;
        boolean test = !ServerType.PRODUCT.equals(ServerConfig.getServerType());
        if (debug && test) {
            return jsonString;
        }
        Object responseEncode = request.getAttribute("responseEncode");
        if (responseEncode != null) {
            int encode = (int) responseEncode;
            if (encode == 0) {
                return jsonString;
            }
        }
        return getEncryptData(result, RequestUtils.getParamInt(request, "new_versioncode"));
    }

    @ExceptionHandler(H5GameException.class)
    public String handleException(H5GameException e, HttpServletRequest request) {
        JSONObject paramObj = RequestUtils.getSendDataFromCache(request);
        HttpEnvData envData = null == paramObj ? new HttpEnvData() : HttpEnvData.create(paramObj);
        HttpResult result = new HttpResult();
        result.setCode(e.getHttpCode().getCode());
        result.setData(e.getData() == null ? EMPTY_OBJECT : e.getData());
        try {
            result.setMsg(message.getMessage(e.getHttpCode().getMsg(), e.getArgs(), getLocal(request)));
        } catch (Exception ex) {
            result.setMsg(e.getHttpCode().getMsg(envData.getSlang()));
//            result.setMsg(e.getHttpCode().getMsg("ar".equals(getLocal(request).getLanguage()) ? SLangType.ARABIC : SLangType.ENGLISH));
        }
        String jsonString = JSON.toJSONString(result);
        return jsonString;
    }

    @ExceptionHandler(Exception.class)
    public String warnSysException(Exception e, HttpServletRequest request) {
        String message = e.toString();
        String requestURI = request.getRequestURI();
        Object responseEncode = request.getAttribute("responseEncode");
        int encode = 0;
        if (responseEncode != null) {
            encode = (int) responseEncode;
        }
        if (ServerConfig.isProduct()) {
            String desc = "request: " + requestURI + ": throw exception msg = " + message + " [requestId]:" + MDC.get(RequestUtils.REQUEST_ID);
            String detail = "{req_data:%s}";
            JSONObject jsonObject = RequestUtils.parseSendData(request);
            detail = String.format(detail, null == jsonObject ? "" : jsonObject.toString());
            // 过滤ClientAbortException的告警
            if (message.contains("ClientAbortException") || message.contains("java.io.IOException") || message.contains("scan new error")) {
                logger.info("throw sys exception. request_uri={} response_encode={} msg={}", requestURI, encode, message);
            } else if (message.contains("org.apache.dubbo.rpc.RpcException")) {
                if (message.contains("Invoke remote method timeout")) {
                    monitorChecker.startWarning("Dubbo服务调用超时", "request: " + requestURI + " [requestId]:" + MDC.get(RequestUtils.REQUEST_ID));
                    logger.info("dubbo invoke remote method timeout. uri={}", requestURI);
                } else if (message.contains("No provider available")) {
                    monitorChecker.startWarning("Dubbo服务发现异常", "request: " + requestURI + " [requestId]:" + MDC.get(RequestUtils.REQUEST_ID));
                    logger.info("dubbo no provider available. uri={}", requestURI);
                } else {
                    monitorChecker.startWarning("Dubbo调用异常", "request: " + requestURI + " [requestId]:" + MDC.get(RequestUtils.REQUEST_ID));
                    logger.error("dubbo invoke remote method failure. uri={}", requestURI);
                }
            } else {
                monitorChecker.startWarning(desc, detail);
                logger.error("throw sys exception. request_uri={} response_encode={} {}", requestURI, encode, message, e);
            }
        } else {
            logger.error("throw sys exception. request_uri={} response_encode={} {}", requestURI, encode, message, e);
        }
        HttpResult<Object> result = new HttpResult<>();
        result.setCode(HttpCode.SERVER_ERROR.getCode());
        result.setMsg(HttpCode.SERVER_ERROR.getMsg());
        result.setData(new Object());
        if (!ServerType.PRODUCT.equals(ServerConfig.getServerType())
                && RequestUtils.getParamInt(request, "debug") > 0) {
            return JSON.toJSONString(result);
        }
        if (encode == 1) {
            return getEncryptData(result, RequestUtils.getParamInt(request, "new_versioncode"));
        }
        return JSON.toJSONString(result);
    }

    private String getEncryptData(Object result, int newVersionCode) {
        return JSON.toJSONString(new HttpPackData(AESUtils.encryptServerData(JSON.toJSONString(result), newVersionCode)));
    }
}
