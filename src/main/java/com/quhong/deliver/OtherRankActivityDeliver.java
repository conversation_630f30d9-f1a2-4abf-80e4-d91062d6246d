package com.quhong.deliver;

import com.mongodb.client.result.UpdateResult;
import com.quhong.constant.ActivityConstant;
import com.quhong.constant.ResourceConstant;
import com.quhong.core.config.ServerConfig;
import com.quhong.core.utils.DateHelper;
import com.quhong.data.ActorData;
import com.quhong.data.dto.MoneyDetailReq;
import com.quhong.enums.ResTypeEnum;
import com.quhong.image.ImageUrlGenerator;
import com.quhong.mongo.dao.ActorDao;
import com.quhong.mongo.dao.MongoRoomDao;
import com.quhong.mongo.dao.OtherRankingActivityDao;
import com.quhong.mongo.dao.ResourceKeyConfigDao;
import com.quhong.mongo.data.*;
import com.quhong.monitor.MonitorSender;
import com.quhong.mq.MqSenderService;
import com.quhong.msg.obj.RankInfoListObject;
import com.quhong.msg.obj.RankInfoObject;
import com.quhong.msg.obj.RidInfoObject;
import com.quhong.msg.room.RankNotificationPushMsg;
import com.quhong.redis.ActivityOtherRedis;
import com.quhong.room.RoomWebSender;
import com.quhong.room.TestRoomService;
import com.quhong.service.*;
import com.quhong.utils.RoomUtils;
import com.quhong.vo.GiftsMqVo;
import org.slf4j.Logger;
import org.slf4j.LoggerFactory;
import org.springframework.data.mongodb.core.query.Update;
import org.springframework.stereotype.Component;
import org.springframework.util.CollectionUtils;

import javax.annotation.Resource;
import java.util.*;

@Component
public class OtherRankActivityDeliver {

    private final static Logger logger = LoggerFactory.getLogger(OtherRankActivityDeliver.class);
    private final static String BLIND_BOX_PATTERN = "Blind Box";
    private final static String GIFT_WISH_ID = "6568562699943f47c12fcc46";
    // 64f5a152bc6d4174108fd3a6: 盲盒  6568562699943f47c12fcc46: 礼物心愿
    private final static List<String> UPDATE_ROUND_NUM_ACTIVITY_LIST = Arrays.asList("64f5a152bc6d4174108fd3a6");
    private final static Integer BLIND_BOX_AWARD_LIMIT = 20000;
    private final static Integer BLIND_BOX_ADD_TIME = 604800;
    private final static Integer CUPID_DAY_TIME;     // 1天时间

    static {
        if (ServerConfig.isProduct()) {
            CUPID_DAY_TIME = 86400;
        } else {
            CUPID_DAY_TIME = 3600;
        }
    }

    @Resource
    private TestRoomService testRoomService;
    @Resource
    private ActorDao actorDao;
    @Resource
    private MongoRoomDao mongoRoomDao;
    @Resource
    private MonitorSender monitorSender;
    @Resource
    private RoomWebSender roomWebSender;
    @Resource
    private MqSenderService mqSenderService;
    @Resource
    private OtherRankingActivityDao otherRankingActivityDao;
    @Resource
    protected ActivityOtherRedis activityOtherRedis;
    @Resource
    private ActivityUtilService activityUtilService;
    @Resource
    private ResourceKeyHandlerService resourceKeyHandlerService;
    @Resource
    private OperationRoomWashService operationRoomWashService;
    @Resource
    private MomentHotPostV2Service momentHotPostV2Service;
    @Resource
    private CarromMasterService carromMasterService;
    @Resource
    private SuperQueen2025Service superQueen2025Service;
    @Resource
    private GameEventOrganizerService gameEventOrganizerService;
    @Resource
    private GuardianService guardianService;
    @Resource
    private LudoMasterService ludoMasterService;
    @Resource
    private SummerPhotoContestService summerPhotoContestService;
    @Resource
    private MonsterCrushMasterService monsterCrushMasterService;
    @Resource
    private ScratchLotteryService scratchLotteryService;
    @Resource
    private GreedyElephantService greedyElephantService;
    @Resource
    private UnoMasterService unoMasterService;
    @Resource
    private YourCircleGloryService yourCircleGloryService;
    @Resource
    private GiftIllustrationBookService giftIllustrationBookService;
    @Resource
    private CrashExchangeShopService crashExchangeShopService;
    @Resource
    private MissContestV3Service missContestV3Service;
    @Resource
    private FootballCarnivalService footballCarnivalService;
    @Resource
    private GameCarnivalService gameCarnivalService;
    @Resource
    private PetFeedService petFeedService;
    /**
     * 配置类排行榜活动结算
     */
    public void deliver() {
        int nowSeconds = DateHelper.getNowSeconds();
        List<OtherRankingActivityData> initActivities = otherRankingActivityDao.getInitActivities();
        logger.info("otherRanking deliver check. initActivities={}", initActivities.size());
        for (OtherRankingActivityData activity : initActivities) {
            try {
                String activityId = activity.get_id().toString();

                // 更新心愿单每日配置
                // if(GIFT_WISH_ID.equals(activityId)){
                //     giftWishService.updateGiftWishDailyConfig(activity);
                // }

                // 活动已到期结束
                if (activity.getEndTime() < nowSeconds) {
                    logger.info("otherRanking activity deliver activityId={}", activity.get_id().toString());
                    UpdateResult updateResult = null;
                    if (UPDATE_ROUND_NUM_ACTIVITY_LIST.contains(activityId)) {
                        int nextRoundStartTime = activity.getStartTime() + BLIND_BOX_ADD_TIME;
                        int nextRoundEndTime = activity.getEndTime() + BLIND_BOX_ADD_TIME;
                        int nextRound = activity.getRoundNum() + 1;
                        Update update = new Update();
                        update.set("startTime", nextRoundStartTime);
                        update.set("endTime", nextRoundEndTime);
                        update.set("roundNum", nextRound);
                        updateResult = otherRankingActivityDao.updateData(activity, update);
                    } else if (MomentHotPostV2Service.ACTIVITY_ID.equals(activityId)) {
                        momentHotPostV2Service.handleMomentHotTopRankingSend(activity);
                        int nextRoundStartTime = activity.getStartTime() + BLIND_BOX_ADD_TIME;
                        int nextRoundEndTime = activity.getEndTime() + BLIND_BOX_ADD_TIME;
                        int nextRound = activity.getRoundNum() + 1;
                        Update update = new Update();
                        update.set("startTime", nextRoundStartTime);
                        update.set("endTime", nextRoundEndTime);
                        update.set("roundNum", nextRound);
                        updateResult = otherRankingActivityDao.updateData(activity, update);
                    } else {
                        updateResult = otherRankingActivityDao.updateData(activity, new Update().set("status", ActivityConstant.STATUS_DONE));
                    }

                    if (null == updateResult || updateResult.getModifiedCount() == 0) {
                        if (ServerConfig.isProduct()) {
                            monitorSender.info("activity", "活动结束异常", "活动更新失败");
                        }
                        continue;
                    }

                    RankNotificationPushMsg msg = new RankNotificationPushMsg();
                    msg.setTitle_en(activity.getAcNameEn());
                    msg.setTitle_ar(activity.getAcNameAr());
                    msg.setRankList(new ArrayList<>());
                    doSendReward(activity, msg);

                    if (OperationRoomWashService.ACTIVITY_ID.equals(activityId)) {
                        operationRoomWashService.distributionTotalRanking(activityId);
                    }

                    if (UnoMasterService.ACTIVITY_ID.equals(activityId)) {
                        unoMasterService.distributionRanking();
                    }

                    if (MonsterCrushMasterService.ACTIVITY_ID.equals(activityId)) {
                        monsterCrushMasterService.distributionRanking();
                    }

                    if (SuperQueen2025Service.ACTIVITY_ID.equals(activityId)) {
                        superQueen2025Service.distributionRanking();
                    }

                    if (SummerPhotoContestService.ACTIVITY_ID.equals(activityId)) {
                        summerPhotoContestService.distributionRanking();
                    }

                    if (CrashExchangeShopService.ACTIVITY_ID.equals(activityId)) {
                        crashExchangeShopService.distributionRanking(activityId);
                    }
                    if (LudoMasterService.ACTIVITY_ID.equals(activityId)) {
                        ludoMasterService.distributionRanking();
                    }

                    if (MissContestV3Service.QUEEN_ACTIVITY_ID_LIST.contains(activityId)) {
                        missContestV3Service.handleMissData(activity);
                    }
                    if (FootballCarnivalService.ACTIVITY_ID.equals(activityId)) {
                        footballCarnivalService.handleLastRoundData(activityId);
                    }
                    if (GameCarnivalService.ACTIVITY_ID.equals(activityId)) {
                        gameCarnivalService.distributionRanking();
                    }
                    if (PetFeedService.ACTIVITY_ID.equals(activityId)) {
                        petFeedService.distributionRanking(activityId);
                    }

                    if (ServerConfig.isProduct()) {
                        monitorSender.info("activity", "活动成功结束", "活动名：" + activity.getAcNameEn());
                    }

                    if (activity.getPopUp() == 1) {
                        // 活动结束消息
                        boolean test = activity.getAcNameEn().startsWith("test");
                        roomWebSender.sendRoomWebMsg(test ? testRoomService.getTestRoom() : "all", null, msg, false);
                    }
                }
            } catch (Exception e) {
                logger.error("otherRankingActivityDeliver error.", e);
                if (ServerConfig.isProduct()) {
                    monitorSender.info("activity", "冲榜活动结束异常", e.getMessage());
                }
            }
        }
    }

    private void doSendReward(OtherRankingActivityData activity, RankNotificationPushMsg msg) {
        List<OtherRankingActivityData.RankingConfig> rankingConfigList = activity.getRankingRewardList();
        String activityId = activity.get_id().toString();
        String acNameEn = activity.getAcNameEn();
        String acNameAr = activity.getAcNameAr();
        String acUrl = activity.getAcUrl();
        int roundNum = activity.getRoundNum();

        // 遍历每种奖励类型
        for (OtherRankingActivityData.RankingConfig rankingConfig : rankingConfigList) {
            // 奖励配置
            List<OtherRankingActivityData.RankingRewardConfig> rewardConfigList = rankingConfig.getRankingRewardConfigList();
            int rankType = rankingConfig.getRankType();
            List<String> rankingList = activityOtherRedis.getOtherRankingList(activityId, rankType, 10, roundNum);
            // 排行榜消息
            fillOtherRankMsg(rankingConfig, rankingList, msg);
            for (OtherRankingActivityData.RankingRewardConfig rankingRewardConfig : rewardConfigList) {
                // 奖励对象
                for (Integer rank : rankingRewardConfig.getRewardObject()) {
                    if (rankingList.size() < rank) {
                        logger.error("cannot find rank activityId={} rank={}", activity.get_id().toString(), rank);
                        continue;
                    }
                    String aid = rankingList.get(rank - 1);

                    if (ActivityConstant.ROOM_RANK == rankingConfig.getRankType()) {
                        aid = RoomUtils.getRoomHostId(aid);
                    }

                    int aidRankingScore = activityOtherRedis.getOtherRankingScore(activityId, aid, rankType, roundNum);
                    if (acNameEn.contains(BLIND_BOX_PATTERN) && aidRankingScore < BLIND_BOX_AWARD_LIMIT) {
                        continue;
                    }

                    // 可能有多个奖励
                    for (OtherRankingActivityData.RewardConfigDetail reward : rankingRewardConfig.getRewardConfigDetailList()) {
                        logger.info("ranking reward activityId={} uid={} rewardType={} sourceId={} rewardTime={} num={} rank={} rankType={}",
                                activity.get_id().toString(), aid, reward.getRewardType(), reward.getSourceId(), reward.getRewardTime(),
                                reward.getRewardNum(), rank, rankingConfig.getRankType());
                        if (ResourceConstant.DIAMOND.equals(reward.getRewardType())) {
                            doAsyncChargeDiamonds(activity, aid, reward.getRewardNum(), rank);
                        } else {
                            if (ResourceConstant.OTHER.equals(reward.getRewardType())) {
                                continue;
                            }
                            // 使用py下发资源
                            activityUtilService.handleResources(new GiftsMqVo(aid,
                                    reward.getRewardType(),
                                    reward.getSourceId(),
                                    reward.getRewardTime(),
                                    null == reward.getRewardNum() ? 1 : reward.getRewardNum()));

                        }
                    }

                    if (rankingRewardConfig.getResourceKey() != null) {
                        resourceKeyHandlerService.sendResourceData(aid, rankingRewardConfig.getResourceKey(), acNameEn, acNameAr, acNameEn, acUrl, "");
                    }

                }
            }
        }
    }


    /**
     * 排行列表消息
     */
    private void fillOtherRankMsg(OtherRankingActivityData.RankingConfig rankingConfig, List<String> rankingList, RankNotificationPushMsg msg) {
        // 排行列表消息
        RankInfoListObject rankInfoListObject = new RankInfoListObject();
        rankInfoListObject.setTagType(rankingConfig.getRankType());
        rankInfoListObject.setTitle_en(rankingConfig.getRankNameEn());
        rankInfoListObject.setTitle_ar(rankingConfig.getRankNameAr());
        List<RankInfoObject> rankInfoObjects = new ArrayList<>();
        rankInfoListObject.setRankInfo(rankInfoObjects);
        for (int i = 0; i < rankingList.size(); i++) {
            String aid = rankingList.get(i);
            RankInfoObject rankInfoObject = new RankInfoObject();
            rankInfoObject.setRank(i + 1);
            rankInfoObject.setTagType(rankingConfig.getRankType());
            if (ActivityConstant.ROOM_RANK == rankingConfig.getRankType()) {
                MongoRoomData roomData = mongoRoomDao.getDataFromCache(aid);
                rankInfoObject.setRoom_id(aid);
                rankInfoObject.setHead(ImageUrlGenerator.generateRoomUserUrl(roomData.getHead()));
                rankInfoObject.setName(roomData.getName());
                ActorData actorData = actorDao.getActorDataFromCache(RoomUtils.getRoomHostId(aid));
                rankInfoObject.setRid(actorData.getRid());
                rankInfoObject.setRidInfo(new RidInfoObject(actorData.getRidData()));
            } else {
                ActorData actorData = actorDao.getActorDataFromCache(aid);
                rankInfoObject.setRoom_id(aid);
                rankInfoObject.setHead(ImageUrlGenerator.generateRoomUserUrl(actorData.getHead()));
                rankInfoObject.setName(actorData.getName());
                rankInfoObject.setRid(actorData.getRid());
                rankInfoObject.setRidInfo(new RidInfoObject(actorData.getRidData()));
            }
            rankInfoObjects.add(rankInfoObject);
        }
        msg.getRankList().add(rankInfoListObject);
    }

    /**
     * 异步打钻
     */
    private void doAsyncChargeDiamonds(OtherRankingActivityData activity, String uid, int changed, int rank) {
        MoneyDetailReq moneyDetailReq = new MoneyDetailReq();
        moneyDetailReq.setRandomId();
        moneyDetailReq.setUid(uid);
        moneyDetailReq.setAtype(905);
        moneyDetailReq.setChanged(changed);
        moneyDetailReq.setTitle(activity.getAcNameEn() + " Competition Win");
        moneyDetailReq.setDesc(String.format("Top %d in the %s Competition Win", rank, activity.getAcNameEn()));
        mqSenderService.asyncChargeDiamonds(moneyDetailReq);
    }

    private void doAsyncChargeDiamonds(OtherRankingActivityData activity, String uid, int changed, String beanTitle, String beanDesc) {
        MoneyDetailReq moneyDetailReq = new MoneyDetailReq();
        moneyDetailReq.setRandomId();
        moneyDetailReq.setUid(uid);
        moneyDetailReq.setAtype(905);
        moneyDetailReq.setChanged(changed);
        moneyDetailReq.setTitle(beanTitle);
        moneyDetailReq.setDesc(beanDesc);
        mqSenderService.asyncChargeDiamonds(moneyDetailReq);
    }
}
