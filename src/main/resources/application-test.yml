baseUrl: /game/
server:
  port: 8080
spring:
  application:
    name: ustar-java-game
  messages:
    basename: i18n/game
    encoding: UTF-8
  cloud:
    kubernetes:
      discovery:
        catalog-services-watch:
          enabled: false
ribbon:
  Readtimeout: 15000
  ConnectTimeout: 15000
feign:
  hystrix:
    enabled: false
  client:
    config:
      default:
        connectTimeout: 15000
        readTimeout: 10000
hystrix:
  command:
    default:
      execution:
        timeout:
          enabled: false
        isolation:
          thread:
            timeoutInMilliseconds: 15000
dubbo:
  application:
    name: ${spring.application.name}
    qos-enable: false
    metadata-service-port: 20885
  registry:
    address: kubernetes://DEFAULT_MASTER_HOST?registry-type=service&duplicate=false&trustCerts=true&namespace=devops
  protocol:
    name: dubbo
    port: 20880
  metadata-report:
    report-metadata: false
  service:
    shutdown:
      wait: 5000

ludo:
  md5: bd6573dc2475808fa8c3e7a6494d1037
  url: https://cdn3.qmovies.tv/youstar/op_sys_1659335935_ludo.zip
  defaultCurrencyIndex: 1 # 游戏费用配置的默认下标值
  ruleUrl: https://static.youstar.live/ludo_rules/
  squareUrl: https://static.youstar.live/ludo_square/
  onlyRoomOwn: false # 仅房主、副房主、管理员可以创建游戏
  createGameLevelLimit: -1 # 大于等于这个等级才能创建游戏，-1不限制
  offline: false
baishunGame:
  iosAppId: 7633847994
  iosAppKey: 2c1UX2dzTd3e59anSaKWUzmegOx7Mmht
  androidAppId: 2135757682
  androidAppKey: Ra8sIfzxayQFM0GEMsrHQc4AnJe5cGH4
  channel: youstar
