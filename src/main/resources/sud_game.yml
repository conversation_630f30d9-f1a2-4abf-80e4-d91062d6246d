# 即构小游戏配置
config:
  appId: "1542135793495027713"
  appKey: "vUNf8LXvSOCZczyQV7QvWuRxw5kzi9oc"
  appSecret: "6TzbF8V2tZPe05o9G6LIMXkofv9KvFvw"
  getSudApiUrl: "https://sim-asc.sudden.ltd/"
  getSudApiUrlProd: "https://asc.sudden.ltd/"
  sudGameInfoMap: {
     2: {
           gameId: "1468180338417074177",
           name: "<PERSON><PERSON>", # Ludo
           nameAr: "لودو",
           onlineVersion: 833,
           iconUrl: "https://cdn3.qmovies.tv/youstar/op_1730452439_ludo_room2x.png",
           feeActType: 923,
           feeTitle: "Ludo Game Fee",
           feeDesc: "play ludo game fee",
           rewardActType: 924,
           rewardTitle: "Ludo Game Rewards",
           rewardDesc: "play ludo game rewards",
           feeReturnActType: 925,
           feeReturnTitle: "Return Ludo Game Fee",
           feeReturnDesc: "return ludo game fee",
           feeCreateActType: 960,
           feeCreateTitle: "Reroll Ludo Game Fee",
           feeCreateDesc: "reroll ludo game fee",
           coinFeeList: [500, 1000, 5000, 10000],
           gameRoomCoinFeeList: [500, 1000, 5000, 10000],
           diamondFeeList: [3, 10, 50, 100],
           currencyInfoMap: {
             10: {
               currencyId: 10,
               currencyType: 2,
               currencyValue: 3
             },
             1: {
               currencyId: 1,
               currencyType: 1,
               currencyValue: 100
             },
             11: {
               currencyId: 11,
               currencyType: 2,
               currencyValue: 10
             },
             12: {
               currencyId: 12,
               currencyType: 2,
               currencyValue: 20
             },
             13: {
               currencyId: 13,
               currencyType: 2,
               currencyValue: 50
             }
           }
     },
     3: {
           gameId: "1472142559912517633",
           name: "UNO", # UMO
           nameAr: "أومو",
           onlineVersion: 838,
           iconUrl: "https://cdn3.qmovies.tv/youstar/op_1730452439_uno_room2x.png",
           feeActType: 926,
           feeTitle: "UMO Game Fee",
           feeDesc: "play umo game fee",
           rewardActType: 927,
           rewardTitle: "UMO Game Rewards",
           rewardDesc: "play umo game rewards",
           feeReturnActType: 928,
           feeReturnTitle: "Return UMO Game Fee",
           feeReturnDesc: "return umo game fee",
           feeCreateActType: 961,
           feeCreateTitle: "Auto Umo Game Fee",
           feeCreateDesc: "auto Umo game fee",
           coinFeeList: [500, 1000, 5000, 10000],
           gameRoomCoinFeeList: [500, 1000, 5000, 10000],
           diamondFeeList: [3, 10, 50, 100],
           currencyInfoMap: {
             1: {
               currencyId: 1,
               currencyType: 2,
               currencyValue: 3
             },
             2: {
               currencyId: 2,
               currencyType: 1,
               currencyValue: 100
             },
             3: {
               currencyId: 3,
               currencyType: 2,
               currencyValue: 10
             },
             4: {
               currencyId: 4,
               currencyType: 2,
               currencyValue: 20
             },
             5: {
               currencyId: 5,
               currencyType: 2,
               currencyValue: 50
             }
           }
     },
     4: {
           gameId: "1664525565526667266",
           name: "Monster Crush", # 消消乐
           nameAr: "مونستر كراش",
           onlineVersion: 8471,
           iconUrl: "https://cdn3.qmovies.tv/youstar/op_sys_1693298431_monster_crush.png",
           feeActType: 941,
           feeTitle: "Monster Crush Game Fee",
           feeDesc: "play monster crush game fee",
           rewardActType: 942,
           rewardTitle: "Monster Crush Game Rewards",
           rewardDesc: "play monster crush game rewards",
           feeReturnActType: 943,
           feeReturnTitle: "Return Monster Crush Game Fee",
           feeReturnDesc: "return monster crush game fee",
           coinFeeList: [500, 1000, 5000, 10000],
           gameRoomCoinFeeList: [500, 1000, 5000, 10000],
           diamondFeeList: [3, 10, 50, 100],
           currencyInfoMap: {
             10: {
               currencyId: 10,
               currencyType: 2,
               currencyValue: 3
             },
             1: {
               currencyId: 1,
               currencyType: 1,
               currencyValue: 100
             },
             11: {
               currencyId: 11,
               currencyType: 2,
               currencyValue: 10
             },
             12: {
               currencyId: 12,
               currencyType: 2,
               currencyValue: 20
             },
             13: {
               currencyId: 13,
               currencyType: 2,
               currencyValue: 50
             }
          }
     },
     5: {
       gameId: "1537330258004504578",
       name: "Domino", # 多米诺骨牌
       nameAr: "دومينو",
       onlineVersion: 853,
       iconUrl: "https://cdn3.qmovies.tv/youstar/op_1730452439_domino_room2x.png",
       feeActType: 948,
       feeTitle: "Play Domino game fee",
       feeDesc: "Play Domino game fee",
       rewardActType: 949,
       rewardTitle: "Play Domino game reward",
       rewardDesc: "Play Domino game reward",
       feeReturnActType: 950,
       feeReturnTitle: "Return Domino game fee",
       feeReturnDesc: "Return Domino game fee",
       coinFeeList: [500, 1000, 5000, 10000 ],
       gameRoomCoinFeeList: [500, 1000, 5000, 10000],
       diamondFeeList: [ 3, 10, 50, 100 ]
     },
     6: {
       gameId: "1777154372100497410",
       name: "Carrom Pool",
       nameAr: "بركة كيرم",
       onlineVersion: 8574,
       iconUrl: "https://cdn3.qmovies.tv/youstar/op_1722319109_carrom.png",
       feeActType: 968,
       feeTitle: "Play Carrom Pool game fee",
       feeDesc: "Play Carrom Pool game fee",
       rewardActType: 969,
       rewardTitle: "Play Carrom Pool game reward",
       rewardDesc: "Play Carrom Pool game reward",
       feeReturnActType: 978,
       feeReturnTitle: "Return Carrom Pool game fee",
       feeReturnDesc: "Return Carrom Pool game fee",
       coinFeeList: [500, 1000, 5000, 10000 ],
       gameRoomCoinFeeList: [500, 1000, 5000, 10000],
       diamondFeeList: [ 3, 10, 50, 100 ]
     },
     7: {
       gameId: "1739914495960793090",
       name: "Billiard (8 Ball)",
       nameAr: "البلياردو (8 كرات)",
       onlineVersion: 8592,
       iconUrl: "https://cdn3.qmovies.tv/youstar/op_1732159371_billiardV1.png",
       feeActType: 1000,
       feeTitle: "Play Billiard (8 Ball) game fee",
       feeDesc: "Play Billiard (8 Ball) game fee",
       rewardActType: 1001,
       rewardTitle: "Play Billiard (8 Ball) game reward",
       rewardDesc: "Play Billiard (8 Ball) game reward",
       feeReturnActType: 1002,
       feeReturnTitle: "Return Billiard (8 Ball) game fee",
       feeReturnDesc: "Return Billiard (8 Ball) game fee",
       coinFeeList: [ 500, 1000, 5000, 10000 ],
       gameRoomCoinFeeList: [ 500, 1000, 5000, 10000 ],
       diamondFeeList: [ 3, 10, 50, 100 ]
     },
     8: {
       gameId: "1848188045807976450",
       name: "Jackaroo",
       nameAr: "جاكارو",
       onlineVersion: 8592,
       iconUrl: "https://cdn3.qmovies.tv/youstar/op_1733378618_jackaroo_gongping.png",
       feeActType: 1003,
       feeTitle: "Play Jackaroo game fee",
       feeDesc: "Play Jackaroo game fee",
       rewardActType: 1004,
       rewardTitle: "Play Jackaroo game reward",
       rewardDesc: "Play Jackaroo game reward",
       feeReturnActType: 1005,
       feeReturnTitle: "Return Jackaroo game fee",
       feeReturnDesc: "Return Jackaroo game fee",
       coinFeeList: [ 500, 1000, 5000, 10000 ],
       gameRoomCoinFeeList: [ 500, 1000, 5000, 10000 ],
       diamondFeeList: [ 3, 10, 50, 100 ]
     },
     9: {
       gameId: "1759471374694019074",
       name: "Baloot",
       nameAr: "بلوت",
       onlineVersion: 8592,
       iconUrl: "https://cdn3.qmovies.tv/youstar/op_1733378898_baloot-gongpong.png",
       feeActType: 1006,
       feeTitle: "Play Baloot game fee",
       feeDesc: "Play Baloot game fee",
       rewardActType: 1007,
       rewardTitle: "Play Baloot game reward",
       rewardDesc: "Play Baloot game reward",
       feeReturnActType: 1008,
       feeReturnTitle: "Return Baloot game fee",
       feeReturnDesc: "Return Baloot game fee",
       coinFeeList: [ 500, 1000, 5000, 10000 ],
       gameRoomCoinFeeList: [ 500, 1000, 5000, 10000 ],
       diamondFeeList: [ 3, 10, 50, 100 ]
     },
     10: {
       gameId: "1599672757949743105",
       name: "Who is the spy",
       nameAr: "من هو الجاسوس",
       onlineVersion: 865,
       iconUrl: "https://cdn3.qmovies.tv/youstar/op_1751963901_who_is_the_spy.png",
       feeActType: 1009,
       feeTitle: "Play who is the spy game fee",
       feeDesc: "Play who is the spy game fee",
       rewardActType: 1010,
       rewardTitle: "Play who is the spy game reward",
       rewardDesc: "Play who is the spy game reward",
       feeReturnActType: 1011,
       feeReturnTitle: "Return who is the spy game fee",
       feeReturnDesc: "Return who is the spy game fee",
       coinFeeList: [ 10, 50, 100, 200 ],
       gameRoomCoinFeeList: [ 500, 1000, 5000, 10000 ],
       diamondFeeList: [ 3, 10, 50, 100 ]
     }
  }

  ludoCurrencyInfoMap: {
    1: {
      currencyId: 1,
      currencyType: 1,
      currencyValue: 100
    },
    2: {
      currencyId: 2,
      currencyType: 1,
      currencyValue: 200
    },
    3: {
      currencyId: 3,
      currencyType: 1,
      currencyValue: 500
    },
    4: {
      currencyId: 4,
      currencyType: 1,
      currencyValue: 1000
    },
    5: {
      currencyId: 5,
      currencyType: 1,
      currencyValue: 2000
    },
    6: {
      currencyId: 6,
      currencyType: 1,
      currencyValue: 5000
    },
    7: {
      currencyId: 7,
      currencyType: 1,
      currencyValue: 10000
    },
    8: {
      currencyId: 8,
      currencyType: 1,
      currencyValue: 20000
    }
  }
