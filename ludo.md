

### 一 网桥调用和接收：

向服务器发送请求，通过sendRequest方法，发送给客户端。然后客户单再以http的形式发送给服务器。

```javascript
      let u = navigator.userAgent;
      let isAndroid = u.indexOf("Android") > -1 || u.indexOf("Adr") > -1; //判断是否是 android终端
      let isIOS = !!u.match(/\(i[^;]+;( U;)? CPU.+Mac OS X/); //判断是否是 iOS终端
      const vm = this;
      if (isAndroid) {
        this.$bridge.callhandler("sendRequest", {}, (data) => {
          // 处理返回数据
        });
      } else {
        this.$bridge.callhandler("sendRequest", {}, (data) => {
          // 处理返回数据
        });
      }
```



服务器主动推送的消息，会先到客户端，再由客户端调用js方法。

```javascript
    const vm = this;
    this.$bridge.registerhandler("onReceiveMsg", (data, responseCallback) => {
  		//处理返回数据
    });
```

### 二 消息推送

ludo游戏采用json格式，全量推送。h5端需要处理重复消息。以下为推送协议具体内容。其中opData为当前操作的用户信息。playDataList为上一步执行后，h5端需要做的表现。

GameInfo

| 属性          | 类型       | 说明                                   |
| ------------- | ---------- | -------------------------------------- |
| gameType      | number     | 游戏类型 1 经典 2 快速                 |
| totalCurrency | number     | 总游戏币                               |
| currencyType  | number     | 游戏币类型 1 心心, 2 钻石              |
| playerList    | ArrayList  | 玩家数组，GamePlayer数组               |
| boardNodeList | ArrayList  | 棋盘，NodeData数组                     |
| step          | number     | 总步数                                 |
| status        | number     | 游戏状态 0 初始状态, 1 进行中, 2 结束  |
| winnerData    | WinnerData | 游戏结束数据，只有当status=2时存在     |
| selfUid       | string     | 自己uid                                |
| opData        | OpData     | 操作对象                               |
| playDataList  | ArrayList  | 播放对象数组，PlayData数组，按顺序播放 |

GamePlayer

| 属性           | 类型      | 说明                                     |
| -------------- | --------- | ---------------------------------------- |
| uid            | string    | 用户id                                   |
| rid            | number    |                                          |
| name           | string    | 用户昵称                                 |
| head           | string    | 用户头像链接                             |
| hosting        | number    | 是否托管 0 正常, 1 托管                  |
| side           | number    | 方位 0 白色 1, 蓝 2 红 3 绿 4 黄         |
| playerNodeList | ArrayList | 玩家自身格子列表，NodeData数组           |
| diceList       | ArrayList | 整数数组，用户已经掷出，还没用的骰子数组 |

NodeData

| 属性         | 类型      | 说明                                 |
| ------------ | --------- | ------------------------------------ |
| id           | number    | 节点序号                             |
| type         | number    | 0 普通 1 保护格 2 最后6个格子 3 终点 |
| side         | number    | 方位 0 白色 1, 蓝 2 红 3 绿 4 黄     |
| chessmanList | ArrayList | 该格子上的数组，Chessman数组         |

Chessman

| 属性        | 类型   | 说明                             |
| ----------- | ------ | -------------------------------- |
| id          | string |                                  |
| nodeId      | number | 当前所在格子id                   |
| uid         | string | 所属用户id                       |
| side        | number | 方位 0 白色 1, 蓝 2 红 3 绿 4 黄 |
| startNodeId | number | 起始格子                         |

OpData

| 属性   | 类型   | 说明                         |
| ------ | ------ | ---------------------------- |
| opUid  | string | 操作者uid                    |
| opType | number | 操作类型 1 掷骰子 2 棋子移动 |

PlayData

| 属性          | 类型      | 说明                                         |
| ------------- | --------- | -------------------------------------------- |
| opUid         | string    | 操作者uid                                    |
| opType        | number    | 操作类型 1 掷骰子 2 棋子移动                 |
| diceValueList | ArrayList | opType=1 骰子数值列表 整数列表               |
| chessmanId    | string    | opType=2 棋子起始节点                        |
| toNodeId      | number    | opType=2 棋子移动目标节点, -1 移动到起始位置 |

WinnerData

| 属性       | 类型      | 说明                            |
| ---------- | --------- | ------------------------------- |
| playerList | ArrayList | 获胜者用户列表 WinnerPlayer数组 |

WinnerPlayer

| 属性        | 类型   | 说明                         |
| ----------- | ------ | ---------------------------- |
| uid         | string | 获胜者uid                    |
| rewardValue | number | 操作类型 1 掷骰子 2 棋子移动 |
| rewardType  | number | 奖励类型 1 心心, 2 钻石      |

### 三 操作请求

请求数据

| 属性          | 类型      | 说明                               |
| ------------- | --------- | ---------------------------------- |
| uid           | string    | 操作者uid                          |
| opType        | number    | 操作类型 1 掷骰子 2 棋子移动       |
| chessmanId    | string    | opType=2 棋子id                    |
| diceValueList | ArrayList | opType=2 对应骰子数值列表 整数列表 |

返回数据

| 属性 | 类型     | 说明                     |
| ---- | -------- | ------------------------ |
| code | number   | 结果码 0 成功,其他为错误 |
| msg  | string   | 结果描述 0时为空字符串   |
| data | GameInfo | 游戏数据                 |

